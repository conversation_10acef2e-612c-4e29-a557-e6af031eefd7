<?xml version="1.0" encoding="utf-8" ?>
<truEngine-document xmlns:te="http://www.truengine.sk">

<?php 

	if(!isset(@{html_id})) {
		@{html_id} = 'default';
	}

	if(!isset(@{onsubmit})) {
		@{onsubmit} = '';
	}

	if(!isset(@{filter_settings})) {
		@{filter:settings} = htmlspecialchars_decode(@{request:filter});
	}
	else {
		@{filter:settings} = htmlspecialchars_decode(@{filter_settings});
	}
 
	@{filter:sort} = htmlspecialchars_decode(@{request:zoradit});
	@{filter:params} = '';

	if(!empty(@{filter:settings})) {
		@{filter:params} .= '&filter=' . urlencode(@{filter:settings});
	}
	if(!empty(@{filter:sort})) {
		@{filter:params} .= '&zoradit=' . urlencode(@{filter:sort});
	}

	if(!isset(@{filter:default})) {
		@{filter:default} = '';
	}


	if(empty(@{filter:settings})) {
		@{filter_id} = '_empty';
	}
	else {
		@{filter_id} = '';
	}

	if(!isset(@{read_only}) || @{read_only} !== 'true') {
		@{read_only} = false;
	}
	else {
		@{read_only} = true;
	}

	@{restore_filter} = session_gClass::getSessionSettingsDetail_gFunc('filter-@{doc}');
	if(!@{restore_filter}) {
		@{restore_filter} = array();
	}

	if(@{request:filter} === NULL && @{request:zoradit} === NULL) {
		if(@{restore_filter} && isset(@{restore_filter}['_default'])) {
			$tmp = @{filter:default}; 
			@{filter} = @{restore_filter}['_default'];
			@{filter:default} = $tmp;
		}
	}

	if(@{request:save-filter} == 1) {
		@{restore_filter}[@{request:filter-name}] = @{filter};
		session_gClass::saveSessionSettingsDetail_gFunc('filter-@{doc}', @{restore_filter});
	}

	@{restore_filter}['_default'] = @{filter};
	session_gClass::saveSessionSettingsDetail_gFunc('filter-@{doc}', @{restore_filter});

	if(!empty(@{filter:default})) {
		parse_str(@{filter:settings}, $tmp1);
		parse_str(@{filter:default}, $tmp2);
		foreach($tmp2 as $k=>$v) {
			if(!isset($tmp1[$k])) {
				$tmp1[$k]=$v;
			}
		}
		$tmpx = '';
		foreach($tmp1 as $k=>$v)
		{
			$tmpx .= '&';
			$tmpx .= $k . '=' . $v;
		}
		@{filter:settings} = $tmpx;
	}

?>

<te:source name="searchFilter" varname="data" items="@{item_type}" request="@{filter:settings}" html_id="@{html_id}" />

<div id="filter_container@{filter_id}@{html_id}" style="display:inline" te:print="no">
	<a href="#" onclick="filter_show_hide_settings('@{html_id}'); return(false);" te:if="@{read_only} !== true"><te:string id="Filter" />:</a>
	
	<te:for each="@{restore_filter} as @{filter-name}=>@{named-filter}">
		<te:if test="@{filter-name} != '_default'">
			<a href="@{doc}?named-filter=1@{named-filter:params}">@{filter-name}</a>
		</te:if>
	</te:for>
	
	
	<te:if test="@{read_only} !== true">
		<div id="filter@{html_id}" style="display:inline"></div>
		<te:else />
		<div id="filter@{html_id}" style="display:none"></div>
	</te:if>
	<br />

	<div id="filter_settings@{html_id}" style="display:none;"></div>

		<te:if test="@{read_only} === true || !empty(@{onsubmit})">
				<input type="hidden" name="filter" id="filter_submit@{html_id}" value="@{filter:settings}" />
				<input type="hidden" name="zoradit" id="filter_sort@{html_id}" value="@{filter:sort}" />
				<te:if test="!empty(@{onsubmit})">
					<input type="button" value="@L{Filtrovať}" onclick="filter_set_request('@{html_id}'); @{onsubmit}" />
				</te:if>
			<te:else />
				<form method="get" onsubmit="filter_set_request('@{html_id}');" action="@{doc}" style="display:inline">
					<input type="hidden" name="filter" id="filter_submit@{html_id}" value="@{filter:settings}" />
					<input type="hidden" name="zoradit" id="filter_sort@{html_id}" value="@{filter:sort}" />
					<input type="submit" value="@L{Filtrovať}" />
					<input type="button" value="@L{Uložiť filter}" onclick="$j('#filter-save-code').css('display', 'inline').replaceAll(this); return(false);" />
				</form>
		</te:if>
		<div id="filter-save-code" style="display:none;">@L{Názov filtra}: <input type="text" name="filter-name" value="" /><input type="hidden" name="save-filter" id="save-filter" value="0" /><input type="submit" value="@L{Uložiť a filtrovať}" onclick="$j('#save-filter').val(1);" /></div>


	<script type="text/javascript">
	newFilter('@{html_id}');
	
	@{data:js}
	</script>
</div>

<te:if test="isset(@{last_name_index})">
	<div style="font-size:12px; padding:3px;" te:print="no">
		 <?php
		 	for($i_pVar = ord('A'); $i_pVar <= ord('Z'); $i_pVar++) {
			echo '<a href="#" onclick="sL(\'' . strtolower(chr($i_pVar)) . '\'); return(false);">' . chr($i_pVar) . '</a> ';
		} 
	 ?>
		 <script type="text/javascript">
		 function sL(value)
		 {		
			var url = '<?php echo main_gClass::makeUrl_gFunc(@{doc}); ?>';
			
			if(url.indexOf('?') >= 0) {
				url += '&amp;';
			}
			else {
				url += '?';
			}
		
			x = document.getElementById('filter_sort@{html_id}' );
			if(x) {
				url += 'zoradit=' + x.value;
			}
			if(filters['@{html_id}'].filter_params) {
				url += '&amp;filter=' + escape(filters['@{html_id}'].filter_params + '&amp;last_name=LIKE(' + value + '%)');
			}
			else {
				url += '&amp;filter=' + escape('&amp;last_name=LIKE(' + value + '%)');
			}
			
			
			document.location = url;
		 }
		 </script>
	</div>
</te:if>
&amp;nbsp;&amp;nbsp;
</truEngine-document>
