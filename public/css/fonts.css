@charset "iso-8859-1";

/*******************************************************************************
*  fonts.css : 2004-06-28 : Ruthsar<PERSON> Layouts
* ------------------------------------------------------------------------------
*  handles the font type (family), sizes, line-height, alignment, and any other
*  properties relating to fonts - with the exception of color.
*
*  Perhaps font color declarations should be in this file as well. 
*  However, certain CSS validators (hello w3!) will complain if colors
*  and background colors are not declared in the same selector or
*  at least within the same stylesheet. So font colors, including links,
*  are defined in colors.css.
*******************************************************************************/

body
{
	font-family: arial, helvetica, sans-serif;
	font-size: 100.1%;	/* font sizing approach recommended by Dave 
				   Silvester on the css-discuss list. "the 
				   .1 is important." */
}
#pageWrapper
{
	font-size: 80%;
}
#footer
{
	text-align: center;
}
/* :root is an invalid pseudo class but mozilla knows it */
:root code
{
	font-size: 120%;	/* monospace fonts are typically smaller that 
				   'normal' fonts under Mozilla. */
}

/*
	Time to try and get all the heading elements to the same size 
	regardless of browser. (IE seems to put them a big bigger
	than Mozilla, typically.)
*/
h1
{
	font-size: 210%;
}
h2
{
	font-size: 180%;
}
h3
{
	font-size: 140%;
}
h4
{
	font-size: 126%;
}
h5
{
	font-size: 110%;
}
h6
{
	font-size: 100%;
}

/******************************************************************************/