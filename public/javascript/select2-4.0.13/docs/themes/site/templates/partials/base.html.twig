{% set theme_config = attribute(config.themes, config.system.pages.theme) %}
<!DOCTYPE html>
<html lang="{{ grav.language.getLanguage ?: 'en' }}">
<head>
    {% block head %}
    <meta charset="utf-8" />
    <title>{% if header.title %}{{ header.title }} | {% endif %}{{ site.title }}</title>
    {% include 'partials/metadata.html.twig' %}
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no, shrink-to-fit=no" />
    <link rel="alternate" type="application/atom+xml" href="{{ base_url_absolute}}/feed:atom" title="Atom Feed" />
    <link rel="alternate" type="application/rss+xml" href="{{ base_url_absolute}}/feed:rss" title="RSS Feed" />
    <link rel="icon" type="image/png" href="{{ url('theme://images/favicon.png') }}">

    {% block stylesheets %}
        {% do assets.addCss('theme://css-compiled/nucleus.css',102) %}
        {% do assets.addCss('theme://css-compiled/theme.css',101) %}
        {% do assets.addCss('theme://css/custom.css',100) %}
        {% do assets.addCss('theme://css/font-awesome.min.css',100) %}
        {% do assets.addCss('theme://css/featherlight.min.css') %}
        {% do assets.addCss('https://cdn.jsdelivr.net/npm/select2@4.0.13/dist/css/select2.min.css') %}
        {% do assets.addCss('theme://css/s2-docs.css', 100) %}
        {% do assets.addCss('theme://css/theme.css',100) %}

        {% if browser.getBrowser == 'msie' and browser.getVersion >= 8 and browser.getVersion <= 9 %}
            {% do assets.addCss('theme://css/nucleus-ie9.css') %}
            {% do assets.addCss('theme://css/pure-0.5.0/grids-min.css') %}
            {% do assets.addJs('theme://js/html5shiv-printshiv.min.js') %}
        {% endif %}

        {{ assets.css() }}
    {% endblock %}

    {% block javascripts %}
        {% do assets.addJs('jquery',101) %}
        {% do assets.addJs('theme://js/modernizr.custom.71422.js',100) %}
        {% do assets.addJs('https://cdn.jsdelivr.net/npm/select2@4.0.13/dist/js/select2.full.min.js', 100) %}
        {% do assets.addJs('https://cdn.rawgit.com/google/code-prettify/master/loader/run_prettify.js', 100) %}
        {% do assets.addJs('theme://js/featherlight.min.js') %}
        {% do assets.addJs('theme://js/clipboard.min.js') %}
        {% do assets.addJs('theme://js/jquery.scrollbar.min.js') %}
        {% do assets.addJs('theme://js/learn.js') %}
        {% do assets.addJs('theme://js/data-fill-from.js') %}
        {{ assets.js() }}

        {% include "partials/js/source-states.html.twig" %}

    {% endblock %}

    {% endblock %}
</head>
<body class="searchbox-hidden {{ page.header.body_classes }}" data-url="{{ page.route }}">
    {% block sidebar %}
    <nav id="sidebar">
        <div id="header-wrapper">
            <div id="header">
                <a id="logo" href="{{ theme_config.home_url ?: base_url_absolute }}">{% include 'partials/logo.html.twig' %}</a>
                <div class="searchbox">
                    <label for="search-by"><i class="fa fa-search"></i></label>
                    <input id="search-by" type="text" placeholder="{{ 'THEME_LEARN2_SEARCH_DOCUMENTATION'|t }}"
                           data-search-input="{{ base_url_relative }}/search.json/query"/>
                    <span data-search-clear><i class="fa fa-close"></i></span>
                </div>
            </div>
        </div>
        {% include 'partials/sidebar.html.twig' %}
    </nav>
    {% endblock %}

    {% block body %}
    <section id="body">
        <div id="overlay"></div>

        <div class="padding highlightable">
            <a href="#" id="sidebar-toggle" data-sidebar-toggle><i class="fa fa-2x fa-bars"></i></a>

            {% block topbar %}{% if theme_config.github.position == 'top' or config.plugins.breadcrumbs.enabled %}
            <div id="top-bar">
                {% if theme_config.github.position == 'top' %}
                <div id="top-github-link">
                {% include 'partials/github_link.html.twig' %}
                </div>
                {% endif %}

                {% if config.plugins.breadcrumbs.enabled %}
                {% include 'partials/breadcrumbs.html.twig' %}
                {% endif %}
            </div>
            {% endif %}{% endblock %}

            {% block content %}{% endblock %}

            {% block footer %}
                {% if theme_config.github.position == 'bottom' %}
                {% include 'partials/github_note.html.twig' %}
                {% endif %}
            {% endblock %}

        </div>
        {% block navigation %}{% endblock %}
    </section>
    {% endblock %}

    {% block analytics %}
        {% if theme_config.google_analytics_code %}
        {% include 'partials/analytics.html.twig' %}
        {% endif %}
    {% endblock %}
 </body>
</html>
