<?php

class delete_cache extends source_gClass
{
    function getData()
    {
        $cacheDir = main_gClass::getPathForCache_gFunc();

        $d = dir($cacheDir);
        while (false !== ($entry = $d->read())) {
            if($entry == '.' || $entry == '..' || $entry == '.svn') {
                continue;
            }
            unlink($cacheDir . $entry);
            echo '<b>deleted</b> ' . $entry."<br />";
        }
        $d->close();

        return(array());
    }
}
