
pri prvom vytvarani instancie musi dat parameter basedir (to sa deje v index.php a v artisan). Inak je exception. 

musim vytvorit config/mixedtype/tracker.php - bez tohto suboru je trackovanie vypnute.

groups.app.enabled musi byt true, inak je cele trackovanie disabled.
groups.app.write musi byt true, inak sa data nikam nezapisuju (ale moze ich vyuzit na debug pocas requestu)
ak groups.app.write===true, tak musi byt definovane groups.app.file_storage_path inak to nebude fungovat, exception...

groups.app.ignore_requests mozem nastavit url ktore nechcem trackovat. Musi matchnut zaciatok url

kazda group moze mat
- enabled - true/false - ak je false, tak sa data netrackuju
- write - true/false - ak je false, tak sa data nezapisuju
ak nie je hodnota definovana, tak je default true. (ale app je nadradena a ak je app.write=false, tak sa nezapisuju data ani z inych group)

pre group mozem definovat storage file/db default je file. Pre app a exceptions je storage vzdy file a neda sa zmenit.

exceptiony sa za zapisuju do groupy exceptions.



TODO: pre trackovanie databazy mam zatial v AppServiceProvider.php pridany kod.... toto by sa malo presunut do trackeru






