<?php
if(isset($_SERVER['SCRIPT_FILENAME']) && strtolower($_SERVER['SCRIPT_FILENAME']) == strtolower(str_replace("\\",'/',__FILE__))) { header("HTTP/1.0 404 Not Found");  return(false); }
if(isset($GLOBALS['file__currency_pVar'])) return(true);
$GLOBALS['file__currency_pVar']=true;


class priceFormat_gClass {
	static private $configLoaded_pVar = false;
	static private $main_currency_pVar;
	static private $main_currency_short_pVar;
	static private $euro2008_enabled_pVar;
	static private $euro2008_phase_pVar;
	static private $euro2008_exchangeRate_pVar;
	static private $euro2008_currency_pVar = false;
	static private $rates_pVar;

	/**
	 * EURO-2008
	 * Podla konfiguracie nastavi $euro2008_enabled_pVar, $euro2008_phase_pVar a $euro2008_exchangeRate_pVar
	 */
	private static function loadConf_gFunc()
	{
		if(self::$configLoaded_pVar) {
			return;
		}
		self::$configLoaded_pVar = true;
		self::$main_currency_pVar = main_gClass::getMainCurrency_gFunc();
		self::$main_currency_short_pVar = main_gClass::getMainCurrency_gFunc(true);

		// EXCHANGE RATES
		$exchange_rates_pVar = main_gClass::getConfigSectionVar_gFunc('exchange_rates');
		self::$rates_pVar = array();
		foreach($exchange_rates_pVar as $k_pVar=>$v_pVar) {
			$k_pVar = strtoupper($k_pVar);
			$tmp_pVar = explode('/', $v_pVar);
			$tmp2_pVar = array();
			$tmp2_pVar['rate_pVar'] = $tmp_pVar[0];
			$tmp2_pVar['short_pVar'] = $tmp_pVar[1];
			$tmp2_pVar['currency_pVar'] = $k_pVar;
			self::$rates_pVar[$k_pVar] = $tmp2_pVar;
		}
		// este do kurzov doplnim hlavnu menu.. kurz je 1
		self::$rates_pVar[self::$main_currency_pVar] = array('rate_pVar'=>1, 'short_pVar'=>self::$main_currency_short_pVar, 'currency_pVar'=>self::$main_currency_pVar);

		//EURO 2008
		self::$euro2008_enabled_pVar = main_gClass::getConfigVar_gFunc('enabled', 'euro2008');
		if(!self::$euro2008_enabled_pVar) {
			self::$euro2008_enabled_pVar = false;
			// ak nie je enabled, nastavim defaultnu fazu podla zakladnej meny a datumu
			self::$euro2008_phase_pVar = self::$main_currency_pVar;
			if(self::$euro2008_phase_pVar !== 'EUR' && self::$euro2008_phase_pVar !== 'SKK') {
				if((int)date('Y') >= 2009) {
					self::$euro2008_phase_pVar = 'EUR';
				}
				else {
					self::$euro2008_phase_pVar = 'SKK';
				}
			}
		}
		if(self::$euro2008_enabled_pVar) {
			self::$euro2008_phase_pVar = 'SKK';
			$date_pVar = main_gClass::getConfigVar_gFunc('phase_skkeur', 'euro2008');
			if(time() > strtotime($date_pVar)) {
				self::$euro2008_phase_pVar = 'SKKEUR';
			}
			$date_pVar = main_gClass::getConfigVar_gFunc('phase_eurskk', 'euro2008');
			if(time() > strtotime($date_pVar)) {
				self::$euro2008_phase_pVar = 'EURSKK';
			}
			$date_pVar = main_gClass::getConfigVar_gFunc('phase_eur', 'euro2008');
			if(time() > strtotime($date_pVar)) {
				self::$euro2008_phase_pVar = 'EUR';
			}
		}
		self::$euro2008_exchangeRate_pVar = main_gClass::getConfigVar_gFunc('exchange_rate', 'euro2008');
	}

	/**
	 * Vrati pole s dvoma alebo jednou hodnotou podla aktualnej fazy.
	 * 0 => primarna mena
	 * 1 => sekundarna mena
	 */
	static private function getEuro2008Currency_gFunc()
	{
		if(is_array(self::$euro2008_currency_pVar)) {
			return(self::$euro2008_currency_pVar);
		}

		self::loadConf_gFunc();

		switch (self::$euro2008_phase_pVar)
		{
			case 'SKK':
				self::$euro2008_currency_pVar = array();
				break;
			case 'SKKEUR':
				self::$euro2008_currency_pVar = array('SKK', 'EUR');
				break;
			case 'EURSKK':
				self::$euro2008_currency_pVar = array('EUR', 'SKK');
				break;
			case 'EUR':
				self::$euro2008_currency_pVar = array('EUR');
				break;
		}

		return(self::$euro2008_currency_pVar);
	}

	/**
	 * Naformatovanie ceny + podpora pre EURO 2008
	 *
	 *
	 *
	 * @param unknown_type $value_pVar = ciselna hodnota
	 * @param unknown_type $inCurrency_pVar = mena v ktorej je vyjadrena ciselna hodnota. Ak je false, pouzije sa hlavna mena systemu.
	 * @param unknown_type $outCurrency_pVar = mena v ktorej ma byt cena zobrazena. Ak je false, pouzije sa hlavna mena systemu.
	 * @param unknown_type $extraParams_pVar = rozne parametre v poli. euro2008=>format dualneho zobrazovania pri prechode na euro
	 * @return unknown
	 */
	public static function formatPriceEx_gFunc($value_pVar, $inCurrency_pVar=false, $outCurrency_pVar=false, $extraParams_pVar = array())
	{
		self::loadConf_gFunc();

		$value_pVar = (float)$value_pVar;

		if($inCurrency_pVar === false) {
			$inCurrency_pVar = self::$main_currency_pVar;
		}
		if($outCurrency_pVar === false) {
			$outCurrency_pVar = self::$main_currency_pVar;
		}
		$inCurrency_pVar = self::formatCurrencyString_gFunc($inCurrency_pVar, false);
		$outCurrency_pVar = self::formatCurrencyString_gFunc($outCurrency_pVar, false);

		if(self::$euro2008_enabled_pVar && ($outCurrency_pVar == 'EUR' || $outCurrency_pVar == 'SKK')) {
			if(isset($extraParams_pVar['euro2008'])) {
				$extraParams_pVar['euro2008'] = strtolower($extraParams_pVar['euro2008']);
				if($extraParams_pVar['euro2008'] === 'primary') {
					// zobrazi sa iba primarna mena
					$value_pVar = self::getEuro2008PricePart_gFunc($value_pVar, $inCurrency_pVar, $outCurrency_pVar, true);
				}
				elseif ($extraParams_pVar['euro2008'] === 'secondary') {
					// zobrazi sa iba sekundarna mena
					$value_pVar = self::getEuro2008PricePart_gFunc($value_pVar, $inCurrency_pVar, $outCurrency_pVar, false);
				}
				elseif ($extraParams_pVar['euro2008'] === '2lines') {
					// do dvoch riadkov
					$value1_pVar = self::getEuro2008PricePart_gFunc($value_pVar, $inCurrency_pVar, $outCurrency_pVar, true);
					$value2_pVar = self::getEuro2008PricePart_gFunc($value_pVar, $inCurrency_pVar, $outCurrency_pVar, false);
					if(!empty($value2_pVar)) {
						$value_pVar = $value1_pVar . '<br />(' . $value2_pVar . ')';
					}
					else {
						$value_pVar = $value1_pVar;
					}
				}
				elseif ($extraParams_pVar['euro2008'] === 'plaintext') {
					// cisty text
					$value1_pVar = self::getEuro2008PricePart_gFunc($value_pVar, $inCurrency_pVar, $outCurrency_pVar, true);
					$value2_pVar = self::getEuro2008PricePart_gFunc($value_pVar, $inCurrency_pVar, $outCurrency_pVar, false);
					if(!empty($value2_pVar)) {
						$value_pVar = $value1_pVar . ' (' . $value2_pVar . ')';
					}
					else {
						$value_pVar = $value1_pVar;
					}
					$value_pVar = str_replace('&nbsp;', ' ', $value_pVar);
				}
				else {
					// standard PRIMARY(SECONDARY)
					$value1_pVar = self::getEuro2008PricePart_gFunc($value_pVar, $inCurrency_pVar, $outCurrency_pVar, true);
					$value2_pVar = self::getEuro2008PricePart_gFunc($value_pVar, $inCurrency_pVar, $outCurrency_pVar, false);
					if(!empty($value2_pVar)) {
						$value_pVar = $value1_pVar . '&nbsp;(' . $value2_pVar . ')';
					}
					else {
						$value_pVar = $value1_pVar;
					}
				}
			}
			else {
					// standard PRIMARY(SECONDARY)
					$value1_pVar = self::getEuro2008PricePart_gFunc($value_pVar, $inCurrency_pVar, $outCurrency_pVar, true);
					$value2_pVar = self::getEuro2008PricePart_gFunc($value_pVar, $inCurrency_pVar, $outCurrency_pVar, false);
					if(!empty($value2_pVar)) {
						$value_pVar = $value1_pVar . '&nbsp;(' . $value2_pVar . ')';
					}
					else {
						$value_pVar = $value1_pVar;
					}
			}
		}
		else {
			if($inCurrency_pVar !== $outCurrency_pVar) {
				$value_pVar = self::convertPrice_gFunc($value_pVar, $inCurrency_pVar, $outCurrency_pVar);
			}
			$currencyStr_pVar = self::formatCurrencyString_gFunc($outCurrency_pVar, true);
			$value_pVar = self::formatPrice_gFunc($value_pVar, $currencyStr_pVar);
		}

		return($value_pVar);
	}

	/**
	 * EURO 2008
	 * Vrati primarnu alebo sekundarnu cenu
	 * Predpoklada nahratu konfiguraciu a upraene parametre $inCurrency_pVar a $outCurrency_pVar
	 *
	 * @param unknown_type $value_pVar
	 * @param unknown_type $inCurrency_pVar
	 * @param unknown_type $outCurrency_pVar
	 */
	private static function getEuro2008PricePart_gFunc($value_pVar, $inCurrency_pVar, $outCurrency_pVar, $primary_pVar = true)
	{
		$currency_pVar = self::getEuro2008Currency_gFunc();

		if(!count($currency_pVar)) {
			// faza SKK - vratim originalnu menu
			if(!$primary_pVar) {
				return('');
			}
			else {
				if($inCurrency_pVar !== $outCurrency_pVar) {
					$value_pVar = self::convertPrice_gFunc($value_pVar, $inCurrency_pVar, $outCurrency_pVar);
				}
				$currencyStr_pVar = self::formatCurrencyString_gFunc($outCurrency_pVar, true);
			}
		}
		else {
			if($primary_pVar) {
				$x_pVar = 0;
			}
			else {
				if(!isset($currency_pVar[1])) {
					return('');
				}
				$x_pVar = 1;
			}

			if($currency_pVar[$x_pVar] === $inCurrency_pVar) {
				$currencyStr_pVar = priceFormat_gClass::formatCurrencyString_gFunc($currency_pVar[$x_pVar], true);
			}
			if($currency_pVar[$x_pVar] === $outCurrency_pVar) {
				$value_pVar = self::convertPrice_gFunc($value_pVar, $inCurrency_pVar, $currency_pVar[$x_pVar]);
				$currencyStr_pVar = self::formatCurrencyString_gFunc($currency_pVar[$x_pVar], true);
			}
			else {
				$value_pVar = self::convertPrice_gFunc($value_pVar, $inCurrency_pVar, $currency_pVar[$x_pVar]);
				$currencyStr_pVar = self::formatCurrencyString_gFunc($currency_pVar[$x_pVar], true);
			}
		}

		$value_pVar = self::formatPrice_gFunc($value_pVar, $currencyStr_pVar);
		return($value_pVar);
	}


	public static function convertPrice_gFunc($value_pVar, $fromCurrency_pVar, $toCurrency_pVar)
	{
		self::loadConf_gFunc();

		// ak je uz faza EUR, tak SKK tu uz nema co hladat, generujem warningy
		if(self::$euro2008_phase_pVar === 'EUR') {
			if($fromCurrency_pVar === 'SKK') {
				error_gClass::warning_gFunc(__FILE__, __LINE__, 'SKK currency: ' . $value_pVar . ' SKK');
			}
			elseif ($toCurrency_pVar === 'SKK') {
				error_gClass::warning_gFunc(__FILE__, __LINE__, 'display SKK currency: ' . $value_pVar . ' ' . $fromCurrency_pVar);
			}
		}

		if($fromCurrency_pVar == $toCurrency_pVar) {
			// nepotrebujem konvertovat
			return($value_pVar);
		}

		if($fromCurrency_pVar != self::$main_currency_pVar && $toCurrency_pVar != self::$main_currency_pVar) {
			// konvertujem dve rozne meny cez hlavnu menu
			$value_pVar = self::convertPrice_gFunc($value_pVar, $fromCurrency_pVar, self::$main_currency_pVar);
			return(self::convertPrice_gFunc($value_pVar, self::$main_currency_pVar, $toCurrency_pVar));
		}

		if($fromCurrency_pVar == self::$main_currency_pVar) {
			if(!isset(self::$rates_pVar[$toCurrency_pVar])) {
				error_gClass::warning_gFunc(__FILE__, __LINE__, string_gClass::get('str___currency_err_rate_sVar', $toCurrency_pVar));
			}
			else {
				$value_pVar = $value_pVar / self::$rates_pVar[$toCurrency_pVar]['rate_pVar'];
			}
		}
		else {
			if(!isset(self::$rates_pVar[$fromCurrency_pVar])) {
				error_gClass::warning_gFunc(__FILE__, __LINE__, string_gClass::get('str___currency_err_rate_sVar', $fromCurrency_pVar));
			}
			else {
				$value_pVar = $value_pVar * self::$rates_pVar[$fromCurrency_pVar]['rate_pVar'];
			}
		}
		return($value_pVar);
	}

	public static function formatCurrencyString_gFunc($currency_string_pVar, $short_pVar = false)
	{
		$currency_string_pVar = strtoupper($currency_string_pVar);

		// toto je easy. Dostal som menu v standardnom formate
		if(isset(self::$rates_pVar[$currency_string_pVar])) {
			if($short_pVar) {
				return(self::$rates_pVar[$currency_string_pVar]['short_pVar']);
			}
			else {
				return(self::$rates_pVar[$currency_string_pVar]['currency_pVar']);
			}
		}

		// asi mam skrateny format. To je uz trosku horsie, lebo musim prehladavat pole.
		foreach (self::$rates_pVar as $k_pVar=>$v_pVar) {
			if(strtoupper($v_pVar['short_pVar']) === $currency_string_pVar) {
				// nasiel som skratku
				if($short_pVar) {
					return(self::$rates_pVar[$k_pVar]['short_pVar']);
				}
				else {
					return(self::$rates_pVar[$k_pVar]['currency_pVar']);
				}
			}
		}

		// ak som nenasiel, vraciam original hodnotu
		error_gClass::warning_gFunc(__FILE__, __LINE__, string_gClass::get('str___currency_err_rate_sVar', $currency_string_pVar));
		return($currency_string_pVar);
	}

	protected static function getPriceParts_gFunc($value_pVar, $currency_pVar, $shortcut_pVar = false)
	{
		$multiplier_pVar = 1;
		if($shortcut_pVar) {
			$multiplier_pVar = 0;
			if($value_pVar >= 1000000000 && $value_pVar%1000000 == 0) { // miliarda ak sa da zobrazit maximalne s tromi desatinnymi miestami
				$multiplier_pVar = 1000000000;
			}
			if(!$multiplier_pVar && $value_pVar >= 1000000 && $value_pVar%10000 == 0) { // miliony ak sa da zobrazit maximalne s dvoma desatinnymi miestami
				$multiplier_pVar = 1000000;
			}
			if(!$multiplier_pVar && $value_pVar >= 1000 && $value_pVar%100 == 0) { // tisice ak sa da zobrazit maximalne s dvoma desatinnymi miestami
				$multiplier_pVar = 1000;
			}
			if(!$multiplier_pVar) {
				$multiplier_pVar = 1;
			}
		}

		if($multiplier_pVar == 1) {
			$shortcutStr_pVar = '';
		}
		elseif ($multiplier_pVar == 1000) {
			$shortcutStr_pVar = 'tis. ';
		}
		elseif ($multiplier_pVar == 1000000) {
			$shortcutStr_pVar = 'mil. ';
		}
		elseif ($multiplier_pVar == 1000000000) {
			$shortcutStr_pVar = 'mld. ';
		}

		if(!$shortcut_pVar) {
			$shortcutStr_pVar = '';
		}
		return(array('value'=>$value_pVar, 'value_str'=>self::getValueAsString_gFunc($value_pVar/$multiplier_pVar), 'multiplier'=>$multiplier_pVar, 'shortcut'=>$shortcutStr_pVar, 'currency'=>$currency_pVar));
	}

	public static function formatPrice_gFunc($value_pVar, $currency_pVar, $shortcut_pVar = false)
	{
		$parts_pVar = self::getPriceParts_gFunc($value_pVar, $currency_pVar, $shortcut_pVar);
		$result_pVar = $parts_pVar['value_str'] .' ' . $parts_pVar['shortcut'] . $parts_pVar['currency'];
		$result_pVar = str_replace(' ', '&nbsp;', $result_pVar);
		return($result_pVar);
	}

	private static function getValueAsString_gFunc($value_pVar)
	{
		$ret_pVar = number_format($value_pVar, 1, ',', ' ');
		$p_pVar = strpos($ret_pVar, ',');
		$ret_pVar = substr($ret_pVar, 0, $p_pVar);
		if($value_pVar - floor($value_pVar)) {
			$ret_pVar .= ',';
			$ret_pVar .= substr(round($value_pVar - floor($value_pVar),2), 2);
		}
		else {
			$ret_pVar .= ',-';
		}
		return($ret_pVar);
	}
}

return(true);
