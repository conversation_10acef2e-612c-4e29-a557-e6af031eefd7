<?php
if(isset($_SERVER['SCRIPT_FILENAME']) && strtolower($_SERVER['SCRIPT_FILENAME']) == strtolower(str_replace("\\",'/',__FILE__))) { header("HTTP/1.0 404 Not Found");  return(false); }
if(isset($GLOBALS['file__pdf_pVar'])) return(true);
$GLOBALS['file__pdf_pVar']=true;

class pdf_gClass
{
	protected function __construct()
	{

	}

	public static function newPdf_gFunc()
	{
		return(new tcpdf_gClass());
	}

	public function output_gFunc($fileName_pVar = 'document.pdf')
	{

	}

	public function writeHTML_gFunc($html_pVar)
	{

	}
}

// @TODO: ak budem puzivat aj ine classy ako TCPDF, tak to budem musiet dat do ineho suboru, aby som mohol fungovat aj bez pritomnosti TCPDF na serveri
		$http_host_pVar = main_gClass::getServerVar_gFunc('HTTP_HOST');
		$https_pVar = main_gClass::getServerVar_gFunc('HTTPS');
		$php_self_pVar = main_gClass::getServerVar_gFunc('PHP_SELF');
		// toto je nastavenie $k_path_url presne tak isto ako to nastavovalo tcpdf, ale musim to nastavit ja pomocou getServerVar_gFunc
		if (isset($http_host_pVar) AND (!empty($http_host_pVar))) {
			if(isset($https_pVar) AND (!empty($https_pVar)) AND strtolower($https_pVar)!='off') {
				$k_path_url = 'https://';
			} else {
				$k_path_url = 'http://';
			}
			$k_path_url .= $http_host_pVar;
			$k_path_url .= str_replace( '\\', '/', substr($php_self_pVar, 0, -24));
		}
		$_SERVER['DOCUMENT_ROOT'] = '%%DOCUMENT_ROOT%%';

define ('K_PATH_FONTS', main_gClass::getConfigVar_gFunc('path_system_include_gVar', 'runtime_pVar') . '../../truEngine/.lib/ext/tcpdf/fonts/');

require_once(main_gClass::getConfigVar_gFunc('path_system_include_gVar', 'runtime_pVar') . '../../vendor/tecnickcom/tcpdf/tcpdf.php');

class MYPDF_gClass extends TCPDF {

    // Page footer
    public function Footer() {
    	parent::Footer();

    	$ormargins = $this->getOriginalMargins();
		$this->SetX($ormargins['left']);

		$user_name_pVar = session_gClass::getUserDetail_gFunc('real_name');
		$this->Cell(0, 0, 'Vytlačil: ' . $user_name_pVar . ', ' . date('j.n.Y H:i:s'), 'T', 0, 'L');
    }

    public function Image($file, $x=null, $y=null, $w=0, $h=0, $type='', $link='', $align='', $resize=false, $dpi=300, $palign='', $ismask=false, $imgmask=false, $border=0, $fitbox=false, $hidden=false, $fitonpage=false, $alt=false, $altimgs=array())
     {
		if(substr($file, 0, 17) == '%%DOCUMENT_ROOT%%') {
			$file = substr($file, 17);
			$web_dir_pVar = main_gClass::getConfigVar_gFunc('web_dir_gVar', 'runtime_pVar');
			$file1 = substr($file, strlen($web_dir_pVar));
			ob_start();
			$file2 = main_gClass::getFileNameByDocName_gFunc($file1);
			ob_end_clean();
			if($file2 !== false) {
				$file = $file2;
			}
			else {
				$query = parse_url($file, PHP_URL_QUERY);
				$vars = array();
				parse_str($query, $vars);
				if(isset($vars['doc'])) {
					ob_start();
					$file = main_gClass::getFileNameByDocName_gFunc($vars['doc']);
					ob_end_clean();
				}
				else {
					while(isset($file[0]) && $file[0] == '/') {
						$file = substr($file, 1);
					}
					$file = main_gClass::getConfigVar_gFunc('document_root_gVar', 'runtime_pVar') . $file;
					if(!file_exists($file)) {
						/// subor neexistuje.....
						// @TODO: ak subor neexistuje, mohol by som dat linku na nejaky defaultny obrazok, pozor na sirku a vysku, ak su definvane...
					}
				}
			}

		}

		if(!file_exists($file)) {
			return;
		}

    	parent::Image($file, $x, $y, $w, $h, $type, $link, $align, $resize, $dpi, $palign, $ismask, $imgmask, $border);
    }
}

class tcpdf_gClass extends pdf_gClass
{
	private $tcpdf_pVar;

	public function __construct()
	{
		$this->tcpdf_pVar = new MYPDF_gClass(PDF_PAGE_ORIENTATION, PDF_UNIT, PDF_PAGE_FORMAT, true, 'UTF-8', false);

		// set document information
		$this->tcpdf_pVar->SetCreator(PDF_CREATOR);
		$this->tcpdf_pVar->SetAuthor('TruEngine');
		$this->tcpdf_pVar->SetTitle('TruEngine title');
		$this->tcpdf_pVar->SetSubject('TruEngine subject');
		$this->tcpdf_pVar->SetKeywords('TruEngine');

		// set header and footer fonts
		$this->tcpdf_pVar->setHeaderFont(Array('tahoma', '', PDF_FONT_SIZE_MAIN));
		$this->tcpdf_pVar->setFooterFont(Array('tahoma', '', PDF_FONT_SIZE_DATA));

		// set default monospaced font
		$this->tcpdf_pVar->SetDefaultMonospacedFont(PDF_FONT_MONOSPACED);


		//set margins
		$this->tcpdf_pVar->SetMargins(PDF_MARGIN_LEFT, PDF_MARGIN_TOP, PDF_MARGIN_RIGHT);
		$this->tcpdf_pVar->SetHeaderMargin(PDF_MARGIN_HEADER);
		$this->tcpdf_pVar->SetFooterMargin(PDF_MARGIN_FOOTER);

		$this->tcpdf_pVar->SetFont('tahoma', 'BI', 12);

		if(modules_gClass::isModuleRegistred_gFunc('kega')) {
			$this->tcpdf_pVar->SetHeaderData('opus_sapientiae.jpg', 40, '', '');
		}
//		$this->tcpdf_pVar->SetHeaderData('', 0, 'OPUS SAPIENTIAE', '');



		//set auto page breaks
		$this->tcpdf_pVar->SetAutoPageBreak(TRUE, PDF_MARGIN_BOTTOM);

		//set image scale factor
		$this->tcpdf_pVar->setImageScale(PDF_IMAGE_SCALE_RATIO);

		$this->tcpdf_pVar->AddPage();

	}

	public function output_gFunc($fileName_pVar = 'document.pdf')
	{
		$this->tcpdf_pVar->Output($fileName_pVar, 'I');
	}

	public function writeHTML_gFunc($html_pVar)
	{
        $html_pVar = str_replace('></img>', ' />', $html_pVar);
        $html_pVar = str_replace('></hr>', ' />', $html_pVar);
        $html_pVar = str_replace('></input>', ' />', $html_pVar);

//        dd($html_pVar);
//        $doc = new DOMDocument();
//        $doc->loadHTML($html_pVar);
        //echo $doc->saveHTML();


        // add thead to html
//        $html_pVar = preg_replace('/<table([^>]*)>/', '<table$1><thead>', $html_pVar);
//        $html_pVar = preg_replace('#</table>#', '</thead></table>', $html_pVar);

        //dd($html_pVar);
        //try {
            $this->tcpdf_pVar->writeHTML($html_pVar, true, 0, true, 0);
//        }
//        catch (Exception $e) {
//
//        }
	}
}


return(true);
