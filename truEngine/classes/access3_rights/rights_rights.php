<?php

class rights_rights_gClass extends truEngineBaseClass_gClass
{

    /**
     * loadne rights, a loadne aj groups.
     *
     * @param unknown_type $rightsArray_pVar
     * @param unknown_type $userID_pVar
     * @param unknown_type $userRole_pVar
     * @param unknown_type $userGroups_pVar
     * @return unknown
     */
    static function loadRights_gFunc(&$rightsArray_pVar, $userID_pVar, $userRole_pVar , &$userGroups_pVar, &$from_pVar, $addGroups_pVar = false)
    {
        // tu nastavim prava do pola $rightsArray_pVar.
        if(modules_gClass::isModuleRegistred_gFunc('access4_groups')) {
            rights_groups_gClass::loadRights_gFunc($rightsArray_pVar, $userID_pVar, $userRole_pVar, $userGroups_pVar, $from_pVar, $addGroups_pVar);
        }

        session_gClass::mergeRights_gFunc($rightsArray_pVar, db_access3_rights_gClass::loadRights_gFunc($userRole_pVar, $userID_pVar, $from_pVar));

        return($rightsArray_pVar);
    }

}

