<?php
namespace Mixedtype\Items\Models\Traits;

/**
  * Trait HasOptions
  * @package App\Models\Traits
  *
  * How to use:
  * - use trait HasOptions in your model
  * - implement getDefaultOptions() method on your model
  * - if you use hasChildren trait, you must implement getBaseModelClassName() and getOptionsModelForeignKey() methods on your parent model
  * - create your options model
  * - set protected $table = 'your_table_name__options';
  * - set protected $guarded = [];
  * - set public $timestamps = false;
  * - You can implement getName() and getValue() methods on your options model to get option name and value
  * - create your options table migration
  *
  *
  * $model->getOption('token');
  * $model->getOptions();
  * $model->options[0]->getName() // relation - but dont return default options defined in config
  * $model->setOption('token', '123456');
  *
  */

trait HasOptions
{
    protected static function getBaseModelClassName() : string
    {
        return static::class;
    }

    protected function getOptionsModelForeignKey() : string
    {
        return static::getSnakeName() . '_id';
    }

    private static function getSnakeName() : string
    {
        return strtolower(preg_replace('/(?<!^)[A-Z]/', '_$0', class_basename(static::getBaseModelClassName())));
    }

    public function options()
    {
        return $this->hasMany(static::getBaseModelClassName() . 'Option', $this->getOptionsModelForeignKey(), 'id');
    }

    public function setOption(string $option_name, $option_value) : void
    {
        $this->options()->updateOrCreate([
            $this->getOptionsModelForeignKey() => $this->id,
            'option_name' => $option_name,
        ], [
            'option_value' => $option_value
        ]);
    }

    public function getOption(string $option_name, ?string $defaultValue = null) : string | null
    {
        return $this->options()
            ->where('option_name', $option_name)
            ->value('option_value')
            ?? $this->getDefaultOption($option_name, $defaultValue);
    }

    public function getDefaultOption($option_name, ?string $defaultValue = null) : string | null
    {
        return $this->getDefaultOptions()[$option_name] ?? $defaultValue;
    }

    function getDefaultOptions() : array
    {
        return [];
    }

    public function getOptions() : array
    {
        return [
            ...$this->getDefaultOptions(),
            ...$this->options()
                ->get()
                ->mapWithKeys(function ($option) {
                    return [$option->getName() => $option->getValue()];
                })->toArray()
        ];
    }
}
