<?php

if(!isStamp_gFunc('kega', 'kega update-2009-10-07')) {
	
	$items_pVar = new install_item_gClass('users', false);

	$items_pVar->updateField_gFunc('skola', 'isic', 'xvarchar', 'ISIC/ITIC snr', array('len'=>'20', 'pattern'=>'/([0-9] |[0-9]){10}/', 'order_by'=>'yes', 'not_null'=>'yes', 'sk_field_info'=>'Zadajte sériové číslo ISIC/ITIC karty (10 miestne číslo)::Ak nemáte pridelený ISIC/ITIC preukaz, použite náhradnú hodnotu 0000000000 (10 núl).<br />Administrátori overia Vaše dôvody zadania tejto hodnoty, a ak sú opodstatnené, schvália Vám prístup do systému.'));
	$items_pVar->apply_gFunc();
	
	$items_pVar = new install_item_gClass('test_questions', false);
	$items_pVar->updateField_gFunc('main', 'moznosti', 'join',  'Možnosti', array('pattern'=>'test_answers|answers','min_value'=>4, 'max_value'=>25, 'comment'=>'`answers`.*|LEFT JOIN `%titems_test_answers__data` as `answers` ON `answers`.`test_question`=`D`.`item_id` AND `answers`.`status`<>\'deleted\''));
	$items_pVar->apply_gFunc();
	
	insertRights_gFunc('role', 'student', array('s_users_edit_myself_field_facebook' => true));
	insertRights_gFunc('role', 'pedagog', array('s_users_edit_myself_field_facebook' => true));
	insertRights_gFunc('role', 'student_navrhovatel', array('s_users_edit_myself_field_facebook' => true));
	
	stamp_gFunc('kega', 'kega update-2009-10-07');
}

if(!isStamp_gFunc('kega', 'kega update-2009-10-07-2')) {
	$items_pVar = new install_item_gClass('test_templates', false);
	$items_pVar->addField_gFunc('vyhodnotenie', 'hranica_uspesnosti', 'int', 'Kritérium pre úspešné absolvovanie (%)', array('sk_field_info'=>'::Pomer správnych odpovedí nutných pre úspešné absolvovanie testu. (vyjadrené v %)'));
	$items_pVar->setFormRule_gFunc('add_item', 'hranica_uspesnosti', 'edit');
	$items_pVar->setFormRule_gFunc('edit_item', 'hranica_uspesnosti', 'edit');
	$items_pVar->apply_gFunc();
	
	stamp_gFunc('kega', 'kega update-2009-10-07-2');
}

if(!isStamp_gFunc('kega', 'kega update-2009-10-07-3')) {
	$sql_pVar = 'alter table `%ttests_running` add column `in_stats` enum(\'yes\',\'no\') DEFAULT \'yes\' NOT NULL after `official`';
	if(!db_public_gClass::execute_gFunc($sql_pVar, __FILE__, __LINE__)) {
		return(false);
	}
	
	insertRights_gFunc('role', 'tester', array('s_test_report_error' => true));
	
	stamp_gFunc('kega', 'kega update-2009-10-07-3');
}

if(!isStamp_gFunc('kega', 'kega update-2009-10-07-4')) {
	$items_pVar = new install_item_gClass('test_templates', false);
	$items_pVar->addField_gFunc('vyhodnotenie', 'typ_hodnotenia', 'enum', 'Typ hodnotenia testu', array('not_null'=>'yes', 'sk_default_value'=>'standard', 'sk_field_info'=>'::Standard: 1 odpoved = 1 bod<br />Dvojmo = za kazdu odpoved 2 body<br />Prijmacky = &amp;lt;-50 az +50&amp;gt;<br />Celoodpovedova (maximalny pocet bodov sa = poctu OTAZOK, ak v odpovediach pre otazku spravi co i len jedinu chybu, cela otazka je zle)<br />Inverzná (umozni nastavit parameter"maximalny pocet chybnych odpovedi". Pri tomto počte chybných odpovedí je počet bodov 0.'));
	$items_pVar->setFormRule_gFunc('add_item', 'typ_hodnotenia', 'edit');
	$items_pVar->setFormRule_gFunc('edit_item', 'typ_hodnotenia', 'edit');
	
	$items_pVar->addEnum_gFunc('typ_hodnotenia', 'standard', 'štandard');
	$items_pVar->addEnum_gFunc('typ_hodnotenia', 'dvojmo', 'dvojmo');
	$items_pVar->addEnum_gFunc('typ_hodnotenia', 'prijmacky', 'príjmačky');
	$items_pVar->addEnum_gFunc('typ_hodnotenia', 'celoodpovedovy', 'celoodpovedový');
	$items_pVar->addEnum_gFunc('typ_hodnotenia', 'inverzny', 'inverzny');
	
	$items_pVar->addField_gFunc('vyhodnotenie', 'max_errors', 'int', 'Maximálny počet chybných odpovedí', array('sk_field_info'=>'::Uplatňuje sa iba pri Inverznom type hodnotenia.'));
	$items_pVar->setFormRule_gFunc('add_item', 'max_errors', 'edit');
	$items_pVar->setFormRule_gFunc('edit_item', 'max_errors', 'edit');
	
	$items_pVar->apply_gFunc();
	
	stamp_gFunc('kega', 'kega update-2009-10-07-4');
}

return(true);