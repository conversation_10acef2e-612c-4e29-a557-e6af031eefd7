<?xml version="1.0" encoding="utf-8" ?>
<truEngine-document xmlns:te="http://www.truengine.sk">

	<script type="text/javascript">
		var uLogin = document.getElementById('users_login');
		var uNick = document.getElementById('users_nick');
		var uIsic = document.getElementById('users_isic'); 
		
		if(uLogin) { uLogin.addEventListener('focus', updateLogin , true); }
		if(uNick) { uNick.addEventListener('focus', updateNick , true); }
		if(uIsic &amp;&amp; uIsic.value == '') {
		   var br = document.createElement("br");
		   var a = document.createElement("a");
		   var atxt = document.createTextNode('@L{nemám ISIC/ITIC kartu}');
		   a.appendChild(atxt);
		   a.href='#';
		   a.setAttribute("onclick",'resetIsic(); return(false);');
		   uIsic.parentNode.insertBefore( a, uIsic.nextSibling );
		   uIsic.parentNode.insertBefore( br, uIsic.nextSibling );
		}
		
		function resetIsic() {
			var uIsic = document.getElementById('users_isic');
			uIsic.value = '0000000000';
		}
		
		function updateLogin()
		{
			var uEmail = document.getElementById('users_email');
			var uLogin = document.getElementById('users_login');
			
			if(uLogin.value == '') {
				uLogin.value = uEmail.value;
			}
		}
		
		function updateNick()
		{
			var uFirstName = document.getElementById('users_first_name');
			var uLastName = document.getElementById('users_last_name');
			var uTitulPred = document.getElementById('users_titul_pred');
			var uTitulZa = document.getElementById('users_titul_za');
			var uNick = document.getElementById('users_nick');
			
			if(uNick.value == '') {
				if(uTitulPred.value != '') { uNick.value = uTitulPred.value; }
				if(uFirstName.value != '') {
					if(uNick.value != '') { uNick.value += ' '; }
					uNick.value += uFirstName.value;
				}
				if(uLastName.value != '') {
					if(uNick.value != '') { uNick.value += ' '; }
					uNick.value += uLastName.value;
				}
				if(uTitulZa.value != '') {
					if(uNick.value != '') { uNick.value += ', '; }
					uNick.value += uTitulZa.value;
				}
			}
		}
		
	</script>
	
</truEngine-document>
