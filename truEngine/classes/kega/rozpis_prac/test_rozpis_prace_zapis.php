<?php

class test_rozpis_prace_zapis_gClass extends additem_gClass
{
    private $predmety_pVar;

    protected function initParams_gFunc()
    {
        $this->setParam_gFunc('formtype-add','add_item');
        $this->setParam_gFunc('formtype-edit', 'edit_item');
        $this->setParam_gFunc('itemtype', 'test_rozpis_prace');
    }

    protected function initForm_gFunc($multiedit_pVar = false, $initFormRef_pVar = true)
    {
        parent::initForm_gFunc(false, false);
        if($this->formType_pVar == 'add_item') {
            $this->addField_gFunc('', 'predmet', 'enum', 'Predmet', true);
            $tmp_pVar = items_gClass::getValues_gFunc('test_questions', 'predmet');
            $this->predmety_pVar = array('-1'=>'');
            foreach($tmp_pVar as $v_pVar) {
                $this->predmety_pVar[$v_pVar['enum_field_value']] = $v_pVar['sk_enum_field_name_item'];
            }
            $this->setFieldOptions_gFunc('', 'predmet', $this->predmety_pVar);
            $this->addField_gFunc('', 'comment', 'text', 'Poznámka', false);
        }
        else {
            $sql_pVar = 'SELECT `D`.*,
								CONCAT_WS(\' \', `u1`.`titul_pred`, `u1`.`first_name`, `u1`.`last_name`, `u1`.`titul_za`) as `user`,
								CONCAT_WS(\' \', `u2`.`titul_pred`, `u2`.`first_name`, `u2`.`last_name`, `u2`.`titul_za`) as `koordinator`
						FROM `%ttest_work` AS `D`
						LEFT JOIN `%titems_users__data` as `u1` ON `D`.`user_id` = `u1`.`item_id`
						LEFT JOIN `%titems_users__data` as `u2` ON `D`.`koordinator_id` = `u2`.`item_id`
						WHERE `D`.`item_id` = %d';
            $data_pVar = db_public_gClass::getResult_gFunc($sql_pVar, __FILE__, __LINE__, $this->item_id_pVar);

            $this->addField_gFunc('', 'user', 'text', 'Meno', false, false, false);
            $this->setFieldDefaultValue_gFunc('user', $data_pVar['user']);

            $this->addField_gFunc('', 'rocnik', 'text', 'Ročník', false, false, false);
            $this->setFieldDefaultValue_gFunc('rocnik', $data_pVar['rocnik']);

            $this->addField_gFunc('', 'predmet', 'text', 'Predmet', true);
            $this->setFieldDefaultValue_gFunc('predmet', $data_pVar['predmet']);
            $this->addField_gFunc('', 'comment', 'text', 'Poznámka', false);
            $this->setFieldDefaultValue_gFunc('comment', $data_pVar['poznamka']);

            $this->addField_gFunc('', 'kniha', 'text', 'Kniha', false);
            $this->setFieldDefaultValue_gFunc('kniha', $data_pVar['kniha']);

            $this->addField_gFunc('', 'kniha_cast', 'varchar', 'Strany', false);
            $this->setFieldDefaultValue_gFunc('kniha_cast', $data_pVar['kniha_cast']);

            $this->addField_gFunc('', 'koordinator', 'text', 'Koordinator', false, false, false);
            $this->setFieldDefaultValue_gFunc('koordinator', $data_pVar['koordinator']);
        }
    }

    protected function saveData_gFunc()
    {
        if($this->formType_pVar == 'add_item') {
            if(!session_gClass::userHasRightsAccess_gFunc(s_test_rozpis_prace_zapis)) {
                return;
            }
            $data_pVar = array();
            $format_pVar = array();

            $data_pVar['user_id'] = session_gClass::getUserDetail_gFunc('user_id');
            $data_pVar['status'] = 'active';
            $data_pVar['join_date'] = date('Y-m-d H:i:s');
            $format_pVar[] = '%d,%s,%s';
            $rocnik = session_gClass::getUserDetail_gFunc('rocnik');
            if(substr($rocnik, 0, 7) == 'rocnik_') {
                $rocnik = substr($rocnik, 7);
            }
            $rocnik = intval($rocnik);

            if($rocnik) {
                $data_pVar['rocnik'] = $rocnik;
                $format_pVar[] = '%d';
            }
            $data_pVar['predmet'] = $this->predmety_pVar[$this->getFieldValue_gFunc('predmet')];
            $data_pVar['poznamka'] = $this->getFieldValue_gFunc('comment');
            $format_pVar[] = '%s,%s';
            db_public_gClass::insertData_gFunc('%ttest_work', implode(',', $format_pVar), $data_pVar, __FILE__, __LINE__);
        }
        else {
            if(!session_gClass::userHasRightsAccess_gFunc(s_test_rozpis_prace_edit)) {
                return;
            }

            $data_pVar = array();
            $format_pVar = array();

            $data_pVar['prepare_date'] = date('Y-m-d H:i:s');
            $data_pVar['predmet'] = $this->getFieldValue_gFunc('predmet');
            $data_pVar['poznamka'] = $this->getFieldValue_gFunc('comment');
            $data_pVar['kniha'] = $this->getFieldValue_gFunc('kniha');
            $data_pVar['kniha_cast'] = $this->getFieldValue_gFunc('kniha_cast');
            $data_pVar['koordinator_id'] = session_gClass::getUserDetail_gFunc('user_id');
            $format_pVar[] = '%s,%s,%s,%s,%s,%d';
            db_public_gClass::updateData_gFunc('%ttest_work', implode(',', $format_pVar), $data_pVar, '`item_id` = %d', array($this->item_id_pVar), __FILE__, __LINE__);


        }
    }
}

class test_rozpis_prace_zapis extends test_rozpis_prace_zapis_gClass {}
