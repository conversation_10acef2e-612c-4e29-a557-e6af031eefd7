<?php


class adduser_gClass extends additem_gClass
{
    protected function initParams_gFunc()
    {
        $this->setParam_gFunc('formtype-add','add_item');
        $this->setParam_gFunc('formtype-edit', 'edit_item');
        $this->setParam_gFunc('itemtype', 'users');
    }

    protected function initForm_gFunc($multiedit_pVar = false, $initFormRef_pVar = true)
    {
        if(isset($this->params['item_id'])) {
            session_session_gClass::recountUserStats_gFunc(intval($this->params['item_id']));
        }

        parent::initForm_gFunc($multiedit_pVar);
        $this->setFieldPrefix_gFunc('users_');
        if(!session_gClass::userHasRightsInfo_gFunc(s_users_set_isic_force_ok)) {
            $this->setFieldEditable_gFunc('isic_force_ok', false);
        }

        if($this->isField_gFunc('', 'status')) {
            if($this->getFieldValue_gFunc('status') != 'request') {
                $options_pVar = $this->getFieldOptions_gFunc('', 'status');
                unset($options_pVar['request']);
                $this->setFieldOptions_gFunc('', 'status', $options_pVar);
            }
            else {
                // request nemozem editovat cez tento form... neriesim to, davam exit...
                exit;
            }
        }

        $this->setFieldPrefix_gFunc('');
    }

    protected function validateField_gFunc($fieldsetName_pVar, $fieldName_pVar, $applyPrefix_pVar = true)
    {
        $result_pVar = parent::validateField_gFunc($fieldsetName_pVar, $fieldName_pVar);

        if($result_pVar === true) {
            if($fieldName_pVar === 'users_login' || $fieldName_pVar === 'users_nick') {
                // zistim ci uz existuje takyto login alebo nick
                if(!session_session_gClass::checkForUniqueLoginName_gFunc(
                    $this->getFieldValue_gFunc($fieldName_pVar),
                    $this->getVar_gFunc('item_id'))) {
                    // error
                    $this->setError_gFunc(string_gClass::get('str__session_login_exists_sVar'), $fieldName_pVar);
                    return(false);
                }
            }
        }
        return($result_pVar);
    }

    protected function saveData_gFunc()
    {
        $extraData_pVar = $this->forcedData_pVar;

        $check_isic_pVar = false;
        $this->setFieldPrefix_gFunc('users_');
        $user_id_pVar = $this->getFieldValue_gFunc('item_id');
        $isic_pVar = $this->getFieldValue_gFunc('isic');
        if(!empty($isic_pVar)) {
            $isic_pVar = str_replace(' ', '', $isic_pVar);
            $extraData_pVar['isic'] = $isic_pVar;
        }

        if($user_id_pVar) {
            if($this->getFieldValue_gFunc('isic_ok') !== false && session_gClass::userHasRightsInfo_gFunc(s_users_set_isic_force_ok)) {
                $sql_pVar = 'SELECT `isic_force_ok` FROM `%titems_users__data` WHERE `item_id` = %d';
                $oldValue_pVar = db_public_gClass::getField_gFunc($sql_pVar, __FILE__, __LINE__, $user_id_pVar);

                // ak chcem nastavit jeho platnost, tak ju nastavim, a dalej necheckujem.
                if($this->getFieldValue_gFunc('isic_force_ok') == 'yes') {
                    $extraData_pVar['isic_ok'] = 'yes';
                    $check_isic_pVar = false;
                }

                // ak som zrusil natvrdo nastavenu platnost, zrusim platnost, a ocheckujem.
                if($this->getFieldValue_gFunc('isic_force_ok') == 'no' && $oldValue_pVar == 'yes') {
                    $extraData_pVar['isic_ok'] = 'no';
                    $check_isic_pVar = true;
                }

                // ak som zmenil isic, tak zrusim jeho platnost.
                $sql_pVar = 'SELECT `isic` FROM `%titems_users__data` WHERE `item_id` = %d';
                $oldValue_pVar = db_public_gClass::getField_gFunc($sql_pVar, __FILE__, __LINE__, $user_id_pVar);
                if($this->getFieldValue_gFunc('isic') != $oldValue_pVar) {
                    $extraData_pVar['isic_ok'] = 'no';
                    $extraData_pVar['isic_force_ok'] = 'no';
                    $check_isic_pVar = true;
                }
            }

            if(modules_gClass::isModuleRegistred_gFunc('kega')) {
                $sql_pVar = 'SELECT `motto` FROM `%titems_users__data` WHERE `item_id` = %d';
                $oldValue_pVar = db_public_gClass::getField_gFunc($sql_pVar, __FILE__, __LINE__, $user_id_pVar);

                if($this->getFieldValue_gFunc('motto') !== $oldValue_pVar) {
                    $extraData_pVar['motto_last_changed'] = date('Y-m-d H:i:s');
                }
            }
        }

        if($user_id_pVar) {

        }
        else {
            if(!db_items_gClass::isFormRule_gFunc('users', $this->params['formtype-add'], 'nick', 'edit')) {
                $tmp_pVar = array();
                if(strlen($this->getFieldValue_gFunc('titul_pred'))) {
                    $tmp_pVar[] = $this->getFieldValue_gFunc('titul_pred');
                }
                if(strlen($this->getFieldValue_gFunc('first_name'))) {
                    $tmp_pVar[] = $this->getFieldValue_gFunc('first_name');
                }
                if(strlen($this->getFieldValue_gFunc('last_name'))) {
                    $tmp_pVar[] = $this->getFieldValue_gFunc('last_name');
                }
                if(strlen($this->getFieldValue_gFunc('titul_za'))) {
                    $tmp_pVar[] = $this->getFieldValue_gFunc('titul_za');
                }
                $extraData_pVar['nick'] = implode(' ', $tmp_pVar);
            }
            if(!db_items_gClass::isFormRule_gFunc('users', $this->params['formtype-add'], 'login', 'edit')) {
                $tmp_pVar = array();
                if(strlen($this->getFieldValue_gFunc('first_name'))) {
                    $tmp_pVar[] = $this->getFieldValue_gFunc('first_name');
                }
                if(strlen($this->getFieldValue_gFunc('last_name'))) {
                    $tmp_pVar[] = $this->getFieldValue_gFunc('last_name');
                }
                $extraData_pVar['login'] = implode('.', $tmp_pVar);
                $extraData_pVar['login'] = string_gClass::removeDiacritic_gFunc($extraData_pVar['login']);
                $extraData_pVar['login'] = preg_replace('/[^a-zA-Z0-9\.\@]/', '-', $extraData_pVar['login']);
                $this->generatedLoginName_pVar = $extraData_pVar['login'];
            }
            if(!db_items_gClass::isFormRule_gFunc('users', $this->params['formtype-add'], 'password', 'edit')) {
                $extraData_pVar['password'] = uniqid('nopass-');
                $extraData_pVar['login_enabled'] = 'disabled';
            }

            if(!isset($extraData_pVar['join_users_onlineusers'])) $extraData_pVar['join_users_onlineusers'] = '';
            if(!isset($extraData_pVar['session_settings'])) $extraData_pVar['session_settings'] = '';
        }


        $this->setFieldPrefix_gFunc('');
        items_gClass::editItemByForm_gFunc('users', $this, $extraData_pVar);
        if($check_isic_pVar && modules_gClass::isModuleRegistred_gFunc('cdouk')) {
            $cdouk_pVar = new cdouk_gClass();
            $cdouk_pVar->getData_gFunc($isic_pVar, false, true, true);
        }
    }
}

class adduser extends adduser_gClass {}
