<?php

namespace Mixedtype\Forms\Components;

use Illuminate\Support\Facades\DB;
use Mixedtype\Forms\Components\Form;

class Userlist extends Form
{
    static function getCurrentUserId()
    {
        return \session_gClass::getUserDetail_gFunc('user_id');
    }

    static function getUserFormatedName($user_id, $default = '')
    {
        if(!is_numeric($user_id)) {
            return $default;
        }
        $data = DB::table('kega_items_users__data')->where('item_id', $user_id)->first();
        if(!$data) {
            return $default;
        }
        return self::getUserFormatedNameFromData($data);
    }

    public static  function getUserFormatedNameFromData($user)
    {
        $id = $user->login;
        if(empty($id)) {
            $id = '*'.$user->item_id.'*';
        }
        return trim($user->titul_pred . ' ' . $user->first_name . ' ' . $user->last_name . ' ' . $user->titul_za)
            . ' [' . $id . ']';
    }

    public function getUsers($field, $query):array
    {
        // tu musim trosku aj prava osetrit
        if(!\session_gClass::userHasRightsAccess_gFunc(\s_users_show_userlist)) {
            return(array());
        }

        $users = DB::table('kega_items_users__data')
            ->where('status', 'active')
            ->where(function($sqlquery) use ($query) {
                $sqlquery->where('last_name', 'like', '%' . $query . '%')
                    ->orWhere('first_name', 'like', '%' . $query . '%')
                    ->orWhere(DB::raw('concat(titul_pred, " ", first_name, " ", last_name, " ", titul_za)'), 'like', '%' . $query . '%')
                    ->orWhere(DB::raw('concat(titul_pred, " ", last_name, " ", first_name, " ", titul_za)'), 'like', '%' . $query . '%')
                    ->orWhere('login', 'like', '%' . $query . '%')
                ;
            })
            ->orderBy('last_name')
            ->orderBy('first_name')
            ->get();

        $ret_pVar = ['results' => [

        ]];
        foreach($users as $user) {
            $ret_pVar['results'][] = [
                'id' => $user->item_id,
                'text' => self::getUserFormatedNameFromData($user)
            ];
        }

        return $ret_pVar;
    }

}
