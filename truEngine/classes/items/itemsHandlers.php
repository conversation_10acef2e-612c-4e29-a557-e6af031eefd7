<?php

class itemsHandlers_gClass
{
    protected $systemName_pVar;

    private function __construct() {}

    static public function factory_gFunc($systemName_pVar)
    {
        $info_pVar = db_items_gClass::getInfo_gFunc($systemName_pVar);
        if(!empty($info_pVar['modules'])) {
            $modules_pVar = explode(',', $info_pVar['modules']);
            foreach ($modules_pVar as $module_pVar) {
                if(!modules_gClass::isModuleRegistred_gFunc($module_pVar)) {
                    error_gClass::fatal_gFunc(__FILE__, __LINE__, 'Module ' . $module_pVar . ' is not registred.');
                    return(null);
                }
            }
        }

        $className_pVar = 'itemsHandlers_' . $systemName_pVar;
        if(!class_exists($className_pVar)) {
            // ak neexistuje trieda.. pretoze ju nemusim vzdy definovat.
            $obj_pVar = new itemsHandlers_gClass();
        }
        else {
            $obj_pVar = new $className_pVar;
        }
        $obj_pVar->systemName_pVar = $systemName_pVar;
        return($obj_pVar);
    }

    function beforeInsert_gFunc(&$newData_pVar)
    {

    }

    function afterInsert_gFunc(&$newData_pVar)
    {

    }

    function beforeUpdate_gFunc(&$newData_pVar, &$oldData_pVar)
    {

    }

    function afterUpdate_gFunc(&$newData_pVar, &$oldData_pVar)
    {

    }


    function beforeDelete_gFunc(&$oldData_pVar)
    {

    }

    function afterDelete_gFunc(&$oldData_pVar)
    {

    }

}


