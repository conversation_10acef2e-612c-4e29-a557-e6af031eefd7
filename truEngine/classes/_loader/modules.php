<?php

class modules_gClass
{
    static private $modules_pVar = array();
    static private $mapClasses_pVar = false;
    static private $mapHandlers_pVar = false;

    private function __construct()
    {

    }

    static function isModuleRegistred_gFunc($module_name_pVar, $load_it_pVar=true, $use_error_pVar = error_gClass::E_OK)
    {
        if(strpos($module_name_pVar, ',') !== false) {
            $modules_pVar = explode(',', $module_name_pVar);
            foreach ($modules_pVar as $module_name_pVar) {
                if(!self::isModuleRegistred_gFunc($module_name_pVar, $load_it_pVar, $use_error_pVar)) {
                    return(false);
                }
            }
            return(true);
        }
        if(self::isModuleLoaded_gFunc($module_name_pVar)) return(true);

        if(!main_gClass::getConfigVar_gFunc($module_name_pVar, 'modules')) {
            if($use_error_pVar != error_gClass::E_OK) {
                error_gClass::_error_gFunc($use_error_pVar, __FILE__, __LINE__, string_gClass::get('str___loader_err_module_sVar', $module_name_pVar));
            }
            return(false);
        }

        if($module_name_pVar == '_loader') { // loader je vzdy nahraty, ak je povoleny v configu
            self::$modules_pVar['_loader'] = true;
            self::loadStringTable_gFunc($module_name_pVar);
            return(true);
        }

        if(!$load_it_pVar) return(true);
        return(self::_loadModule_gFunc($module_name_pVar));
    }

    static private function _loadModule_gFunc($module_name_pVar)
    {
        if(self::isModuleLoaded_gFunc($module_name_pVar)) return(true);

        $moduleFile_pVar = main_gClass::getConfigVar_gFunc('path_system_include_gVar', 'runtime_pVar') . '_' . $module_name_pVar . '.module.php';
        log_gClass::write_gFunc('LOAD_MODULE', $module_name_pVar . ' ' . $moduleFile_pVar, false);
        if(!include_once($moduleFile_pVar)) {
            error_gClass::error_gFunc(__FILE__, __LINE__, string_gClass::get('str___loader_err_loadmodule_sVar', $module_name_pVar . '-' . $moduleFile_pVar));
            return(false);
        }
        log_gClass::write_gFunc('LOAD_MODULE_OK', $module_name_pVar, false);
        self::$modules_pVar[$module_name_pVar] = true;
        self::loadStringTable_gFunc($module_name_pVar);
        return(true);
    }

    static public function isModuleLoaded_gFunc($module_name_pVar)
    {
        if(isset(self::$modules_pVar[$module_name_pVar])) {
            return(true);
        }
        else {
            return(false);
        }
    }

    static public function loadStringTable_gFunc($moduleName_pVar)
    {
        $lng_pVar = main_gClass::getLanguage_gFunc();
        if(empty($lng_pVar)) {
            $lng_pVar = 'sk';
        }
        $fileName_pVar = main_gClass::getConfigVar_gFunc('path_system_include_gVar', 'runtime_pVar') . '_' . $moduleName_pVar . '.'. $lng_pVar .'.str.php';
        if(fileSafe_gClass::file_exists_gFunc($fileName_pVar)) include_once($fileName_pVar);
    }

    static public function getModules_gFunc()
    {
        return(self::$modules_pVar);
    }

    static public function mapClassLoad_gFunc()
    {
        $cacheDir_pVar = main_gClass::getPathForCache_gFunc();
        $mapFile_pVar = $cacheDir_pVar . '.classes.map.php';
        $mapFile_pVar = fileSafe_gClass::safePath_gFunc($mapFile_pVar);

        if(fileSafe_gClass::file_exists_gFunc($mapFile_pVar)) {
            include_once($mapFile_pVar);
        }
    }

    static public function mapClassAdd_gFunc($className_pVar, $moduleName_pVar)
    {
        if(!is_array(self::$mapClasses_pVar)) {
            self::$mapClasses_pVar = array();
        }

        self::$mapClasses_pVar[$className_pVar] = $moduleName_pVar;
    }

    static public function mapClassAddAndSave_gFunc($className_pVar, $moduleName_pVar)
    {
        $className_pVar = strtolower($className_pVar);

        $cacheDir_pVar = main_gClass::getPathForCache_gFunc();
        $mapFile_pVar = $cacheDir_pVar . '.classes.map.php';
        $mapFile_pVar = fileSafe_gClass::safePath_gFunc($mapFile_pVar);

        $f_pVar = fopen($mapFile_pVar, 'at');
        fputs($f_pVar, '<' . '?php modules_gClass::mapClassAdd_gFunc(\'' . $className_pVar . '\', \'' . $moduleName_pVar .'\'); ' . LF . '?' . '>');
        fclose($f_pVar);

        if(!is_array(self::$mapClasses_pVar)) {
            self::$mapClasses_pVar = array();
        }
        self::$mapClasses_pVar[$className_pVar] = $moduleName_pVar;
    }

    static public function findClass_gFunc($className_pVar)
    {
        if(!is_array(self::$mapClasses_pVar)) {
            self::$mapClasses_pVar = array();
            self::mapClassLoad_gFunc();
        }
        $origClassName_pVar = $className_pVar;
        $className_pVar = strtolower($className_pVar);
        if(isset(self::$mapClasses_pVar[$className_pVar])) {
            return(self::$mapClasses_pVar[$className_pVar]);
        }

        // trieda nie je namapovana, skusim ju najst
        $modules_pVar = main_gClass::getConfigVar_gFunc('modules');
        $libDir_pVar = main_gClass::getConfigVar_gFunc('path_system_include_gVar', 'runtime_pVar');
        foreach($modules_pVar as $moduleName_pVar=>$v) {
            $fileName_pVar = $libDir_pVar . '_' . $moduleName_pVar . '.module.php';
            $fileContent_pVar = file_get_contents($fileName_pVar);
            if(preg_match('/(.*)class(\s+)'.$origClassName_pVar.'(\s+)(.*)/i', $fileContent_pVar)) {
                self::mapClassAddAndSave_gFunc($className_pVar, $moduleName_pVar);
                return($moduleName_pVar);
            }
        }

        // hladam v adresari classes
        foreach($modules_pVar as $moduleName_pVar=>$v) {
            $dir = $libDir_pVar . '../classes/' . $moduleName_pVar . '/';

            if(self::findClassInDir($origClassName_pVar, $dir)) {
                self::mapClassAddAndSave_gFunc($className_pVar, $moduleName_pVar);
                return($moduleName_pVar);
            }
        }

        return(false);
    }

    static private function findClassInDir($className_pVar, $dir)
    {
        if(file_exists($dir)) {
            // prejde, vsetky subory v $dir
            $d = dir($dir);
            while (false !== ($entry = $d->read())) {
                if($entry == '.' || $entry == '..') {
                    continue;
                }
                if(is_dir($dir . $entry)) {
                    if(self::findClassInDir($className_pVar, $dir . $entry . '/')) {
                        return(true);
                    }
                    continue;
                }
                $fileContent_pVar = file_get_contents($dir . $entry);
                if(preg_match('/(.*)class(\s+)'.$className_pVar.'(\s+)(.*)/i', $fileContent_pVar)) {
                    return true;
                }
            }
        }
        return false;
    }

    static public function initClass_gFunc($className_pVar)
    {
        $moduleName_pVar = self::findClass_gFunc($className_pVar);
        if($moduleName_pVar === false) {
            return(false);
        }
        if(self::isModuleRegistred_gFunc($moduleName_pVar)) {
            return(true);
        }
        return(false);
    }

    static public function mapHandlersLoad_gFunc()
    {

    }

}
