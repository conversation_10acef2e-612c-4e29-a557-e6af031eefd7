<style>
    [x-cloak] { display: none !important; }
</style>

<div id="{{ $widgetInstanceId }}">
    <?php $form = \Mixedtype\Forms\FormFactory::createForm(\Mixedtype\Forms\FakeForm::class); ?>

    <div x-data="startTestApp">
        @if($template)
            <div class="row">
                <div class="col">
                    <strong>{{ $template->sk_name }}</strong>
                </div>
            </div>

            <div class="row">
                <div class="col">
                    {{ $template->sk_description }}
                </div>
            </div>
        @endif

        @if(!empty($formErrors))
            <div class="alert alert-danger">
                @foreach($formErrors as $error)
                    {{ $error }}<br />
                @endforeach
            </div>
        @endif

        @if($template)
                @if($displayForm)
                    <form method="post" id="startTestForm" action="{{ request()->url() }}" x-on:submit="startTest">
                        <input type="hidden" name="template_id" value="{{ $template->item_id }}" />

                        <div class="row my-5">
                            @if($template->access_key)
                                <div class="col-12">
                                    Pre spustenie tohto testu musíte zadať prístupový kód.
                                </div>
                            @endif
                            @if($template->official === 'yes')
                                <div class="col-12">
                                    Pre spustenie tohto testu musíte zadať randomizačný kód.
                                </div>
                            @endif
                        </div>

                        @if($template->access_key)
                            <x-field type="text" name="access_key" label="Prístupový kód:" x-model="accessKey" x-bind:class="accessKeyClasses" />
                        @endif

                        @if($template->official === 'yes')
                            <x-field type="text" name="randomization_key" label="Randomizačný kód:" x-model="randomizationKey" x-bind:class="randomizationKeyClasses" />
                        @endif

                        <x-submit  name="submit" label="Spustiť test" />
                    </form>
                @endif

                @if($ready)
                    <div class="row my-5">
                        <div class="col-12">
                            <div class="alert alert-success" style="display: none" id="{{ $running_test_id }}">
                                Test je pripravený na spustenie.
                                <a href="/sk/testy/test?test_id=" class="btn btn-primary">Spustiť test</a>
                            </div>

                            <div class="spinner-border text-primary" role="status" id="{{ $running_test_id }}-waiting">
                                <span class="sr-only"></span>
                            </div>

                            <script type="text/javascript">
                                $(document).ready(function() {
                                    $.ajax({
                                        url: '/sk/job/{{ $running_test_id }}',
                                        type: 'GET',
                                        success: function(response) {
                                            if(response.status === 'success') {
                                                $('#{{ $running_test_id }} a').attr('href', '/sk/testy/test?test_id=' + response.running_test_id);
                                                $('#{{ $running_test_id }}').show();
                                                $('#{{ $running_test_id }}-waiting').hide();
                                            }
                                        },
                                        error: function(response) {
                                            $('#{{ $running_test_id }}-waiting').hide();
                                            alert('Nastala chyba pri spúšťaní testu. Skúste to prosím znova.');
                                        }
                                    });
                                });
                            </script>



                        </div>
                    </div>
                @endif

        @endif
    </div>


    @if($template)
    <script>
        document.addEventListener('alpine:init', () => {
            Alpine.data('startTestApp', () => {
                return {
                    accessKey:'{{ $accessKey }}',
                    randomizationKey:'{{ $randomizationKey }}',
                    accessKeyClasses: { 'is-invalid' : false },
                    randomizationKeyClasses: { 'is-invalid' : false },
                    isError: {{ $isFormError?'true':'false' }},
                    startTest(event) {
                        $('#access_key').next('.invalid-feedback').remove();
                        this.accessKeyClasses['is-invalid'] = false;
                        this.randomizationKeyClasses['is-invalid'] = false;
                        this.isError = false;

                        @if($template->access_key)
                            if(!this.accessKey.length) {
                                this.accessKeyClasses['is-invalid'] = true;
                                $('#access_key').after('<div class="invalid-feedback">Prosím zadajte prístupový kód.</div>');
                                this.isError = true;
                            }
                        @endif

                        @if($template->official === 'yes')
                            $('#randomization_key').next('.invalid-feedback').remove();
                            if(this.randomizationKey.length !== 6) {
                                this.randomizationKeyClasses['is-invalid'] = true;
                                $('#randomization_key').after('<div class="invalid-feedback">Prosím zadajte správny randomizačný kód.</div>');
                                this.isError = true;
                            }
                        @endif

                        if(!this.isError) {
                            console.log('submitting form');

                            //$('#startTestForm').submit();
                        }
                        else {
                            event.preventDefault();
                            return false;
                        }
                    }
                }
            });
        });
    </script>
    @endif

</div>
