

<meta charset="utf-8">
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta name="csrf-token" content="{{ csrf_token() }}">

<title>{{ config('app.name', 'truEngine') }}</title>


@php
    if(!isset($layout)) {
        $layout = 'kega-modern';
	}
@endphp
<link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet" integrity="sha384-QWTKZyjpPEjISv5WaRU9OFeRpok6YctnYmDr5pNlyT2bRjXh0JMhjY6hW+ALEwIH" crossorigin="anonymous">
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js" integrity="sha384-YvpcrYf0tY3lHB60NNkmXc5s9fDVZLESaAA55NDzOxhy9GkcIdslK1eN7N6jIeHz" crossorigin="anonymous"></script>

<script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
<script src="/js/vendor/components.js" type="module" ></script>
<script src="/js/vendor/forms.js" type="module" ></script>
<script defer src="https://cdn.jsdelivr.net/npm/alpinejs@3.14.8/dist/cdn.min.js"></script>


<link href="https://cdn.jsdelivr.net/npm/tom-select@2.4.3/dist/css/tom-select.css" rel="stylesheet">
<script src="https://cdn.jsdelivr.net/npm/tom-select@2.4.3/dist/js/tom-select.complete.min.js"></script>

{{--<script src="https://cdn.jsdelivr.net/npm/@tabler/core@1.1.1/dist/js/tabler.min.js"></script>--}}
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@tabler/core@1.1.1/dist/css/tabler.min.css" />
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@tabler/icons-webfont@latest/dist/tabler-icons.min.css" />


@if($layout != 'text')
    <link rel="stylesheet" href="/css/truEngine.css" type="text/css" media="screen" />
    <link rel="stylesheet" href="/javascript/jquery-ui-1.13.3.full-smoothness/jquery-ui.min.css" type="text/css" media="screen" />
    <link rel="stylesheet" href="/javascript/select2-4.0.13/dist/css/select2.css" type="text/css" media="screen" />
    <link rel="stylesheet" href="/css/mixedtype/mixedtype.css" type="text/css" media="screen" />
@endif


<script type="text/javascript" src="/javascript/jquery-3.7.1.min.js"></script>
<script type="text/javascript" src="/javascript/mixedtype.js"></script>
<script type="text/javascript">var $j = $;</script>
<script type="text/javascript">var oldJsEnabled = false;</script>
<script type="text/javascript" src="/javascript/jquery-ui-1.13.3.full-smoothness/jquery-ui.min.js"></script>
<script type="text/javascript" src="/javascript/select2-4.0.13/dist/js/select2.full.js"></script>
<script type="text/javascript" src="/js/truEngine.js"></script>



@if($layout == 'kega-modern')
    <link rel="stylesheet" href="/design/style.css" type="text/css" media="screen"></link>
    <link rel="stylesheet" href="/css/layout/modern.css?t={{ config('opus.scripts_version') }}" type="text/css" />
@endif

@if($layout == 'kega-retro')

    <link rel="stylesheet" href="/design-retro/style.css" type="text/css" media="screen"></link>
@endif

@if($layout == 'basic')
    <link rel="stylesheet" href="/css/v4.css" type="text/css" media="screen" />
    <link rel="stylesheet" href="/css/screen.css" type="text/css" media="screen" />
    <link rel="stylesheet" href="/css/print.css" type="text/css" media="print" />
    <link rel="stylesheet" href="/css/fixedWidth.css" type="text/css" media="screen" />
    <link rel="stylesheet" href="/css/layout/basic.css" type="text/css" media="screen"></link>
@endif


<script type="text/javascript">
    var _gaq = _gaq || [];
    _gaq.push(['_setAccount', 'UA-********-2']);
    _gaq.push(['_trackPageview']);
    (function() {
        var ga = document.createElement('script'); ga.type = 'text/javascript'; ga.async = true;
        ga.src = ('https:' == document.location.protocol ? 'https://ssl' : 'http://www') + '.google-analytics.com/ga.js';
        var s = document.getElementsByTagName('script')[0]; s.parentNode.insertBefore(ga, s);
    })();

</script>

