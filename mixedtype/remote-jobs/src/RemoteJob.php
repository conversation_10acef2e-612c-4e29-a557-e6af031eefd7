<?php
namespace Mixedtype\RemoteJobs;


class RemoteJob implements RemoteJobInterface
{
    protected $jobId;

    public function dispatch()
    {
        \Illuminate\Support\Facades\DB::table('remote_jobs')
            ->insert([
                'job_id' => $this->getJobId(),
                'status' => 'pending',
                'created_at' => \Carbon\Carbon::now(),
                'expires_at' => \Carbon\Carbon::now()->addMinutes(5),
                'serialized_object' => serialize($this)
            ]);
        return $this->getJobId();
    }
    public function getJobId()
    {
        if(!$this->jobId) {
            $this->jobId = uniqid('job_');
        }
        return $this->jobId;
    }

    static public function execute($jobId)
    {
        $jobRecord = \Mixedtype\RemoteJobs\Models\RemoteJob::where('job_id', $jobId)
            ->first();
        if(!$jobRecord || $jobRecord->status !== 'pending' || $jobRecord->expires_at < \Carbon\Carbon::now()) {
            return response()->json([
                'job_id' => $jobId,
                'status' => 'error',
                'message' => 'Job not found'
            ])->setStatusCode(500);
        }
        /** @var RemoteJob $job */
        $job = unserialize($jobRecord->serialized_object);
        $jobRecord->started_at = \Carbon\Carbon::now();
        $jobRecord->save();
        $response = $job->handle();
        $jobRecord->finished_at = \Carbon\Carbon::now();
        $jobRecord->status = json_decode($response->getContent())->status;
        $jobRecord->save();
        return $response;
    }

    public function handle(): \Symfony\Component\HttpFoundation\Response
    {
        return response()->json([
            'job_id' => $this->getJobId(),
            'status' => 'success'
        ]);
    }
}
