<x-layout layout="single-question" theme="kega-modern">
    <h1>test komunikacie js komponentov</h1>

    <script>

        class BaseStore {
            reset() {
                this.text = 'Hello, from base store!';
            }
        }
    </script>


        <div x-data>
            <h2>komponenta1</h2>

            <button @click="$store.component1.changeText()">Change text</button>
            <p x-text="$store.component1.text"></p>

            <script>
                document.addEventListener('alpine:init', () => {

                    class component1 extends BaseStore {
                        constructor() {
                            super();
                            console.log('init')
                            this.text = 'Hello, from component1 constructor !'
                        }

                        changeText(x=null) {
                            if(x) {
                                this.text = x
                            }
                            else {
                                this.text = 'Hello, from component1, again !'
                            }
                        }
                    }

                    Alpine.store('component1', new component1())
                })
            </script>

        </div>



        <div x-data>
            <h2>komponenta2</h2>

            <button @click="$store.component2.changeText()">Change text</button>
            <p x-text="$store.component2.text"></p>

            <button @click="$store['component1'].changeText('testujem z 2')">Change text of component1**</button>
            <button @click="$store['component1'].reset()">RESET**</button>

            <script>
                document.addEventListener('alpine:init', () => {
                    class component2 extends BaseStore {
                        constructor() {
                            super();
                            console.log('init')
                            this.text = 'Hello, from component2 constructor !'
                        }

                        changeText(x=null) {
                            if(x) {
                                this.text = x
                            }
                            else {
                                this.text = 'Hello, from component2, again !'
                            }
                        }
                    }
                    Alpine.store('component2', new component2())
                })

            </script>
        </div>


    <div x-data>
        <h2>komponenta3</h2>

        <button @click="$store.component3.changeText()">Change text</button>
        <p x-text="$store.component3.text"></p>

        <button @click="$store['component1'].changeText('testujem z 3')">Change text of component1**</button>
        <button @click="$store['component1'].reset()">RESET 1**</button>
        <button @click="$store['component2'].reset()">RESET 2**</button>
        <button @click="$store['component3'].reset()">RESET 3**</button>

        <script>
            document.addEventListener('alpine:init', () => {
                class component3 extends BaseStore {
                    constructor() {
                        super();
                        console.log('init')
                        this.text = 'Hello, from component3 constructor !'
                    }

                    changeText(x=null) {
                        if(x) {
                            this.text = x
                        }
                        else {
                            this.text = 'Hello, from component3, again !'
                        }
                    }
                }
                Alpine.store('component3', new component3())
            })

        </script>
    </div>


</x-layout>
