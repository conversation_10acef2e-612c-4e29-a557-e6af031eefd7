
body {
  padding:0px;
  margin:0px;
  background: url(bg.gif) repeat-x #e0e9ed;
  font-family:"Helvetica condensed","Arial CE","Helvetica CE",Arial,helvetica,sans-serif;
  font-size:16px;
}

.msel_test_questions {
    position: relative;
    top:20px;
    left:-8px;
}

#content > h2 {
    font-size: 26px;
    font-weight: bold;
    margin-top: 20px;
}

legend {
    font-size:18px;
    padding:8px;
}
fieldset {
    padding:8px;
}

a {
	color:#000000;
}

#head {
  height:107px;
  width:1000px;
  margin-left:auto;
  margin-right:auto;
}

#logo_container {
  width:315px;
  height:100px;
  float:left;
}

#menu_container {
  width:673px;
  height:85px;
  float:right;
  margin-right:11px;
  margin-top:0px;
}
#menu_container2 {
  float:right;
}

#menu_container ul {
	margin-top:15px;
	padding:0px;
	height:85px;
	list-style-type:none;
	font-size:12px;
}

#menu_container ul li {
	float:left;
	height:85px;
	background: url(bg_menu.gif) left bottom repeat-x;
	text-align:center;
}
#menu_container ul li.active {
	float:left;
	height:85px;
	background: url(bg_menu_active.gif) left bottom repeat-x;
}
#menu_container ul li:hover {
	float:left;
	height:85px;
	background: url(bg_menu_hover.gif) left bottom repeat-x;
}

#menu_container ul li a {
	display: block;
	padding: 0px 21px 0px 21px;
	color:black;
}

#menu_container img {
  border:1px solid #cccccc;
  display:block;
  margin:0px auto 4px auto;
  padding:0px;
}

#container_left {
  width:1000px;
  margin-left:auto;
  margin-right:auto;
  background: url(bg_left_bg.gif) left top repeat-y;
  position:relative;
}

#container_right {
  width:988px;
  background: url(bg_right_bg.gif) right top repeat-y #ffffff;
  min-height:280px;
  margin-left:12px;
}

#left_gradient {
  position:absolute;
  left:0px;
  top:0px;
  height:280px;
  width:24px;
  background: url(bg_left.gif) left top no-repeat;
}

#right_gradient {
  position:absolute;
  right:0px;
  top:0px;
  height:280px;
  width:24px;
  background: url(bg_right.gif) right top no-repeat;
}

#content {
  background:#ffffff;
  margin-left:14px;
  margin-bottom:30px;
  width:688px;
  float:left;
  min-height:280px;
}

#content_right {
  margin-left:0px;
  margin-bottom:30px;
  width:284px;
  min-height:146px;
  float:left;
  position:relative;
}

#content_right_top {
  background: url(bg_right_menu_line.gif) left bottom no-repeat #efefef;
  width:252px;
  margin-left:22px;
  min-height:41px;
  padding-left:13px;
  padding-right:15px;
  padding-bottom:10px;
}

#content_right_top div {
	padding-top:2px;
	text-align:center;
}

.right_menu_div {
  padding-left:38px;
  padding-right:23px;
}

.right_menu_div ul, .right_menu_div ul li
{
	margin: 0;
	padding: 0;
	list-style-type: none;
	display: block;
}
.right_menu_div ul
{
	border: solid 1px #E1E1E1;
	border-bottom-width: 0;
}
.right_menu_div ul li
{
	border-bottom: solid 1px #E1E1E1;
	padding:3px;
}

.right_menu_div ul li:hover
{
	border-bottom: solid 1px #E1E1E1;
	padding:3px;
	background:#e2e2e2;
}

.right_menu_div ul li.active
{
	background:#04ADCC;
}
.right_menu_div ul li.active a
{
	color:#ffffff;
}

.right_menu_div ul li a
{
	display: block;
	text-decoration: underline;
	padding: 2px 10px;
	font-size:12px;
}

.right_menu_div ul li a:hover
{
	display: block;
	text-decoration: none;
	padding: 2px 10px;
	background:#e2e2e2;
}

.right_menu_div h4.loginname {
	font-size:10px;
	border:1px solid #E1E1E1;
	padding:5px 5px;
}

.right_menu_div h4.loginname:hover {
	background:#e2e2e2;
}

.right_menu_div h4.loginname a {
	display:block;
}

.clear {
  clear:both;
}

#footer {
  margin-left:auto;
  margin-right:auto;
  width:1000px;
  min-height:60px;
  background: url(footer.gif) left top repeat-y;
  font-size:11px;
  color:#999999;
  line-height:15px;
  position:relative;
}
#footer a {
  color:#999999;
}

#footer_shadow {
  margin-left:auto;
  margin-right:auto;
  width:1000px;
  height:45px;
  background: url(footer_shadow.gif) left top no-repeat #e0e9ed;
}
#footer div {
  float:right;
  width:330px;
}

#footer #footer_top {
	position:absolute;
	top:0px;
	left:300px;
	background:#E0E9ED;
	width:150px;
	height:23px;
	text-align:center;
	padding-top:4px;
}

#footer .footer_text {
  float:left;
  width:700px;
  margin-left:20px;
  margin-top:30px;
}

#footer .te_logo {
  width:270px;
  margin-left:0px;
}

#footer .te_logo div {
  float:right;
  width:175px;
  padding-top:25px;
}

#footer .te_logo img {
  padding:10px 0 10px 0;
  border:none;
}

h1 {
	margin:0px;
}

.divider {
	display:none;
}

#content ul {
	margin-left:30px;
}

.form_fieldset, .userlist, .questionlist, .itemlist {
	background-color:#F3F7F9;
	border-color:#C7D9E1;
}

#menu_container .menu_img {
	width:55px;
	height:55px;
	border:0px;
}

.questionlist.media {
	background:#FfFfe9;
}
