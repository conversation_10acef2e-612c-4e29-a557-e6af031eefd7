<?php
namespace Mixedtype\FormsNew;

use Illuminate\Support\Facades\Blade;
use Illuminate\Support\ServiceProvider;

class FormsNewServiceProvider extends ServiceProvider
{
    public function boot()
    {
        $this->loadRoutesFrom(__DIR__.'/../routes/web.php');
        $this->loadViewsFrom(__DIR__.'/../resources/views', 'mixedtype-formsnew');
        $this->loadMigrationsFrom(__DIR__.'/../database/migrations');

        Blade::component(FormComponent\Form::class, 'xform');
        Blade::component(FieldComponent\Field::class, 'xfield');
        Blade::component(InputComponents\Input::class, 'xinput');
        Blade::component(InputComponents\Select::class, 'xselect');
        Blade::component(InputComponents\Tags::class, 'xtags');
        Blade::component(InputComponents\File::class, 'xfile');

        Blade::directive('alpineComponentClass', function() {
            return '<?php echo \Mixedtype\Components\ComponentIdManager::getComponentId() ?>';
        });

        Blade::directive('xStore', function($expression) {
            return '$store.<?php echo \Mixedtype\Components\ComponentIdManager::getComponentId() ?>';
        });
        Blade::directive('xField', function($expression) {
            return '$store.<?php echo \Mixedtype\Components\ComponentIdManager::getComponentId() ?>.fields.<?=' . $expression . '?>';
        });

    }

    public function register()
    {


    }
}
