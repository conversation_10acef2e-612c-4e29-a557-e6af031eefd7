<?php 

if(!isStamp_gFunc('av', 'AV user vlastnosti')) {
	$items_pVar = new install_item_gClass('users', false);
	$items_pVar->addFieldset_gFunc('kontakt', 'Kontaktné informácie');
	$items_pVar->addFieldset_gFunc('pracovisko', 'Pracovisko');
	$items_pVar->addFieldset_gFunc('statistics', 'Štatistické informácie');
	$items_pVar->addFieldset_gFunc('others', 'Iné');

	$items_pVar->addField_gFunc('personal', 'birth_no', 'xvarchar', 'Rodné č<PERSON>lo', array('not_null'=>'yes', 'pattern'=>'/[0-9]+/', 'len'=>11));
	$items_pVar->addField_gFunc('personal', 'date_of_birth', 'date', 'Dátum narodenia', array('not_null'=>'yes'));
	
	$items_pVar->addField_gFunc('kontakt', 'street', 'xvarchar', 'Ulica, č.d.', array('len'=>255));
	$items_pVar->addField_gFunc('kontakt', 'city', 'xvarchar', 'PSČ, Mesto', array('len'=>255));
	$items_pVar->addField_gFunc('kontakt', 'country', 'xvarchar', 'Štát', array('len'=>255));
	$items_pVar->addField_gFunc('kontakt', 'mobil', 'xvarchar', 'Mobil', array('len'=>255));
	$items_pVar->addField_gFunc('kontakt', 'icq', 'xvarchar', 'ICQ', array('len'=>255));
	$items_pVar->addField_gFunc('kontakt', 'msn', 'xvarchar', 'MSN', array('len'=>255));
	$items_pVar->addField_gFunc('kontakt', 'skype', 'xvarchar', 'Skype', array('len'=>255));
	$items_pVar->addField_gFunc('kontakt', 'facebook', 'xvarchar', 'Facebook', array('len'=>255));
	
	$items_pVar->addField_gFunc('pracovisko', 'isic', 'xvarchar', 'ITIC snr (číslo zamestnanca)', array('len'=>20, 'pattern'=>'/[0-9 ]?/'));
	$items_pVar->addField_gFunc('pracovisko', 'pracovisko_klinika', 'xvarchar', 'Pracovisko - klinika', array('len'=>255));
	$items_pVar->addField_gFunc('pracovisko', 'pracovisko_oddelenie', 'xvarchar', 'Pracovisko - oddelenie', array('len'=>255));
	$items_pVar->addField_gFunc('pracovisko', 'foto_rec', 'ximagelist', 'Oficiálne foto', array('len'=>255));
	
	$items_pVar->addField_gFunc('statistics', 'vlastni_pacienti', 'int', 'Počet vlastných pacientov');
	$items_pVar->addField_gFunc('statistics', 'konzultovani_pacienti', 'int', 'Počet konzultovaných pacientov');
	$items_pVar->addField_gFunc('statistics', 'vylieceni_pacienti', 'int', 'Doterajší pacienti (vyliečení)');
	
	$items_pVar->updateField_gFunc('personal', 'email', 'email', 'E-mailová adresa', array('len'=>'255', 'not_null'=>'no', 'order_by'=>'yes', 'sk_field_info'=>'::Zadajte svoju e-mailovú adresu.<br />Adresa sa nebude zobrazovať bežným používateľom.<br />Adresa musí byť zadaná správne, je overená overovacím emailom.'));

	$items_pVar->setFormRule_gFunc('add_item', 'date_of_birth', 'edit');
	$items_pVar->setFormRule_gFunc('add_item', 'birth_no', 'edit');
	$items_pVar->setFormRule_gFunc('add_item', 'mobil', 'edit');
	$items_pVar->setFormRule_gFunc('add_item', 'street', 'edit');
	$items_pVar->setFormRule_gFunc('add_item', 'city', 'edit');
	$items_pVar->setFormRule_gFunc('add_item', 'country', 'edit');
	$items_pVar->setFormRule_gFunc('add_item', 'pracovisko_klinika', 'edit');
	$items_pVar->setFormRule_gFunc('add_item', 'pracovisko_oddelenie', 'edit');
	$items_pVar->setFormRule_gFunc('add_item', 'isic', 'edit');
	$items_pVar->setFormRule_gFunc('add_item', 'icq', 'edit');
	$items_pVar->setFormRule_gFunc('add_item', 'msn', 'edit');
	$items_pVar->setFormRule_gFunc('add_item', 'skype', 'edit');
	$items_pVar->setFormRule_gFunc('add_item', 'facebook', 'edit');
	
	$items_pVar->setFormRule_gFunc('edit_item', 'date_of_birth', 'edit');
	$items_pVar->setFormRule_gFunc('edit_item', 'birth_no', 'edit');
	$items_pVar->setFormRule_gFunc('edit_item', 'mobil', 'edit');
	$items_pVar->setFormRule_gFunc('edit_item', 'street', 'edit');
	$items_pVar->setFormRule_gFunc('edit_item', 'city', 'edit');
	$items_pVar->setFormRule_gFunc('edit_item', 'country', 'edit');
	$items_pVar->setFormRule_gFunc('edit_item', 'pracovisko_klinika', 'edit');
	$items_pVar->setFormRule_gFunc('edit_item', 'pracovisko_oddelenie', 'edit');
	$items_pVar->setFormRule_gFunc('edit_item', 'isic', 'edit');
	$items_pVar->setFormRule_gFunc('edit_item', 'icq', 'edit');
	$items_pVar->setFormRule_gFunc('edit_item', 'msn', 'edit');
	$items_pVar->setFormRule_gFunc('edit_item', 'skype', 'edit');
	$items_pVar->setFormRule_gFunc('edit_item', 'facebook', 'edit');
	$items_pVar->setFormRule_gFunc('edit_item', 'vlastni_pacienti', 'static');
	$items_pVar->setFormRule_gFunc('edit_item', 'konzultovani_pacienti', 'static');
	$items_pVar->setFormRule_gFunc('edit_item', 'vylieceni_pacienti', 'static');	
	
	$items_pVar->apply_gFunc();
	
	$items_pVar = new install_item_gClass('users', false);
	$items_pVar->addForm_gFunc('add_pacient', 'Pridanie pacienta', 'add_item');
	$items_pVar->addForm_gFunc('edit_pacient', 'Editovanie pacienta', 'edit_item');
	$items_pVar->setFormRule_gFunc('add_pacient', 'nick', 'none');
	$items_pVar->setFormRule_gFunc('add_pacient', 'login', 'none');
	$items_pVar->setFormRule_gFunc('add_pacient', 'timeout', 'none');
	$items_pVar->setFormRule_gFunc('add_pacient', 'password', 'none');
	$items_pVar->setFormRule_gFunc('add_pacient', 'user_role', 'none');
	$items_pVar->setFormRule_gFunc('add_pacient', 'isic', 'none');
	$items_pVar->setFormRule_gFunc('add_pacient', 'pracovisko_klinika', 'none');
	$items_pVar->setFormRule_gFunc('add_pacient', 'pracovisko_oddelenie', 'none');
	$items_pVar->setFormRule_gFunc('add_pacient', 'foto_rec', 'none');
	$items_pVar->setFormRule_gFunc('add_pacient', 'foto', 'none');
	
	$items_pVar->setFormRule_gFunc('edit_pacient', 'user_role', 'none');
	$items_pVar->setFormRule_gFunc('edit_pacient', 'isic', 'none');
	$items_pVar->setFormRule_gFunc('edit_pacient', 'pracovisko_klinika', 'none');
	$items_pVar->setFormRule_gFunc('edit_pacient', 'pracovisko_oddelenie', 'none');
	$items_pVar->setFormRule_gFunc('edit_pacient', 'vlastni_pacienti', 'none');
	$items_pVar->setFormRule_gFunc('edit_pacient', 'konzultovani_pacienti', 'none');
	$items_pVar->setFormRule_gFunc('edit_pacient', 'vylieceni_pacienti', 'none');	
	
	
	
	$items_pVar->add_right_gFunc('insert', 's_users_add_pacient','user_role=pacient');
	$items_pVar->add_right_gFunc('update', 's_users_edit_pacient','user_role=pacient');
	$items_pVar->add_right_gFunc('update_from', 's_users_edit_pacient','user_role=pacient');
	
	$items_pVar->apply_gFunc();
	
	
	db_items_gClass::applyFieldsToDataTable_gFunc('users');

	stamp_gFunc('av', 'AV user vlastnosti');
}

if(!isStamp_gFunc('av', 'AV user vlastnosti2')) {
	$items_pVar = new install_item_gClass('users', false);
	$items_pVar->addFieldset_gFunc('notes', 'Poznámky');
	$items_pVar->addField_gFunc('notes', 'notes', 'xtext', 'Poznámky');
	$items_pVar->setFormRule_gFunc('edit_item', 'notes', 'edit');
	$items_pVar->apply_gFunc();	
	db_items_gClass::applyFieldsToDataTable_gFunc('users');
	stamp_gFunc('av', 'AV user vlastnosti2');
}

if(!isStamp_gFunc('av', 'AV roly vytvorenie')) {
	$items_pVar = new install_item_gClass('users', false);
	$items_pVar->addEnum_gFunc('user_role', 'lekar', 'Lekár');
	$items_pVar->addEnum_gFunc('user_role', 'sestra', 'Sestra');
	$items_pVar->addEnum_gFunc('user_role', 'pacient', 'Pacient');
	$items_pVar->deleteEnum_gFunc('user_role', 'user');
	$items_pVar->apply_gFunc();
	
	db_items_gClass::applyFieldsToDataTable_gFunc('users');

	stamp_gFunc('av', 'AV roly vytvorenie');
}


return(true);