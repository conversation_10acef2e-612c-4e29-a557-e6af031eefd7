<?php

class list_questions_filter_gClass extends source_gClass
{

    protected function getData()
    {
        $filter_pVar = array();
        $filter_pVar['params'] = array();
        $filter_pVar['tree'] = db_items_gClass::getTree_gFunc('test_questions', 'zaradenie');
        $filter_pVar['fields'] = array_flip($filter_pVar['tree']['_defs']);
        foreach ($filter_pVar['tree']['_defs'] as $k_pVar=>$v_pVar) {
            if(main_gClass::getSessionData_gFunc('resetfilter')) {
                main_gClass::unsetPhpSessionVar_gFunc('list_questions_filter_' . $k_pVar);
            }
            $tmp_pVar = main_gClass::getSessionData_gFunc('list_questions_filter_' . $k_pVar, null);
            if($tmp_pVar !== null) {
                $filter_pVar['params'][$k_pVar] = $tmp_pVar;
            }
            $tmp_pVar = main_gClass::getInputString_gFunc($k_pVar, main_gClass::SRC_GETPOST_pVar, false, null);
            if($tmp_pVar !== null) {
                if($tmp_pVar === '*') {
                    main_gClass::unsetPhpSessionVar_gFunc('list_questions_filter_' . $k_pVar);
                    unset($filter_pVar['params'][$k_pVar]);
                }
                else {
                    $filter_pVar['params'][$k_pVar] = $tmp_pVar;
                    main_gClass::setPhpSessionVar_gFunc('list_questions_filter_' . $k_pVar, $tmp_pVar);
                }
            }
        }

        $filter_pVar['filter'] = '';
        foreach ($filter_pVar['params'] as $k_pVar=>$v_pVar) {
            $filter_pVar['filter'] .= '&' . $k_pVar . '=' . $v_pVar;
        }

        return($filter_pVar);
    }
}

class list_questions_filter extends list_questions_filter_gClass { }
