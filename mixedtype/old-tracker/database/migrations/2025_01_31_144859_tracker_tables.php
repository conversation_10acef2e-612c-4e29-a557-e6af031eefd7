<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::dropIfExists('tracker_raw');
        Schema::dropIfExists('tracker_sums');
        Schema::dropIfExists('tracker_tags');

        Schema::create('tracker_tags', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->string('tag')->index();
        });

        Schema::create('tracker_raw', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->unsignedBigInteger('parent_id')->nullable()->default(null);

            $table->string('type'); // etc. app, db, exception
            $table->string('tag');

            $table->bigInteger('tag_id')->nullable()->default(null);

            $table->string('request_id', 32);
            $table->string('parent_request_id', 32)->nullable()->default(null);
            $table->unsignedInteger('user_id')->default(0);

            $table->dateTime('timestamp', 6)->index();
            $table->dateTime('timestamp_end', 6)->nullable()->default(null);
            $table->unsignedInteger('duration')->nullable()->default(null);
            $table->json('data')->nullable()->default(null); // data

            $table->index(['timestamp', 'tag']);
        });



        Schema::create('tracker_sums', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->unsignedInteger('user_id')->default(0);
            $table->string('tag');

            $table->integer('year')->nullable()->default(null)->index();
            $table->integer('month')->nullable()->default(null)->index();
            $table->integer('day')->nullable()->default(null)->index();
            $table->integer('hour')->nullable()->default(null)->index();
            $table->integer('min')->nullable()->default(null)->index();
            $table->integer('weekday')->nullable()->default(null)->index();

            $table->bigInteger('item_id')->nullable()->default(null);

            $table->unsignedInteger('sum_duration')->nullable()->default(null);
            $table->unsignedInteger('avg_duration')->nullable()->default(null);
            $table->unsignedInteger('min_duration')->nullable()->default(null);
            $table->unsignedInteger('max_duration')->nullable()->default(null);
            $table->unsignedInteger('count')->nullable()->default(null);
            $table->unsignedInteger('sum1')->nullable()->default(null);
            $table->unsignedInteger('sum2')->nullable()->default(null);
            $table->unsignedInteger('avg1')->nullable()->default(null);
            $table->unsignedInteger('avg2')->nullable()->default(null);
            $table->unsignedInteger('min1')->nullable()->default(null);
            $table->unsignedInteger('min2')->nullable()->default(null);
            $table->unsignedInteger('max1')->nullable()->default(null);
            $table->unsignedInteger('max2')->nullable()->default(null);



        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('tracker_raw');
        Schema::dropIfExists('tracker_sums');
    }
};
