<?php
if(isset($_SERVER['SCRIPT_FILENAME']) && strtolower($_SERVER['SCRIPT_FILENAME']) == strtolower(str_replace("\\",'/',__FILE__))) { header("HTTP/1.0 404 Not Found");  return(false); }
if(isset($GLOBALS['file__rss_pVar'])) return(true);
$GLOBALS['file__rss_pVar']=true;

if(!modules_gClass::isModuleRegistred_gFunc('db'))
{
    return(false);
}

class rss_gClass
{
	private $rssName_pVar;
	private $rssLink_pVar;
	private $rssDescription_pVar;
	private $data_pVar;
	
	function __construct($rssName_pVar)
	{
		$rssName_pVar = 'kega';
		
		$this->rssName_pVar = $rssName_pVar;
		$this->rssLink_pVar = 'http://www.kega.sk';
		$this->data_pVar = $this->getData_gFunc();
	}
	
	function getRss_gFunc($version_pVar = 2)
	{
		return($this->getRss20_gFunc());
	}
	
	function getRss20_gFunc()
	{
		$ret_pVar = '<' . '?' . 'xml version="1.0" encoding="utf-8" ?' . '>' . NL;
		$ret_pVar .= '<' . '!' . '-- RSS generated by TruEngine.sk -->' . NL;
		$ret_pVar .= '<rss version="2.0">' . NL;
		$ret_pVar .= ' <channel>' . NL;
		$ret_pVar .= '  <title>' . $this->rssName_pVar . '</title>' . NL;
		$ret_pVar .= '  <link>' . htmlspecialchars($this->rssLink_pVar) . '</link>' . NL;
		$ret_pVar .= '  <description>' . $this->rssDescription_pVar . '</description>' . NL;
		$ret_pVar .= '  <generator>www.TruEngine.sk</generator>' . NL;
		
		foreach($this->data_pVar as $v_pVar) {
			$ret_pVar .= '  <item>' . NL;
			$ret_pVar .= '   <title>' . htmlspecialchars($v_pVar['title']) . '</title>' . NL;
			$ret_pVar .= '   <pubDate>' . date(DATE_RFC822, strtotime($v_pVar['pubDate'])) . '</pubDate>' . NL;
			$ret_pVar .= '   <link>http://www.kega.sk/?time='.strtotime($v_pVar['pubDate']) . '</link>' . NL;
			//$ret_pVar .= '   <pubDate>' . gmdate(DATE_RFC822, strtotime($v_pVar['pubDate'])) . '</pubDate>' . NL;
			//$ret_pVar .= '   <description>' . htmlspecialchars($v_pVar['description']) . '</description>' . NL;
			$ret_pVar .= '  </item>' . NL;
		}
		
		$ret_pVar .= ' </channel>' . NL;
		$ret_pVar .= '</rss>' . NL;
		return($ret_pVar);
	}
	
	private function getData_gFunc()
	{
		/*
		$sql_pVar = 'SELECT * FROM `%titems_test_questions__changes` as `ch` 
						LEFT JOIN `%titems_test_questions__change_details` as `chd`
						ON `ch`.`change_id` = `chd`.`change_id`
					 WHERE `ch`.`change_datetime` < now() -  interval 30 day
					 ORDER BY `ch`.`change_datetime` DESC';
					 */
		
		$sql_pVar = 'SELECT * FROM `%titems_test_questions__changes` as `ch` 
			LEFT JOIN %titems_test_questions__data as d
			on d.item_id=ch.item_id
		
		WHERE change_type IN (\'insert\', \'insertcomment\') ORDER BY change_datetime desc LIMIT 0,50';
		
		$tmp_pVar = db_public_gClass::getResultArray_gFunc($sql_pVar, __FILE__, __LINE__, false);
		foreach($tmp_pVar as $k_pVar=>$v_pVar) {
			if($tmp_pVar[$k_pVar]['change_type'] === 'insert') {
				$tmp_pVar[$k_pVar]['title'] = 'Vytvorená nová otázka. ('.$tmp_pVar[$k_pVar]['change_datetime'].')';
			}
			if($tmp_pVar[$k_pVar]['change_type'] === 'insertcomment') {
				$tmp_pVar[$k_pVar]['title'] = 'Pridaný nový komentár. ('.$tmp_pVar[$k_pVar]['change_datetime'].')';
			}
			$tmp_pVar[$k_pVar]['pubDate'] = $tmp_pVar[$k_pVar]['change_datetime'];
		}
		return($tmp_pVar);
	}
	
	
}

class rssSource_gClass extends source_gClass
{
	function getData()
	{
		$rss_pVar = new rss_gClass('kega');
		$data_pVar = $rss_pVar->getRss_gFunc();
		
		return(array('xml'=>$data_pVar));
	}
}

class rssSource extends rssSource_gClass {}

return(true);
