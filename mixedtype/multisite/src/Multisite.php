<?php

namespace Mixedtype\Multisite;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

class Multisite
{

    /**
     * Get all sites - used for generating routes
     */
    static function getSitesForRouting() : array
    {
        $allSites = config('mixedtype.multisite.sites');
        if(!$allSites) {
            throw new \Exception('No sites defined in config/mixedtype/multisite.php');
        }

        return $allSites;
    }

    private static function getConfigValueForSiteAndLanguage($configArray, $configKey, $siteKey, $languageKey, $defaultValue = null)
    {
        // site:language:key
        $fullPath = implode(':', [$siteKey, $languageKey, $configKey]);
        if(isset($configArray[$fullPath])) {
            return $configArray[$fullPath];
        }

        // language:key
        $langPath = implode(':', [$languageKey, $configKey]);
        if(isset($configArray[$langPath])) {
            return $configArray[$langPath];
        }

        // key
        if(isset($configArray[$configKey])) {
            return $configArray[$configKey];
        }

        return $defaultValue;
    }

    static function registerRawRoutes($closure)
    {
        foreach(self::getSitesForRouting() as $siteKey => $site) {
            foreach($site['languages'] as $languageKey => $language) {
                Route::domain($language['domain'])->group(function () use ($siteKey, $languageKey, $language, $closure) {
                    Route::group(['middleware' => ['web'], 'prefix' => $language['path']], function () use ($siteKey, $languageKey, $language, $closure) {
                        $namePrefix = implode('.', [$siteKey, $languageKey, '']);
                        $closure($namePrefix);
                    });
                });
            }
        }
    }

    static function registerRoutes($configRoutesArray)
    {
        foreach(self::getSitesForRouting() as $siteKey => $site) {
            foreach($site['languages'] as $languageKey => $language) {
                Route::domain($language['domain'])->group(function () use ($siteKey, $languageKey, $language, $configRoutesArray) {
                    Route::group(['middleware' => ['web'], 'prefix' => $language['path']], function () use ($siteKey, $languageKey, $language, $configRoutesArray) {
                        foreach ($configRoutesArray as $routeKey => $route) {
                            $uri = static::getConfigValueForSiteAndLanguage($route, 'uri', $siteKey, $languageKey);
                            if($uri === null) {
                                continue;
                            }

                            $view = static::getConfigValueForSiteAndLanguage($route, 'view', $siteKey, $languageKey);
                            if($view) {

                                $all_methods = static::getConfigValueForSiteAndLanguage($route, 'all_methods', $siteKey, $languageKey);

                                if($all_methods) {
                                    Route::any($uri, function () use ($view) {
                                        return view($view);
                                    })->name(implode('.', [$siteKey, $languageKey, $routeKey]));
                                }
                                else {
                                    Route::view($uri, $view)
                                        ->name(implode('.', [$siteKey, $languageKey, $routeKey]));
                                }

                                continue;
                            }

                            $any = static::getConfigValueForSiteAndLanguage($route, 'any', $siteKey, $languageKey);
                            if($any) {
                                Route::any($uri, $any)
                                    ->name(implode('.', [$siteKey, $languageKey, $routeKey]));
                                continue;
                            }

                            $get = static::getConfigValueForSiteAndLanguage($route, 'get', $siteKey, $languageKey);
                            if($get) {
                                Route::get($uri, $get)
                                    ->name(implode('.', [$siteKey, $languageKey, $routeKey]));
                                continue;
                            }

                        }
                    });
                });
            }
        }
    }

    /**
     * Detect site from request
     * Foreach all sites, check if domain and path matches. Return site with longest matching path
     */
    static public function getCurrentSiteConfigFromRequest(Request $request): ?array
    {
        $host = $request->getHost();
        $path = $request->getPathInfo();

        $results = collect();
        $allSites = config('mixedtype.multisite.sites');
        if(!$allSites) {
            throw new \Exception('No sites defined in config/multisite.php');
            return null;
        }

        $possibleReditects = collect();

        foreach($allSites as $siteTag => $site) {
            foreach($site['languages'] as $language) {
                if($language['domain'] !== $host) {
                    continue;
                }

                $possibleReditects->push($language);

                if($language['path'] !== substr($path, 0, strlen($language['path']))) {
                    continue;
                }

                $p = rtrim($language['path'], '/');
                if(strlen($p) !== strlen($path)) {
                    if($path[strlen($p)] !== '/') {
                        continue;
                    }
                }

                $language['site'] = $siteTag;

                $results->push($language);
            }
        }

        if($results->isEmpty()) {
            if($possibleReditects->isNotEmpty()) {
                return [
                    'redirect' => $possibleReditects->first()['path']
                ];
            }

            throw new \Exception('Site not found for domain ' . $host . ' and path ' . $path);
        }

        $results = $results->sortByDesc(function($value, $key) {
            return strlen($value['path']);
        });

        return $results->first();
    }

    static public function getLanguage($request)
    {
        $site = self::getCurrentSiteConfigFromRequest($request);
        return $site ? $site['locale'] : null;
    }

    static function getCurrentLanguage()
    {
        return self::getLanguage(request());
    }


    /**
     * Get route for current site and language
     */
    static public function route($routeName, $language = null, $site = null, $parameters = [], $absolute = true)
    {
        if(!$site) {
            $site = self::getCurrentSiteConfigFromRequest(request())['site'];
        }
        if(!$language) {
            $language = self::getLanguage(request());
        }
        return route(self::getFullRouteName($routeName, $language, $site), $parameters, $absolute);
    }

    static function getFullRouteName($siteRouteName, $language = null, $site = null)
    {
        if(!$site) {
            $site = self::getCurrentSiteConfigFromRequest(request())['site'];
        }
        if(!$language) {
            $language = self::getLanguage(request());
        }

        return implode('.', [$site, $language, $siteRouteName]);
    }

    static function redirect($siteRouteName, $language = null, $site = null, $parameters = [], $absolute = true)
    {
        return redirect(self::route($siteRouteName, $language, $site, $parameters, $absolute));
    }
}
