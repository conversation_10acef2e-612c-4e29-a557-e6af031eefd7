<?php



class itemsAdapter_test_instance extends itemsAdapter_gClass
{
    function getItemsFields_gFunc()
    {
        $fields_pVar = array(
            array('tag' => 'time_first',
                'type' => 'xvarchar',
                'sk_name' => 'Čas spustenia'
            ),
            array('tag' => 'test_name',
                'type' => 'xvarchar',
                'sk_name' => 'Názov testu'
            ),
            array('tag' => 'score',
                'type' => 'integer',
                'sk_name' => 'Počet bodov'
            ),
            array('tag' => 'name',
                'type' => 'user_id',
                'sk_name' => 'Meno'
            )
        );

        return ($fields_pVar);
    }

    function getEnumFieldsValues_gFunc($where_str_pVar = '', $where_data_pVar = array(), $columns_pVar = '*', $index_by_pVar = false)
    {
        return (array());
    }

    function getItems_checkRights_gFunc($filter_pVar)
    {
        if (isset($filter_pVar['user_id']) && $filter_pVar['user_id'] == session_gClass::getUserDetail_gFunc('user_id')) {
            return (session_gClass::userHasRightsAccess_gFunc(s_test_list_my_instances)
                || session_gClass::userHasRightsAccess_gFunc(s_test_list_instances));
        }
        return (session_gClass::userHasRightsAccess_gFunc(s_test_list_instances));
    }

    protected function _pagerData_gFunc($where_str_pVar, $where_data_pVar, $order_by_pVar, $offset_pVar, $pageLen_pVar)
    {
        $sql_pVar = 'UPDATE `%ttests_running` SET status = \'deleted\' WHERE time_first IS NULL AND time_create < date_sub(now(), interval 1 day)';
        db_public_gClass::execute_gFunc($sql_pVar, __FILE__, __LINE__);

        $where_str_pVar = str_replace('`D`.`test_name`', '`t`.`sk_name`', $where_str_pVar);
        $where_str_pVar = str_replace('`D`.`item_id`', '`D`.`id`', $where_str_pVar);
        $where_str_pVar = str_replace('`D`.`name`', '`name`', $where_str_pVar);
        $order_by_pVar = str_replace('`D`.`test_name`', '`t`.`sk_name`', $order_by_pVar);
        $order_by_pVar = str_replace('`D`.`item_id`', '`D`.`id`', $order_by_pVar);
        $order_by_pVar = str_replace('`D`.`name`', '`name`', $order_by_pVar);
        $sql_pVar = 'SELECT `D`.`id`,
							CONCAT_WS(\' \', `u`.`last_name`, `u`.`first_name`) as `name`,
							`D`.`source_template_id` as `source_test_id`,
							concat_ws(\'\', `t`.`sk_name`, `tt`.`sk_name`) as `test_name`,
							`D`.`score`,
							`D`.`time_first`
						FROM `%ttests_running` as `D`
						LEFT JOIN `%titems_users__data` as `u` ON `D`.`user_id` = `u`.`item_id`
						LEFT JOIN `%titems_test_templates__data` as `tt` ON `D`.`source_template_id` = `tt`.`item_id`
						LEFT JOIN `%ttests` as `t` ON `D`.`source_test_id` = `t`.`id`';
        $sql_pVar .= $where_str_pVar;
        $sql_pVar .= $order_by_pVar;
        $data_pVar = db_public_gClass::getResultArray_gFunc($sql_pVar, __FILE__, __LINE__, $where_data_pVar);

        foreach ($data_pVar as $k_pVar => $v_pVar) {
            $data_pVar[$k_pVar]['name'] = '<a href="' . main_gClass::makeUrl_gFunc('/testy/test?test_id=' . $data_pVar[$k_pVar]['id']) . '">' . $data_pVar[$k_pVar]['name'] . '</a>';
            $data_pVar[$k_pVar]['test_name'] = '<a href="' . main_gClass::makeUrl_gFunc('/testy/test?test_id=' . $data_pVar[$k_pVar]['id']) . '">' . $data_pVar[$k_pVar]['test_name'] . '</a>';
        }

        $data_pVar['_format_handlers'] = $this->_getFormatHandlers_gFunc();

        return ($data_pVar);

    }

    protected function _nonPagerData_gFunc($where_str_pVar, $where_data_pVar, $order_by_pVar)
    {
        return (array());
    }

    protected function _getCountData_gFunc($where_str_pVar, $where_data_pVar)
    {
        return (0);
    }

    protected function _getOrderByFields_gFunc()
    {
        return (array('time_first' => 'Čas spustenia', 'test_name' => 'Názov testu', 'score' => 'Počet bodov', 'name' => 'Meno'));
    }

    protected function _getFormatHandlers_gFunc()
    {
        return (array('time_first' => array('itemsAdapter_test_instance', 'formatTime_gFunc')));
    }

    static public function formatTime_gFunc($value_pVar)
    {
        if (empty($value_pVar)) {
            return ($value_pVar);
        }
        $id_pVar = uniqid('time_');
        return ('' . $value_pVar . '<span id="' . $id_pVar . '"></span><script type="text/javascript">$j(\'#' . $id_pVar . '\').parent().html(datetime(' . strtotime($value_pVar) . '));</script>');
    }

    function getItemsFilterFields_gFunc()
    {
        return [];
    }

}

