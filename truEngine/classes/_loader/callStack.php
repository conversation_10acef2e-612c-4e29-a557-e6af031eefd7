<?php


/**
 * stack pri volani include...
 * obsahuje:
 *  - stak include
 *  - stack na layouty pre aktualny include (array push/pop)
 *  - pole contents pre aktualny include
 */
class callStack_gClass
{
    static private $printing_pVar;
    static private $stacks_pVar = array();
    static private $lastStackId_pVar = -1;
    private $layouts_pVar = array();
    private $lastLayoutId_pVar = -1;
    private $docName_pVar = '';
    private $docPath_pVar = false;
    private $docObject_pVar = false;

    private function __construct()
    {

    }

    static function printStart_gFunc()
    {
        self::$printing_pVar = true;
    }

    static function printEnd_gFunc()
    {
        self::$printing_pVar = false;
    }

    static function isPrinting_gFunc()
    {
        return(self::$printing_pVar);
    }

    static function getDocName_gFunc()
    {
        return(self::$stacks_pVar[self::$lastStackId_pVar]->docName_pVar);
    }

    static function setDocObject_gFunc($object, $overwrite_pVar = false)
    {
        if(self::$stacks_pVar[self::$lastStackId_pVar]->docObject_pVar === false || $overwrite_pVar) {
            self::$stacks_pVar[self::$lastStackId_pVar]->docObject_pVar = &$object;
        }
    }

    static function &getDocObject_gFunc()
    {
        $ret_pVar = &self::$stacks_pVar[self::$lastStackId_pVar]->docObject_pVar;
        return($ret_pVar);
    }

    static function getDocPath_gFunc()
    {
        if(self::$stacks_pVar[self::$lastStackId_pVar]->docPath_pVar !== false) {
            return(self::$stacks_pVar[self::$lastStackId_pVar]->docPath_pVar);
        }
        $path_pVar = self::$stacks_pVar[self::$lastStackId_pVar]->docName_pVar;
        if(substr($path_pVar, -1) === '/') {
            // odstranim lomitko z konca, ak existuje
            $path_pVar = substr($path_pVar, 0, -1);
        }
        // teraz odstranim poslednu cast cesty
        $p_pVar =strrpos($path_pVar, '/');
        if($p_pVar) { // ani false ani 0
            $path_pVar = substr($path_pVar, 0, $p_pVar); // odstranim koniec aj s lomitkom
        }
        else {
            $path_pVar = ''; // ma iba jednu cast, takze odstranim cele
        }
        $path_pVar .= '/';
        self::$stacks_pVar[self::$lastStackId_pVar]->docPath_pVar = $path_pVar;
        return($path_pVar);
    }

    static function execute_gFunc($docName_pVar, $extraContent_pVar = false, $vars_pVar = false, $systemCall_pVar = false)
    {
        if(!session_gClass::userHasRightsAccessAction_gFunc(s_document_read, false, 0, $docName_pVar)) {
            return;
        }
        if(self::$lastStackId_pVar === 0) {
            $isRootCall_pVar = true;
        }
        else {
            $isRootCall_pVar = false;
        }
        varStack_gClass::createStack_gFunc();

        if(is_array($vars_pVar)) {
            foreach($vars_pVar as $k_pVar=>$v_pVar) {
                varStack_gClass::$vars[$k_pVar] = &$vars_pVar[$k_pVar];
            }
        }

        // nastavim nazov dokumentu, aby ho mohli funkcie zistit
        self::$stacks_pVar[self::$lastStackId_pVar]->docName_pVar = $docName_pVar;

        if(session_gClass::ajaxRequest_gFunc() || strpos($docName_pVar, 'rss') !== false) {
            $layoutOff_pVar = true;
        }

        // spracujem dokument
        $doc_pVar = document_gClass::getDocumentObject_gFunc($docName_pVar, document_gClass::DOCTYPE_DOCUMENT, $systemCall_pVar);

        // nastavim objekt dokumentu, aby si ho mohli funkcie zistit
        self::$stacks_pVar[self::$lastStackId_pVar]->docObject_pVar = &$doc_pVar;

        $GLOBALS['contents']['content'] = $doc_pVar->execute_gFunc();

        if($extraContent_pVar !== false && !callStack_gClass::isPrinting_gFunc() && (!isset($layoutOff_pVar) || $layoutOff_pVar !== true)) {
            $GLOBALS['contents']['content'] .= $extraContent_pVar;
        }
        content_gClass::setContent_gFunc('content', $GLOBALS['contents']['content']);

        if($isRootCall_pVar) {
            // content dokumentu zabalim do layoutov az po spracovani vsetkych layoutov, aby som mohol nezobrazene
            // contenty pridat ku contentu
            $restoreContent_pVar = content_gClass::getContent_gFunc('content');
            $GLOBALS['contents']['content'] = '~#CONTENT#~';
            content_gClass::setContent_gFunc('content', $GLOBALS['contents']['content']);
        }
        // spracujem layouty
        $top_layout_name_pVar = '';
        while(1) {
            $layout_pVar = self::popLayout_gFunc();
            if($layout_pVar === false) {
                break;
            }

            $top_layout_name_pVar = $layout_pVar;
            if($layout_pVar == '.layouts/none') {
                $layoutOff_pVar = true;
                break;
            }
            $layout_pVar = document_gClass::getDocumentObject_gFunc($layout_pVar, document_gClass::DOCTYPE_LAYOUT);
            $GLOBALS['contents']['content'] = $layout_pVar->execute_gFunc();
            content_gClass::setContent_gFunc('content', $GLOBALS['contents']['content']);
        }
        $layout_pVar = content_gClass::getContent_gFunc('content');

        // k document-contentu pridam nezobrazene contenty
        if($isRootCall_pVar) {
            if(!isset($layoutOff_pVar) || !$layoutOff_pVar) {
                $contents_pVar = content_gClass::getOthersContents_gFunc();
                foreach ($contents_pVar as $k_pVar=>$v_pVar) {
                    $restoreContent_pVar .= $v_pVar;
                }


                if($top_layout_name_pVar == '.layouts/sk_responsive') {
                    $layout_pVar = view('layouts.legacy', [
                        'content' => $restoreContent_pVar
                    ]);
                }
                else {
                    // replace document
                    $p_pVar = strpos($layout_pVar, '~#CONTENT#~');
                    if ($p_pVar !== false) {
                        $layout_pVar = str_replace('~#CONTENT#~', $restoreContent_pVar, $layout_pVar);
                    } else {
                        $layout_pVar .= $restoreContent_pVar;
                    }
                }
            }
            else {
                $layout_pVar = $restoreContent_pVar;
            }
        }

        // koniec
        varStack_gClass::destroyStack_gFunc();

        if(preg_match('/\.layouts\/[a-z]{2}_pdf(_[a-z]+)*/i', $top_layout_name_pVar) && modules_gClass::isModuleRegistred_gFunc('pdf')) {
            $pdf_pVar = pdf_gClass::newPdf_gFunc();

            /// vycistim HTML
            $layout_pVar = preg_replace('/<script[^>]*>[^<]*<\/script>/im', '', $layout_pVar);
            $layout_pVar = str_replace("\t", ' ', $layout_pVar);
            $layout_pVar = str_replace("\n", ' ', $layout_pVar);
            $layout_pVar = str_replace("\r", ' ', $layout_pVar);
            while(strpos($layout_pVar, '  ') !== false) {
                $layout_pVar = str_replace('  ', ' ', $layout_pVar);
            }
            $layout_pVar = str_replace('> ', '>', $layout_pVar);
            $layout_pVar = str_replace(' <', '<', $layout_pVar);

            if($docName_pVar == 'sk/otazky' || $docName_pVar == 'sk/predotazky') {
                $layout_pVar = str_replace('class="label_1"', 'style="width:50px;"', $layout_pVar);
                $layout_pVar = str_replace('class="label"', 'style="width:80px;"', $layout_pVar);
            }

            //log_gClass::write_gFunc('PDF_DATA', $layout_pVar);

            $pdf_pVar->writeHTML_gFunc($layout_pVar);

            $pdf_pVar->output_gFunc($docName_pVar);
        }
        else {
            echo($layout_pVar);
        }

        if(isset($doc_pVar->result_pVar)) {
            return($doc_pVar->result_pVar);
        }
        else {
            return(true);
        }
    }

    /**
     * includuje dokument.
     * Layouty sa daj nastavit az includu. Ak chcem nastavit layout z rodicovskeho dokumentu,
     * musim pouzit volania:
     *      - includePrepare_gFunc
     *      - pushLayout_gFunc /insertLayout_gFunc/
     *      ...
     *      - includeEnd_gFunc($docName_pVar)
     *
     * @param unknown_type $docName_pVar
     */
    static function include_gFunc($docName_pVar, $vars_pVar = false)
    {
        self::createNewStack_gFunc();
        self::execute_gFunc($docName_pVar, false, $vars_pVar);
        self::destroyLastStack_gFunc();
    }

    /**
     * vid komentar pri metode include_gFunc
     */
    static function includePrepare_gFunc()
    {
        self::createNewStack_gFunc();
    }

    /**
     * vid komentar pri metode include_gFunc
     */
    static function includeEnd_gFunc($docName_pVar, $vars_pVar = false)
    {
        ///////DEBUG
        //echo '<div style="position:absolute; background:#fcc;z-index:999999; outline:1px dotted red;">'.$docName_pVar.'</div>';
        self::execute_gFunc($docName_pVar, false, $vars_pVar, true);
        self::destroyLastStack_gFunc();
    }

    static function resetStacks_gFunc()
    {
        self::$stacks_pVar = array();
        self::$lastStackId_pVar = -1;
        self::createNewStack_gFunc();
    }

    static function destroyLastStack_gFunc()
    {
        if(self::$stacks_pVar[self::$lastStackId_pVar]->docObject_pVar !== false) {
            // zrusim referenciu
            unset(self::$stacks_pVar[self::$lastStackId_pVar]->docObject_pVar);
        }
        if(self::$lastStackId_pVar > -1) {
            unset(self::$stacks_pVar[self::$lastStackId_pVar]);
            self::$lastStackId_pVar--;
        }
    }

    static function &createNewStack_gFunc()
    {
        self::$lastStackId_pVar++;
        self::$stacks_pVar[self::$lastStackId_pVar] = new callStack_gClass();
        $ret_pVar = &self::$stacks_pVar[self::$lastStackId_pVar];
        return($ret_pVar);
    }

    static function &getCurrentStack_gFunc()
    {
        $ret_pVar = &self::$stacks_pVar[self::$lastStackId_pVar];
        return($ret_pVar);
    }

    static function pushLayout_gFunc($name_pVar)
    {
        if(!isset(self::$stacks_pVar[self::$lastStackId_pVar])) {
            error_gClass::fatal_gFunc(__FILE__,__LINE__);
        }
        $x_pVar = &self::$stacks_pVar[self::$lastStackId_pVar];
        $x_pVar->lastLayoutId_pVar++;
        $x_pVar->layouts_pVar[$x_pVar->lastLayoutId_pVar] = $name_pVar;
        unset($x_pVar);
    }

    static function insertLayout_gFunc($name_pVar)
    {
        self::$stacks_pVar[self::$lastStackId_pVar]->pushLayout_gFunc($name_pVar);
    }

    static function popLayout_gFunc()
    {
        $x_pVar = &self::$stacks_pVar[self::$lastStackId_pVar];
        if($x_pVar->lastLayoutId_pVar < 0) {
            unset($x_pVar);
            return(false);
        }
        $ret_pVar = $x_pVar->layouts_pVar[$x_pVar->lastLayoutId_pVar];
        unset($x_pVar->layouts_pVar[$x_pVar->lastLayoutId_pVar]);
        $x_pVar->lastLayoutId_pVar--;
        unset($x_pVar);
        return($ret_pVar);
    }

    static function getDocContent_gFunc($docName_pVar, $vars_pVar)
    {
        ob_start();
        callStack_gClass::createNewStack_gFunc();
        callStack_gClass::execute_gFunc($docName_pVar, false, $vars_pVar, true);
        callStack_gClass::destroyLastStack_gFunc();
        $content_pVar = ob_get_contents();
        ob_end_clean();
        return($content_pVar);
    }
}

