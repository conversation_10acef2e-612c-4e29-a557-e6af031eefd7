<?php


class session_check_email_gClass extends source_gClass
{
    protected function getData()
    {
        // skontrolujem platnost linky
        $type_pVar = 'error';
        if(isset($this->params['hash']) && isset($this->params['email']) && isset($this->params['user_id'])) {
            $sql_pVar = 'SELECT * FROM `%taccess__check_mail` WHERE `hash` = %s AND `email`= %s AND `user_id` = %d AND `destroy_time` > now() AND `user_id_checked` IS NULL';
            $data_pVar = db_public_gClass::getResult_gFunc($sql_pVar, __FILE__, __LINE__, array($this->params['hash'], $this->params['email'], $this->params['user_id']));
            $type_pVar = 'ok';
            $request_id_pVar = isset($data_pVar['request_id']) ? $data_pVar['request_id']: null;
        }

        if(!is_array($data_pVar) || !count($data_pVar) || $type_pVar == 'error') {
            return(array('type'=>'error'));
        }

        $ret_pVar = array();
        session_gClass::giveMeFullAccess_gFunc();
        $user_pVar = items_gClass::getItem_gFunc('users', intval($this->params['user_id']));
        session_gClass::revokeMeFullAccess_gFunc();

        if(!$user_pVar || !is_array($user_pVar)) {
            return(array('type'=>'error'));
        }

        if($user_pVar['email'] != $this->params['email']) {
            return(array('type'=>'error'));
        }

        session_gClass::giveMeFullAccess_gFunc();
        $user_data_pVar = array('item_id'=>$user_pVar['item_id'], 'email_check_status'=>'checked');
        items_gClass::saveOrUpdateItem_gFunc('users', $user_data_pVar);
        session_gClass::revokeMeFullAccess_gFunc();

        $str_pVar = '';
        if(isset($user_pVar['isic_ok']) && $user_pVar['isic_ok'] == 'no') {
            if(modules_gClass::isModuleRegistred_gFunc('cdouk')) {
                session_gClass::giveMeFullAccess_gFunc();
                $cdouk_pVar = new cdouk_gClass();
                $cdouk_pVar->getData_gFunc($user_pVar['isic'], false, true, true);
                $user_pVar = items_gClass::getItem_gFunc('users', intval($this->params['user_id']));
                session_gClass::revokeMeFullAccess_gFunc();
            }

            if(isset($user_pVar['isic_ok']) && $user_pVar['isic_ok'] == 'no') {
                $str_pVar .= '&isic=true';
            }
        }

        // odblokuje pristup
        db_session_gClass::resetDisableLevel_gFunc($user_pVar['item_id']);

        // zrusim platnost hashu
        $sql_pVar = 'UPDATE `%taccess__check_mail` SET `user_id_checked` = %d WHERE `request_id` = %d';
        db_public_gClass::execute_gFunc($sql_pVar, __FILE__, __LINE__, array($user_pVar['item_id'], $request_id_pVar));

        $ret_pVar['status'] = $str_pVar;

        return($ret_pVar);
    }
}

class session_check_email extends session_check_email_gClass {}

