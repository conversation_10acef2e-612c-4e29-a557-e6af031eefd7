<?php


class list_literatura_gClass extends source_gClass
{

    protected function getData()
    {
        $filter_pVar = $this->params;
        $literatura_pVar = items_gClass::getItems_gFunc('literatura', $filter_pVar);

        foreach($literatura_pVar as $k=>$v) {
            $sql_pVar = 'SELECT literatura_strana FROM %titems_test_questions__data WHERE literatura_md5 = %s';
            if(!isset($v['qmd5'])) {
                continue;
            }
            $pages = db_public_gClass::getResultArray_gFunc($sql_pVar, __FILE__, __LINE__, $v['qmd5']);
            $literatura_pVar[$k]['pages'] = array();
            foreach($pages as $vv) {
                $literatura_pVar[$k]['pages'][] = $vv['literatura_strana'];
            }
            if(count($literatura_pVar[$k]['pages'])) {
                $literatura_pVar[$k]['pages'] = implode(',', $literatura_pVar[$k]['pages']);
                $literatura_pVar[$k]['pages'] = explode(',', $literatura_pVar[$k]['pages']);
                foreach($literatura_pVar[$k]['pages'] as $kk=>$vv) {
                    if(empty($vv)) {
                        unset($literatura_pVar[$k]['pages'][$kk]);
                    }
                    else {
                        $literatura_pVar[$k]['pages'][$kk] = trim($literatura_pVar[$k]['pages'][$kk]);
                        $like = $literatura_pVar[$k]['pages'][$kk];
                        $literatura_pVar[$k]['pages'][$kk] = str_replace(' ', '', $literatura_pVar[$k]['pages'][$kk]);
                        $literatura_pVar[$k]['pages'][$kk] = str_replace(' ', '', $literatura_pVar[$k]['pages'][$kk]);
                        $literatura_pVar[$k]['pages'][$kk] = str_replace(' ', '', $literatura_pVar[$k]['pages'][$kk]);
                        $literatura_pVar[$k]['pages'][$kk] = '<a href="'.main_gClass::makeUrl_gFunc('/sk/otazky/otazky&filter=literatura_md5%3DLIKE(%25'.$v['qmd5'].'%25)%26literatura_strana%3DLIKE(%25'.$like.'%25)') .'">' . $literatura_pVar[$k]['pages'][$kk] . '</a>';
                    }
                }
                if(!count($literatura_pVar[$k]['pages'])) {
                    unset($literatura_pVar[$k]['pages']);
                }
                else {
                    $literatura_pVar[$k]['pages'] = implode(', ', $literatura_pVar[$k]['pages']);
                }
            }
            else {
                unset($literatura_pVar[$k]['pages']);
            }
        }
        unset($literatura_pVar['filter']);
        return($literatura_pVar);
    }
}

class list_literatura extends list_literatura_gClass {}

