<?php

class users_login_as_gClass extends source_gClass
{
    protected function getData()
    {
        $result_pVar = db_session_gClass::login_as_gFunc($this->params['item_id']);
        if(is_array($result_pVar)) {
            $ret_pVar = $result_pVar;
            $ret_pVar['result'] = '1';
        }
        else {
            $ret_pVar = array('result'=>'0');
        }
        return($ret_pVar);
    }
}

class users_login_as extends users_login_as_gClass {}
