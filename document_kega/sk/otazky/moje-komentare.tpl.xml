<?xml version="1.0" encoding="utf-8" ?>
<truEngine-document xmlns:te="http://www.truengine.sk" te:access="s_test_show_comments">

<te:content-set name="title" value="@L{Moje komentáre}" />

<te:source name="test_question_last_comments" varname="comments" creator_id="@{session:user_id}" item_id="0" columns="&amp;nbsp;,comment_datetime_html:@L{Pridané},comment_text:@L{Komentár}" delete_id="@{request:delete_id}" _order_by="@{request:zoradit}" />

<te:include name="/sk/components/table" table="@@{comments}"  _sortable="nick,comment_datetime_html,comment_text" _order_by="@{request:zoradit}">
	<te:include-param name="&amp;nbsp;">
		<a href="/otazky/komentare-k-otazke?item_id=%%item_id%%" te:access="s_test_show_comments">@L{Otázka}</a><br />
		<a href="/otazky/moje-komentare?delete_id=%%comment_id%%" te:access="s_test_delete_owner_comment" onclick="return(confirm('@L{Naozaj chcete zmazať komentár?}'));">@L{Zmazať komentár}</a>
	</te:include-param>
</te:include>

<script type="text/javascript">
	$j(function() {
		$j('.table-comment_datetime_html').each(function(index) {
			$j('.table-comment_datetime_html')[index].innerHTML = datetime($j('span.hidden', $j('.table-comment_datetime_html')[index])[0].innerHTML);			
		});
	});
</script>

</truEngine-document>