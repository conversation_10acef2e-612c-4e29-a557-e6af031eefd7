<?php

class test_list_gClass extends source_gClass
{

    protected function getData()
    {
        $delete_id_pVar = 0;
        if(key_exists('delete_id', $this->params)) {
            // preco tato pomienka nefunguje?????? nevadi, nahradil som ju s key_exists
            // if(isset($this->params['delete_id'])) {
            $delete_id_pVar = intval($this->params['delete_id']);
            unset($this->params['delete_id']);
        }
        $delete_data_pVar = false;
        if(key_exists('delete_data', $this->params)) {
            if($this->params['delete_data'] === 'true') {
                $delete_data_pVar = true;
            }
            unset($this->params['delete_data']);
        }

        if($delete_id_pVar) {
            if($delete_data_pVar) {
                if(session_gClass::userHasRightsAccess_gFunc(s_test_delete_test_data)) {
                    $sql_pVar = 'UPDATE `%ttests` SET `status` = \'deleted\' WHERE `id` = %d';
                    db_public_gClass::execute_gFunc($sql_pVar, __FILE__, __LINE__, array($delete_id_pVar));
                }
            }
            else {
                if(session_gClass::userHasRightsAccess_gFunc(s_test_delete_test)
                    || session_gClass::userHasRightsAccess_gFunc(s_test_delete_test_data)) {
                    $sql_pVar = 'SELECT count(`id`) FROM `%ttests_running` WHERE `source_test_id` = %d';
                    if(!db_public_gClass::getField_gFunc($sql_pVar, __FILE__, __LINE__, $delete_id_pVar)) {
                        $sql_pVar = 'UPDATE `%ttests` SET `status` = \'deleted\' WHERE `id` = %d';
                        db_public_gClass::execute_gFunc($sql_pVar, __FILE__, __LINE__, array($delete_id_pVar));
                    }
                }
            }
        }

        $adapter_pVar = items_gClass::getAdapter_gFunc('test');
        $data_pVar = $adapter_pVar->getItems_gFunc($this->params);

        return($data_pVar);

        /*
                $filter_pVar = $this->params;
                if(isset($filter_pVar['pager'])) {
                    $pager_pVar = explode(',', $filter_pVar['pager']);
                    if(intval($pager_pVar[1])) {
                        $pager_pVar[1] = intval($pager_pVar[1]) - 1;
                    }
                    unset($filter_pVar['pager']);
                }
                else {
                    $pager_pVar = false;
                }

                $where_str_pVar = ' WHERE `status` <> \'deleted\'';
                $where_data_pVar = array();

                if(!isset($filter_pVar['official']) || $filter_pVar['official'] !== 'yes') {
                    $filter_pVar['official'] = 'no';
                }

                if($filter_pVar['official'] === 'yes') {
                    if(!session_gClass::userHasRightsAccess_gFunc(s_test_list_official_tests)) {
                        return(array());
                    }
                    if(!session_gClass::userHasRightsAccess_gFunc(s_test_list_official_unactive_tests)) {
                        $where_str_pVar .= 'AND (`time_start` IS NULL OR `time_start` < now())';
                        $where_str_pVar .= 'AND (`time_stop` IS NULL OR `time_stop` > now())';
                    }
                }
                else {
                    if(!session_gClass::userHasRightsAccess_gFunc(s_test_list_tests)) {
                        return(array());
                    }
                    if(!session_gClass::userHasRightsAccess_gFunc(s_test_list_unactive_tests)) {
                        $where_str_pVar .= 'AND (`time_start` IS NULL OR `time_start` < now())';
                        $where_str_pVar .= 'AND (`time_stop` IS NULL OR `time_stop` > now())';
                    }
                }

                foreach($filter_pVar as $k_pVar=>$v_pVar) {
                    if($k_pVar === 'filter') continue;
                    if($k_pVar === '_order_by') continue;
                    $where_str_pVar .= ' AND `' . $k_pVar . '` = %s';
                    $where_data_pVar[] = $v_pVar;
                }

                if(is_array($pager_pVar) && count($pager_pVar) == 2 && intval($pager_pVar[0]) > 0) {
                    $page_pVar = intval($pager_pVar[1]);
                    $pageLen_pVar = intval($pager_pVar[0]);
                    $offset_pVar = $pageLen_pVar * $page_pVar;
                    $limit_str_pVar = ' LIMIT %d, %d';
                    $where_data_pVar[] = $offset_pVar;
                    $where_data_pVar[] = $pageLen_pVar;
                }

                $sql_pVar = 'SELECT * FROM `%ttests`' . $where_str_pVar . $limit_str_pVar;
                $data_pVar = db_public_gClass::getResultArray_gFunc($sql_pVar, __FILE__, __LINE__, $where_data_pVar);

                if($pager_pVar !== false) {
                    $sql_pVar = 'SELECT count(`id`) as `n` FROM `%ttests`' . $where_str_pVar;
                    $pager_pVar[2] = db_public_gClass::getField_gFunc($sql_pVar, __FILE__, __LINE__, $where_data_pVar);

                    $data_pVar['_pager'] = array();
                    $data_pVar['_pager']['pageLen'] = $pager_pVar[0];
                    $data_pVar['_pager']['currentPage'] = (int)$pager_pVar[1] + 1;
                    $data_pVar['_pager']['totalItems'] = $pager_pVar[2];
                    $data_pVar['_pager']['totalPages'] = ceil($data_pVar['_pager']['totalItems'] / $data_pVar['_pager']['pageLen']);
                }


                $this->formatData_gFunc($data_pVar);

                //$data_pVar['_order_by_fields'] =
                return($data_pVar);
                */
    }

}

class test_list extends test_list_gClass {}
