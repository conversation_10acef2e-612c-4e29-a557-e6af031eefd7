<?php
if(isset($_SERVER['SCRIPT_FILENAME']) && strtolower($_SERVER['SCRIPT_FILENAME']) == strtolower(str_replace("\\",'/',__FILE__))) { header("HTTP/1.0 404 Not Found");  return(false); }
if(isset($GLOBALS['file__export_pVar'])) return(true);
$GLOBALS['file__export_pVar']=true;

if(!modules_gClass::isModuleRegistred_gFunc('forms'))
{
    return(false);
}


class export_gClass
{
	protected $right_pVar;
	protected $settings_pVar;
	protected $result_pVar;
	protected $comment_pVar;

	function __construct($settings_pVar, $initBuffer_pVar = true)
	{
		$this->settings_pVar = $settings_pVar;

		$this->right_pVar = s_system_export_data;
		$right_pVar =  's_' . $this->settings_pVar['data_type'] . '_export_data_' . $this->settings_pVar['export_type'];
		if(defined($right_pVar)) {
			$this->right_pVar = constant($right_pVar);
		}

		$this->settings_pVar['timestamp'] = date('Y-m-d H:i:s');
		$this->settings_pVar['user_id'] = session_gClass::getUserDetail_gFunc('user_id');
		$this->settings_pVar['db_name'] = main_gClass::getConfigVar_gFunc('database', 'db');
		$this->settings_pVar['table_prefix'] = main_gClass::getConfigVar_gFunc('table_prefix', 'db');
		$this->comment_pVar = $this->settings_pVar['comment'];
		unset($this->settings_pVar['comment']);
		ob_clean();

		if($initBuffer_pVar) {
			$this->result_pVar = new ob_cache_gClass();
			$this->result_pVar->setEncoding_gFunc($this->settings_pVar['encoding']);
		}
	}

	public function export_gFunc()
	{
		if(!session_gClass::userHasRightsAccess_gFunc($this->right_pVar)) {
			return(false);
		}

		if(!$this->export_open_gFunc()) {
			ob_clean();
			return(false);
		}


		if(!$this->export_data_gFunc()) {
			ob_clean();
			return(false);
		}

		if(!$this->export_close_gFunc()) {
			ob_clean();
			return(false);
		}

		main_gClass::openTrash_gFunc();

		$sql_pVar = 'INSERT INTO `%texport_log` (`user_id`, `export_datetime`, `export_settings`, `export_comment`)
						VALUES (%d, now(), %s, %s)';
		$sqlParams_pVar = array(session_gClass::getUserDetail_gFunc('user_id'), $this->implodeSettings_gFunc(), $this->comment_pVar);
		db_public_gClass::execute_gFunc($sql_pVar, __FILE__, __LINE__, $sqlParams_pVar);
		return(true);
	}

	private function implodeSettings_gFunc()
	{
		$ret_pVar = array();
		foreach($this->settings_pVar as $k_pVar=>$v_pVar) {
			if(is_array($v_pVar)) {
				if($k_pVar === 'item_ids') {
					$ret_pVar[] = $k_pVar . '=' . implode(',', array_keys($v_pVar));
				}
				else {
					$ret_pVar[] = $k_pVar . '=' . implode(',', $v_pVar);
				}
			}
			else {
				$ret_pVar[] = $k_pVar . '=' . $v_pVar;
			}
		}
		$ret_pVar = implode(LF, $ret_pVar);
		return($ret_pVar);
	}

	protected function export_open_gFunc()
	{
		return(true);
	}

	protected function export_close_gFunc()
	{
		return(true);
	}

	protected function export_data_gFunc()
	{
		return(true);
	}

	protected function getData_gFunc()
	{
		if(!session_gClass::userHasRightsAccess_gFunc($this->right_pVar)) {
			return(false);
		}
		$filter_pVar = array('filter'=>'item_id=' . implode('|', array_keys($this->settings_pVar['item_ids'])));

		$items_pVar = items_gClass::getItems_gFunc($this->settings_pVar['data_type'], $filter_pVar);
		unset($items_pVar['filter']);

		$fields_pVar = explode(',', $this->settings_pVar['fields']);
		$languages_pVar = explode(',', $this->settings_pVar['languages']);

		$data_pVar = array('_fields'=>array());
		foreach($items_pVar as $ik_pVar=>$item_pVar) {
			if(substr($ik_pVar, 0, 1) == '_') {
				continue;
			}
			$item_data_pVar = array();
			$childs_pVar = array();
			foreach($fields_pVar as $field_pVar) {
				foreach($languages_pVar as $lng_pVar) {
					$ext_pVar = false;
					if(strpos($field_pVar, '.') !== false) {
						$lng_field_pVar = str_replace('.', '.' . $lng_pVar . '_', $field_pVar);
						$ext_pVar = explode('.', $lng_field_pVar);
					}
					else {
						$lng_field_pVar = $lng_pVar . '_' . $field_pVar;
					}
					if(!isset($item_pVar[$lng_field_pVar])) {
						$lng_field_pVar = $field_pVar;
						if(strpos($field_pVar, '.') !== false) {
							$ext_pVar = explode('.', $lng_field_pVar);
						}
					}

					if($ext_pVar !== false && is_array($item_pVar[$lng_field_pVar])) {
						if(!isset($childs_pVar[$ext_pVar[0]])) {
							$childs_pVar[$ext_pVar[0]] = array();
						}
						foreach($item_pVar[$lng_field_pVar] as $k_pVar=>$v_pVar) {
							if(!isset($childs_pVar[$ext_pVar[0]][$k_pVar])) {
								$childs_pVar[$ext_pVar[0]][$k_pVar] = array();
							}
							if(is_array($v_pVar)) { // subor
								if(!count($v_pVar)) { // nie je nastaveny
									$childs_pVar[$ext_pVar[0]][$k_pVar][$ext_pVar[1]] = null;
								}
								else {
									$childs_pVar[$ext_pVar[0]][$k_pVar][$ext_pVar[1]] = array('_TE:file_content'=>$v_pVar[0]['file']);
								}
								$data_pVar['_fields'][$lng_field_pVar] = 'file';
							}
							else {
								$childs_pVar[$ext_pVar[0]][$k_pVar][$ext_pVar[1]] = $v_pVar;
								if(!isset($data_pVar['_fields'][$lng_field_pVar])) {
									$data_pVar['_fields'][$lng_field_pVar] = 'data';
								}
							}
						}
					}
					else {
						if(is_array($item_pVar[$lng_field_pVar])) { // subor
							if(!count($item_pVar[$lng_field_pVar])) { // nie je nastaveny
								$item_data_pVar[$lng_field_pVar] = null;
							}
							else {
								$item_data_pVar[$lng_field_pVar] = array('_TE:file_content'=>$item_pVar[$lng_field_pVar][0]['file'], 'data'=>$item_pVar[$lng_field_pVar]);
							}
							$data_pVar['_fields'][$lng_field_pVar] = 'file';
						}
						else {
							$item_data_pVar[$lng_field_pVar] = $item_pVar[$lng_field_pVar];
							if(!isset($data_pVar['_fields'][$lng_field_pVar])) {
								$data_pVar['_fields'][$lng_field_pVar] = 'data';
							}
						}
					}
				}
			}
			if(count($childs_pVar)) {
				foreach($childs_pVar as $k_pVar=>$v_pVar) {
					$item_data_pVar[$k_pVar] = $v_pVar;
				}
			}
			if(count($item_data_pVar)) {
				$data_pVar[] = $item_data_pVar;
			}
			unset($items_pVar[$ik_pVar]);
		}
		return($data_pVar);
	}

	private function getData_fieldValue_gFunc()
	{

	}
}

class export_form_gClass extends form_gClass {

	protected function initForm_gFunc($multiedit_pVar = false, $initFormRef_pVar = true)
	{
		if(!isset($this->params['data_type']) || empty($this->params['data_type'])) {
			return;
		}
		if(!isset($this->params['export_type'])) {
			$this->params['export_type'] = 'xml';
		}

		$selected_items_pVar = main_gClass::getSessionData_gFunc('selected_items', array());
		$n_pVar = 0;
		if(isset($selected_items_pVar[$this->params['data_type']])) {
			$n_pVar = count($selected_items_pVar[$this->params['data_type']]);
		}
		$this->addField_gFunc('data', 'n', 'integer', 'Počet exportovaných záznamov', false, false, false);
		$this->setFieldDefaultValue_gFunc('n', $n_pVar);

		if(isset($this->params['submit_button_title'])) {
			$this->setVar_gFunc('submit_button_title', $this->params['submit_button_title'], false);
		}

		if(!$n_pVar) {
			return;
		}

		if(isset($this->params['fields']) && is_array($this->params['fields'])) {
			$xfields_pVar = $this->params['fields'];
		}

		if(modules_gClass::isModuleRegistred_gFunc('items')) {
			$info_pVar = db_items_gClass::getInfo_gFunc($this->params['data_type']);
			if($info_pVar !== false) {
				$this->addHiddenField_gFunc('data_type', $this->params['data_type']);
				$fields_pVar = db_items_gClass::getItemsFields_gFunc($this->getFieldValue_gFunc('data_type'));
				foreach($fields_pVar as $v_pVar) {
					if($v_pVar['type'] !== 'join') {
						continue;
					}
					if(!empty($v_pVar['pattern'])) {
						$tmp_pVar = explode('|', $v_pVar['pattern']);
						if(count($tmp_pVar) >= 2) {
							$child_fields_pVar = db_items_gClass::getItemsFields_gFunc($tmp_pVar[0]);
							if(is_array($child_fields_pVar)) {
								foreach($child_fields_pVar as $kk_pVar=>$vv_pVar) {
									$vv_pVar['tag'] = $tmp_pVar[1] . '.' . $vv_pVar['tag'];
									$vv_pVar[main_gClass::getLanguage_gFunc() .'_name'] = $v_pVar[main_gClass::getLanguage_gFunc() .'_name'] . ' (' . $vv_pVar[main_gClass::getLanguage_gFunc() .'_name'] . ')';
									$fields_pVar[$vv_pVar['tag']] = $vv_pVar;
								}
							}
						}
					}
				}


				$options_pVar = array();
				foreach($fields_pVar as $v_pVar) {
					if(isset($xfields_pVar) && isset($xfields_pVar[$this->params['data_type']])) {
						if(!in_array($v_pVar['tag'], $xfields_pVar[$this->params['data_type']])) {
							continue;
						}
					}
					if(in_array($v_pVar['type'], array('password','blob','join','itemlist'))) {
						continue;
					}
					if($this->params['export_type'] === 'cvs') {
						if(in_array($v_pVar['type'], array('filelist','xfilelist','imagelist','ximagelist'))) {
							continue;
						}
					}
					$options_pVar[$v_pVar['tag']] = $v_pVar[main_gClass::getLanguage_gFunc() .'_name'];
				}

				$this->addField_gFunc('data', 'fields', 'set', 'Exportovať polia');
				$options2_pVar = array();
				foreach($options_pVar as $kk_pVar=>$vv_pVar) {
					$options2_pVar[str_replace('.', '-DOT-', $kk_pVar)] = $vv_pVar;
				}
				$this->setFieldOptions_gFunc('data', 'fields', $options2_pVar);

				$info_pVar = db_items_gClass::getInfo_gFunc($this->params['data_type']);
				$options_pVar = array();
				foreach($info_pVar['languages'] as $v_pVar) {
					$options_pVar[$v_pVar] = $v_pVar;
				}

				$this->addField_gFunc('lng', 'languages', 'set', 'Exportované jazyky', true);
				$this->setFieldOptions_gFunc('lng', 'languages', $options_pVar);

				$this->addField_gFunc('other', 'comment', 'text', 'Komentár');
			}
		}

		$this->addField_gFunc('lng', 'encoding', 'varchar', 'Znaková sada', true);
		$this->setFieldDefaultValue_gFunc('encoding', 'utf-8');
		$this->setFieldInfo_gFunc('encoding', '::Ak zadáte znakovú sadu, v ktorej nemôžu byť všetky znaky správne zobrazené, export bude znehodnotený.<br />Pozor dávajte hlavne pri exportoch vo viacerých jazykoch. Znaková sada musí vyhovovať všetkým exportovaným jazykom.<br />Znaková sada utf-8 je vhodná pre všetky jazyky, ale niektoré editory alebo programy (hlavne staršie) ju nemusia dobre spracovať.');

	}

	protected function getData()
	{
		$selected_items_pVar = main_gClass::getSessionData_gFunc('selected_items', array());
		$n_pVar = 0;
		if(isset($selected_items_pVar[$this->params['data_type']])) {
			$n_pVar = count($selected_items_pVar[$this->params['data_type']]);
		}

		$result_pVar = array();
		$result_pVar['title'] = $this->params['export_type'];
		if(!isset($this->params['data_type']) || empty($this->params['data_type']) || !$n_pVar) {
			$data_pVar = $this->getFormData_gFunc();
			$data_pVar['result'] = $result_pVar;
			$data_pVar['error_code'] = self::RESULT_VISIBLE_pVar;
			return($data_pVar);
		}

		$data_pVar = $this->getFormData_gFunc();
		if($data_pVar['error_code'] !== self::RESULT_OK_pVar) {
			$data_pVar['result'] = $result_pVar;
			return($data_pVar);
		}

		$settings_pVar = array();
		$settings_pVar['item_ids'] = $selected_items_pVar[$this->params['data_type']];
		$settings_pVar['languages'] = $this->getFieldValue_gFunc('languages');
		$settings_pVar['fields'] = str_replace('-DOT-', '.', $this->getFieldValue_gFunc('fields'));
		$settings_pVar['encoding'] = $this->getFieldValue_gFunc('encoding');
		$settings_pVar['comment'] = $this->getFieldValue_gFunc('comment');
		$settings_pVar['data_type'] = $this->params['data_type'];
		$settings_pVar['export_type'] = $this->params['export_type'];
		// exportujem
		$export_pVar = false;
		switch ($this->params['export_type']) {
			case 'xml':
				if(modules_gClass::isModuleRegistred_gFunc('export_xml')) {
					$export_pVar = new export_xml_gClass($settings_pVar);
				}
				break;
			case 'csv':
				if(modules_gClass::isModuleRegistred_gFunc('export_csv')) {
					$export_pVar = new export_csv_gClass($settings_pVar);
				}
				break;
			case 'txt':
				if(modules_gClass::isModuleRegistred_gFunc('export_txt')) {
					$export_pVar = new export_txt_gClass($settings_pVar);
				}
				break;
			case 'pdf':
				if(modules_gClass::isModuleRegistred_gFunc('export_pdf')) {
					$export_pVar = new export_pdf_gClass($settings_pVar);
				}
				break;
		}

		if($export_pVar === false) {
			$export_pVar = new export_gClass($settings_pVar);
		}

		$result_pVar['result'] = $export_pVar->export_gFunc();
		if($result_pVar['result']) {
			unset($selected_items_pVar[$this->params['data_type']]);
			main_gClass::setPhpSessionVar_gFunc('selected_items', $selected_items_pVar);
		}

		$data_pVar = $this->getFormData_gFunc();
		$data_pVar['result'] = $result_pVar;

		return($data_pVar);
	}

}

class export_form extends export_form_gClass {}


return(true);
