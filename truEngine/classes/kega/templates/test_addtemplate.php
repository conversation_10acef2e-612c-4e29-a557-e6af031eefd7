<?php

class test_addtemplate_gClass extends additem_gClass
{
    protected function initParams_gFunc()
    {
        $this->setParam_gFunc('formtype-add','add_item');
        $this->setParam_gFunc('formtype-edit', 'edit_item');
        $this->setParam_gFunc('itemtype', 'test_templates');
    }

    protected function initForm_gFunc($multiedit_pVar = false, $initFormRef_pVar = true)
    {
        parent::initForm_gFunc();
        if(!$this->getFieldValue_gFunc('test_templates_item_id')) {

        }
        else {

        }

        if(isset($this->params['official']) && $this->params['official'] == 'yes') {
            $this->setDefaultValue_pVar('base', 'official', 'yes');
        }

        $tree_pVar = db_items_gClass::getTree_gFunc('test_questions', 'zaradenie');
        $questionFields_pVar = db_items_gClass::getItemsFields_gFunc('test_questions');

        foreach($tree_pVar['_defs'] as $field_pVar=>$fieldId_pVar) {
            $this->addField_gFunc('categories', $field_pVar,  $questionFields_pVar[$field_pVar]['type'], $questionFields_pVar[$field_pVar]['sk_name']);
            $values_pVar = array('-1'=>'');
            foreach($tree_pVar['_values'][$fieldId_pVar] as $value_pVar) {
                $values_pVar[$value_pVar['enum_field_value']] = $value_pVar['sk_enum_field_name_item'];
            }
            $this->setFieldOptions_gFunc('categories', $field_pVar, $values_pVar);

            $this->setFieldEvent_gFunc('categories', $field_pVar, 'onchange', 'selectFilterChanged(test_questions_zaradenie_selector, \''.$field_pVar.'\', \'\');');
            $this->addJavaScript_gFunc($tree_pVar['_js']);
        }
        $this->addField_gFunc('categories', 'category_count',  'int', 'Pomer (%)');
        $this->addField_gFunc('categories', 'add_category',  'varchar', '');

        $items_pVar = main_gClass::getSessionData_gFunc('selected_items', array());
        $this->addField_gFunc('categories', 'add_questions_values',  'text', '');
        $this->addField_gFunc('categories', 'add_questions',  'varchar', '');
        if(isset($items_pVar['test_questions']) && is_array($items_pVar['test_questions']) && count($items_pVar['test_questions'])) {
            $this->setDefaultValue_pVar('categories', 'add_questions_values', implode(',', array_keys($items_pVar['test_questions'])));
        }

    }

    protected function getData()
    {
        $this->setFieldPrefix_gFunc('test_templates_');
        $value_pVar = $this->getFieldValue_gFunc('categories');

        $total_pVar = 0;
        $values_pVar = explode(NL, $value_pVar);
        foreach($values_pVar as $v_pVar) {
            $tmp_pVar = explode(' ', $v_pVar);
            if(count($tmp_pVar) > 1 && is_numeric($tmp_pVar[0])) {
                $total_pVar += $tmp_pVar[0];
            }
        }
        foreach($values_pVar as $k_pVar=>$v_pVar) {
            $tmp_pVar = explode(' ', $v_pVar);
            if(count($tmp_pVar) > 1 && is_numeric($tmp_pVar[0])) {
                $tmp_pVar[0] = intval((100 / $total_pVar) * $tmp_pVar[0]);
                $values_pVar[$k_pVar] = implode(' ', $tmp_pVar);
            }
        }

        $value_pVar = implode(NL, $values_pVar);

        $this->setCurrentValue_pVar('categories', 'categories', $value_pVar);
        $this->setFieldPrefix_gFunc('');
        return(parent::getData());
    }

    protected function validateField_gFunc($fieldsetName_pVar, $fieldName_pVar, $applyPrefix_pVar = true)
    {
        if($fieldName_pVar !== 'test_templates_sk_name'
            && $fieldName_pVar !== 'test_templates_en_name') {
            return(parent::validateField_gFunc($fieldsetName_pVar, $fieldName_pVar, $applyPrefix_pVar));
        }

        $names_pVar = array();
        $names_pVar['sk'] = $this->getFieldValue_gFunc('test_templates_sk_name');
        $names_pVar['en'] = $this->getFieldValue_gFunc('test_templates_en_name');
        $language_pVar = $this->getFieldValue_gFunc('test_templates_language');

        if(!isset($names_pVar[$language_pVar]) || empty($names_pVar[$language_pVar])) {
            $this->setError_gFunc(false, 'test_templates_' . $language_pVar . '_name', 'require');
        }

        return(true);
    }

    protected function saveData_gFunc()
    {
        $ret_pVar = parent::saveData_gFunc();
        log_gClass::write_gFunc('TEST_QUESTION_TEMPLATE_UPDATE', $this->getVar_gFunc('item_id'), false, true);
        return($ret_pVar);
    }
}

class test_addtemplate extends test_addtemplate_gClass {}
