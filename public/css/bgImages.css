@charset "iso-8859-1";

/*******************************************************************************
*  bgImages.css : 2006-01-30 : Ruthsarian Layouts
* ------------------------------------------------------------------------------
*  this demonstration stylesheet is used to show how to apply background images
*  to elements within the layout. This includes switching from borders to 
*  background images for the side columns. This requires some additional 
*  values to be altered besides the background family of properties.
*******************************************************************************/

#outerColumnContainer
{
	border-left-width: 0;
	border-right-width: 0;	/* we remove the borders so we can apply
				   background images. */
	padding-left: 15em;	/* because padding is on the left-side,
				   the left-column"s background image is
				   going to be applied to this element. */
	margin-right: 15em;	/* the right-column"s space is reserved
				   with margin space rather than padding,
				   so we can apply a background image to
				   the parent element, #pageWrapper, and
				   it will peek through. */
	background-image: url("../images/tile16.jpg");
	background-repeat: repeat;
	background-position: 0 0;
}
#pageWrapper
{
	background-image: url("../images/tile19.jpg");
	background-repeat: repeat-y;
	background-position: 100% 0;
}
#masthead
{
	background-image: url("../images/tile11.jpg");
	background-repeat: repeat;
	background-position: 100% 0;
}
.hnav, .hnav ul li a
{
	background-image: url("../images/tile15.jpg");
	background-repeat: repeat;
	background-position: 100% 0;
}
.hnav ul li a:hover
{
	background-image: url("../images/tile09.jpg");
}
#footer
{
	background-image: url("../images/tile11.jpg");
	background-repeat: repeat;
	background-position: 100% 0;
}
#contentColumn
{
	background-color: transparent;	/* need to reset the background color of
					   the content column because we set it
					   in v4.css */
}
#innerColumnContainer
{
	background-image: url("../images/tile17.jpg");
	background-repeat: repeat;
	background-position: 100% 0;
}
/* html and body elements specified in selector below for compatibility with
   skidoo_too */
html, body
{
	background-image: url("../images/tile18.jpg");
	background-repeat: repeat;
	background-position: 100% 0;
}
.vnav ul li a
{
	background-image: url("../images/tile16.jpg");
	background-repeat: repeat;
	background-position: 100% 0;
}
.vnav ul li a:hover
{
	background-image: url("../images/tile20.jpg");
	background-repeat: repeat;
	background-position: 100% 0;
}

/******************************************************************************/