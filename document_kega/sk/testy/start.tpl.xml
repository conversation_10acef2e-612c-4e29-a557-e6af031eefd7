<?xml version="1.0" encoding="utf-8" ?>
<truEngine-document xmlns:te="http://www.truengine.sk">

<te:content-set name="title" value="@L{Spustenie testu}" />

<te:source name="test_prepare" varname="test" test_id="@{request:test_id}" template_id="@{request:template_id}" running_test_id="@{request:running_test_id}" access_key="@{request:access_key}" />


<te:if test="@{test:running_test_id}">
	<p te:if="!empty(@{test:time_end})">
		@L{Test bol už ukončený, nie je ho možné spustiť znovu.}
		<!--  @TODO: mozno linka na moznost spustit test s rovnakymi nastaveniami -->
	</p>
	<p te:if="!empty(@{test:time_start}) &amp;&amp; empty(@{test:time_end})">
		@L{Test bol už spustený. Po kliknutí na "Spustiť test" sa bude v teste pokračovať.}
	</p>
	<p>
	@L{Test je pripravený na spustenie.}
	</p>
	<p>
	<form method="get" action="/testy/test?test_id=@{test:running_test_id}" style="display:inline">
		<input type="submit" value="Spustiť test" />
	</form>
	<!-- a href="/testy/zrusit-test?test_id=@{test:running_test_id}">Zrušiť test</a -->
	</p>
<te:else />
	<te:if test="isset(@{test:access_key_required})">
		<p>
		@L{Pre spustenie tohto testu musíte zadať prístupový kód.}
		</p>
		<form method="get" action="/testy/start">
			@L{Prístupový kód:} <input type="text" name="access_key" />
			<input type="hidden" name="template_id" value="@{request:template_id}" />
			<br />
			<input type="submit" value="@L{Pokračovať}" />
		</form>
	<te:else />
		<p>
			@L{Test sa nepodarilo spustiť.}
		</p>
	</te:if>
</te:if>

</truEngine-document>
