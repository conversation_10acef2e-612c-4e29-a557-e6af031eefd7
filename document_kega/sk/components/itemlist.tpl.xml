<?xml version="1.0" encoding="utf-8" ?>
<truEngine-document xmlns:te="http://www.truengine.sk">

<?php
if(!isset(@{items_name})) {
	@{items_name} = 'item';
}
?>

<script type="text/javascript">
	function show@{items_name}ListDetails(id, show)
	{
		if(show) {
			document.getElementById('more_' + id).style.display='block';
			document.getElementById('a_show_' + id).style.display='none';
			document.getElementById('a_hide_' + id).style.display='block';
		}
		else {
			document.getElementById('more_' + id).style.display='none';
			document.getElementById('a_show_' + id).style.display='block';
			document.getElementById('a_hide_' + id).style.display='none';
		}
	}
</script>

<te:for each="@{items} as @{k_item}=>@{item}">
	<div class="itemlist">
		<div class="nav">
			<a href="#" onclick="show@{items_name}ListDetails(@{item:item_id}+'_'+@{k_item}, 1); return(false);" id="a_show_@{item:item_id}_@{k_item}" title="Viac detailov">+</a>
			<a href="#" onclick="show@{items_name}ListDetails(@{item:item_id}+'_'+@{k_item}, 0); return(false);" id="a_hide_@{item:item_id}_@{k_item}" style="display:none" title="Menej detailov">--</a>
		</div>
		<table cellpadding="0" cellspacing="0">
			<tr>
				<td class="label"><te:string id="Meno priezvisko" />:</td>
				<td>@{item:titul_pred} @{item:first_name} @{item:last_name} @{item:titul_za}</td>
			</tr>
		</table>
		<div id="more_@{item:item_id}_@{k_item}" style="display:none">
			<table cellpadding="0" cellspacing="0">
				<tr>
					<td class="label"><te:string id="Rola" />:</td>
					<td>@{item:user_role}</td>
				</tr>
			</table>
		</div>

		<div class="clear"></div>
	</div>
</te:for>

</truEngine-document>
