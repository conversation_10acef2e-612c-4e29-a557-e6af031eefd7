<?xml version="1.0" encoding="utf-8" ?>
<truEngine-document xmlns:te="http://www.truengine.sk">

<?php
	if(!isset(@{otazka})) {
		@{otazka} = @{request:otazka};
	}
	@{q} = array();
?>

<te:if test="@{otazka}">
	<te:include name="/sk/components/question_details" question_id="@{otazka}" otazka="@@{q}" edit_enabled="false" display="languages,source" keywords="test_multiaddanswer" />
	<script type="text/javascript">
			lng_sk =<te:if test="!empty(@{q:sk_otazka}) || count(@{q:sk_otazka_media})">true<te:else />false</te:if>;
			lng_en =<te:if test="!empty(@{q:en_otazka}) || count(@{q:en_otazka_media})">true<te:else />false</te:if>;
			lng_cz =<te:if test="!empty(@{q:cz_otazka}) || count(@{q:cz_otazka_media})">true<te:else />false</te:if>;
	</script>
	
</te:if>

<te:source name="test_multiaddanswer" varname="form" otazka="@{otazka}">
	<te:source-param name="submit_button_title_add">@L{Pridať odpovede}</te:source-param>
	<te:source-param name="submit_button_title_update">@L{Aktualizovať odpovede}</te:source-param>
</te:source>

<te:include name="/sk/components/form-multi" form="@@{form}" />

<te:if test="@{form:hidden_fields:a0_test_answers_item_id:value}">
	<te:content-set name="title" value="@L{Editovať odpovede}" />
<te:else />
	<te:content-set name="title" value="@L{Nové odpovede}" />
</te:if>

<te:if test="@{form:error_code} == 'ok'">
	<te:include name="/sk/components/question_details" question_id="@{form:hidden_fields:question_id:value}" edit_enabled="1" />
	<ul>
		<li><a href="/otazky/nova-odpoved?otazka=@{form:hidden_fields:question_id:value}">@L{Pridať novú odpoveď}</a></li>
		<li><a href="/otazky/odpovede-multiedit?otazka=@{form:hidden_fields:question_id:value}">@L{Editovať všetky odpovede}</a></li>
		<li style="padding-top:20px;"><a href="/otazky/nova-otazka">@L{Pridať novú otázku}</a></li>
		<li><a href="/otazky/editovat-otazku?item_id=@{form:hidden_fields:question_id:value}">@L{Editovat otázku}</a></li>
	</ul>
	<te:if test="@{request:answer-add}">
		<te:redirect location="/@{doc}?otazka=@{form:hidden_fields:question_id:value}&amp;answer-add=1" />
	</te:if>
	<te:else />
		<script type="text/javascript">
			$j(document).ready(function() {
			  blockLayout();
			});	
		</script>
		
		<te:if test="@{request:answer-add}">
			<script type="text/javascript">
				$j(document).ready(function() {
				$j('.multiFormPart:visible .multiform-addlink').click();
				setTimeout('document.location = \'#submit\';', 100);	
				});
			</script>		
		</te:if>
</te:if>

<script type="text/javascript">
	$j(document).ready(function() {
		$j('#test_multiaddanswer').submit(function () {
			for(var i = 0; $j('#a'+i+'_').css('display') == 'block'; i++) {
				if($j('#a'+i+'_test_answers_spravnost').val() == -1) {
					alert('@L{Musíte nastaviť hodnotu.}');
					$j('#a'+i+'_test_answers_spravnost').focus();
					return(false);
				}
			}
			return(true);
		})
		
		$j('input[type=submit]').after('<input type="button" value="@L{Uložiť a pridať}" onclick="$j(\'#answer-add\').val(1); $j(\'#test_multiaddanswer\').submit();" /><input type="hidden" name="answer-add" value="0" id="answer-add" /><a name="submit"></a>');
		
		for(i=0; i&lt;100; i++) {
			$j('#a'+i+'_test_answers_sk_odpoved_vysvetlenie').parent().parent().before('<tr style="font-size:9px;"><td></td><td><a href="#" onclick="$j(\'#a'+i+'_test_answers_sk_odpoved_vysvetlenie\').parent().parent().toggleClass(\'collapsed\');$j(\'#a'+i+'_test_answers_cz_odpoved_vysvetlenie\').parent().parent().toggleClass(\'collapsed\');$j(\'#a'+i+'_test_answers_en_odpoved_vysvetlenie\').parent().parent().toggleClass(\'collapsed\'); return(false);">@L{Vysvetlenie}</a></td></tr>');
			$j('#a'+i+'_test_answers_sk_odpoved_vysvetlenie').parent().parent().addClass('collapsed');
			$j('#a'+i+'_test_answers_en_odpoved_vysvetlenie').parent().parent().addClass('collapsed');
			$j('#a'+i+'_test_answers_cz_odpoved_vysvetlenie').parent().parent().addClass('collapsed');
		}
		
	});
	
</script>

</truEngine-document>
