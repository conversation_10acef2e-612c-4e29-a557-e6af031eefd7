body {
	background: $page-bg;
	color: $core-text;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

a {
	color: $core-accent;
	&:hover {
		color: darken($core-accent, 15%);
	}
}

pre {
	position: relative;
}

.bg {
	background: #fff;
	border: 1px solid $body-border;
}

b, strong {
    font-weight: $font-weight-bold
}

.default-animation {
    @include transition(all 0.5s ease);
}
