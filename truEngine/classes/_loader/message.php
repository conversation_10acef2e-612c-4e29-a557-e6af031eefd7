<?php

/**
 * message_gClass
 * Vypis hlasky
 */
class message_gClass {

    private function __construct()
    {

    }

    private static function formatMessage_pFunc($msg_pVar, $cssClass_pVar, $out_pVar)
    {
        $ret_pVar = '<div class="' . $cssClass_pVar . '">';
        $ret_pVar .= $msg_pVar;
        $ret_pVar .= '</div>';

        if($out_pVar) {
            echo $ret_pVar;
        }
        return($ret_pVar);
    }

    public static function ok_gFunc($msg_pVar, $out_pVar)
    {
        return(message_gClass::formatMessage_pFunc($msg_pVar, 'msg_ok', $out_pVar));
    }

    public static function error_gFunc($msg_pVar, $out_pVar)
    {
        return(message_gClass::formatMessage_pFunc($msg_pVar, 'msg_error', $out_pVar));
    }

    public static function warning_gFunc($msg_pVar, $out_pVar)
    {
        return(message_gClass::formatMessage_pFunc($msg_pVar, 'msg_warning', $out_pVar));
    }

    public static function info_gFunc($msg_pVar, $out_pVar)
    {
        return(message_gClass::formatMessage_pFunc($msg_pVar, 'msg_info', $out_pVar));
    }

    public static function tip_gFunc($msg_pVar, $out_pVar)
    {
        return(message_gClass::formatMessage_pFunc($msg_pVar, 'msg_tip', $out_pVar));
    }

    public static function fatal_gFunc($msg_pVar, $out_pVar)
    {
        return(message_gClass::formatMessage_pFunc($msg_pVar, 'msg_fatal', $out_pVar));
    }

    public static function debug_gFunc($msg_pVar, $out_pVar)
    {
        return(message_gClass::formatMessage_pFunc($msg_pVar, 'msg_debug', $out_pVar));
    }

}
