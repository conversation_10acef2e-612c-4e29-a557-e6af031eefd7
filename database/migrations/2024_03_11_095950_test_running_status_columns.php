<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('kega_tests_running', function (Blueprint $table) {
            $table->boolean('ignored')->nullable()->default(null);
            $table->boolean('processed')->nullable()->default(null);
            $table->index(['processed', 'ignored', 'status'], 'idx_processed_ignored_status');
            $table->index(['time_create'], 'idx_time_create');
        });

        Schema::table('kega_tests_running__questions', function (Blueprint $table) {
            $table->boolean('ignored')->nullable()->default(null);
            $table->boolean('processed')->nullable()->default(null);
        });

        Schema::create('kega_stats', function (Blueprint $table) {
            $table->id();
            $table->unsignedInteger('question_id');
            $table->unsignedInteger('answer_id');
            $table->unsignedInteger('user_id')->nullable()->default(null);
            $table->unsignedInteger('year')->nullable()->default(null);
            $table->date('date')->nullable()->default(null);

            $table->unsignedInteger('count_total')->default(0);
            $table->unsignedInteger('count_ok_total')->default(0);
            $table->unsignedInteger('count_fail_total')->default(0);
            $table->unsignedInteger('score_total')->default(0);

            $table->unsignedInteger('count_test')->default(0);
            $table->unsignedInteger('count_ok_test')->default(0);
            $table->unsignedInteger('count_fail_test')->default(0);
            $table->unsignedInteger('score_test')->default(0);

            $table->unique(['question_id', 'answer_id', 'user_id', 'year', 'date'], 'unique_question_answer_user_year_date');
        });


    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('kega_stats');
    }
};
