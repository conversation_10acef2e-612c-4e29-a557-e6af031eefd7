

label-description sa da pouzit ako slot ale aj ako parameter. ```<x-slot:label-description>```
label viem tiez pouzit ako slot alebo aj ako attribut
!!! TODO: Ale pre label mi **nefunguje** html, pretoze to zobrazujem cez alpinejs x-text !!!
```
<x-field name="email" label="Email" />
```

```
<x-field name="email">
    <x-slot:label><a href="#">Email</a></x-slot:label>
</x-field>
```

- default hodnoty mozem definovat v metode getDefaultData() alebo mozem definovat parameter value. Hodnota z getDefaultData() ma prednost.

- pouzivam globalny aplinejs store, pretoze viem refresnut(prerenderovat) komponentu, bez toho ze by sa mi vynulovali data ktore pouzivatel zadal. Inak by som musel poslat nove data - takto ich nemusim posielat na server a zaznamenavat ich, ale vraciam len tie ktore potrebujem zmenit.
