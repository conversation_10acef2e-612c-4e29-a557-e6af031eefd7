<?php

class db_public_gClass extends db_gClass
{
    public static function startTransaction_gFunc($fileName_pVar, $fileLine_pVar)
    {
        return(parent::startTransaction_gFunc($fileName_pVar, $fileLine_pVar));
    }

    public static function commit_gFunc($fileName_pVar, $fileLine_pVar)
    {
        return(parent::commit_gFunc($fileName_pVar, $fileLine_pVar));
    }

    public static function rollback_gFunc($fileName_pVar, $fileLine_pVar)
    {
        return(parent::rollback_gFunc($fileName_pVar, $fileLine_pVar));
    }

    public static function execute_gFunc($query_string_pVar, $fileName_pVar, $lineNum_pVar, $params_pVar = false, $getAffectedRows_pVar = false)
    {
        return(db_gClass::execute_gFunc($query_string_pVar, $fileName_pVar, $lineNum_pVar, $params_pVar, $getAffectedRows_pVar));
    }

    public static function insert_gFunc($query_string_pVar, $fileName_pVar, $lineNum_pVar, $params_pVar = false, $getLastId_pVar = false)
    {
        return(db_gClass::insert_gFunc($query_string_pVar, $fileName_pVar, $lineNum_pVar, $params_pVar, $getLastId_pVar));
    }

    public static function insertData_gFunc($tableName_pVar, $formatString_pVar, $data_pVar, $fileName_pVar, $lineNum_pVar, $getLastId_pVar = false)
    {
        return(db_gClass::insertData_gFunc($tableName_pVar, $formatString_pVar, $data_pVar, $fileName_pVar, $lineNum_pVar, $getLastId_pVar));
    }

    public static function updateData_gFunc($tableName_pVar, $formatString_pVar, $data_pVar, $whereString_pVar, $whereData_pVar, $fileName_pVar, $lineNum_pVar, $getAffectedRows_pVar = false)
    {
        return(db_gClass::updateData_gFunc($tableName_pVar, $formatString_pVar, $data_pVar, $whereString_pVar, $whereData_pVar, $fileName_pVar, $lineNum_pVar, $getAffectedRows_pVar));
    }

    public static function getField_gFunc($query_string_pVar, $fileName_pVar, $lineNum_pVar, $params_pVar = false)
    {
        return(db_gClass::getField_gFunc($query_string_pVar, $fileName_pVar, $lineNum_pVar, $params_pVar));
    }

    public static function getResultArray_gFunc($query_string_pVar, $fileName_pVar, $lineNum_pVar, $params_pVar = false, $indexBy_pVar = false, $indexByType_pVar = 0)
    {
        return(db_gClass::getResultArray_gFunc($query_string_pVar, $fileName_pVar, $lineNum_pVar, $params_pVar, $indexBy_pVar, $indexByType_pVar));
    }

    public static function getResult_gFunc($query_string_pVar, $fileName_pVar, $lineNum_pVar, $params_pVar = false)
    {
        return(db_gClass::getResult_gFunc($query_string_pVar, $fileName_pVar, $lineNum_pVar, $params_pVar));
    }

    public static function files_insertFile_gFunc($ref_tag_pVar, $ref_id_pVar, $dirIndex_pVar, $targetName_pVar, $original_md5_pVar, $md5_pVar, $size_pVar, $fileType_pVar = 'file', $custom_name_pVar = false, $copyright_pVar = false)
    {
        $params_pVar = array($fileType_pVar , $dirIndex_pVar, $targetName_pVar, $ref_tag_pVar, $ref_id_pVar, $original_md5_pVar, $md5_pVar, $size_pVar);
        if($custom_name_pVar !== false) {
            $params_pVar[] = $custom_name_pVar;
        }
        if($copyright_pVar !== false) {
            $params_pVar[] = $copyright_pVar;
        }
        $sql_pVar = 'INSERT INTO `%tfiles` (`file_type`, `location`, `full_name`, `ref_tag`, `ref_value`, `original_md5`, `md5`, `size`, `custom_name`, `copyright`) VALUES (%s, %s, %s, %s, %d, %s, %s, %d, '.($custom_name_pVar===false?'NULL':'%s').', '.($copyright_pVar===false?'NULL':'%s').')';
        $file_id_pVar = parent::insert_gFunc($sql_pVar, __FILE__, __LINE__, $params_pVar, true);
        if($file_id_pVar === false) {
            return(false);
        }
        return($file_id_pVar);
    }

    public static function files_deleteFileRecord_gFunc($fileId_pVar, $returnFileName_pVar = false, $refTagLike_pVar = false, $refValue_pVar = false)
    {
        $extra_sql_pVar = '';
        $extra_sql_params_pVar = array();
        if($refTagLike_pVar !== false) {
            $extra_sql_pVar .= ' AND `ref_tag` LIKE %s';
            $extra_sql_params_pVar[] = $refTagLike_pVar;
        }
        if($refValue_pVar !== false) {
            $extra_sql_pVar .= ' AND `ref_value` = %d';
            $extra_sql_params_pVar[] = $refValue_pVar;
        }

        if($returnFileName_pVar) {
            $params_pVar = array($fileId_pVar);
            $params_pVar = array_merge($params_pVar, $extra_sql_params_pVar);
            $sql_pVar = 'SELECT `location`,`full_name`,`md5` FROM `%tfiles` WHERE `file_id` = %d' . $extra_sql_pVar;
            $row_pVar = parent::getResult_gFunc($sql_pVar, __FILE__, __LINE__, $params_pVar);
            if($row_pVar === false) {
                return(false);
            }
            if(!is_array($row_pVar)) {
                return(true);
            }

            // zistim ci mozem vymazat
            $sql_pVar = 'SELECT count(`file_id`) FROM `%tfiles` WHERE `location` = %s AND `full_name` = %s';
            $n_pVar = parent::getField_gFunc($sql_pVar, __FILE__, __LINE__, array($row_pVar['location'], $row_pVar['full_name']));
            if($n_pVar <= 1) {
                db_public_gClass::files_log_gFunc('delete', $fileId_pVar);
                $fileName_pVar = main_gClass::getConfigVar_gFunc($row_pVar['location'], 'files') . $row_pVar['full_name'];
            }
            else {
                db_public_gClass::files_log_gFunc('delete_clone', $fileId_pVar);
                $fileName_pVar = true;
            }
        }
        else {
            db_public_gClass::files_log_gFunc('delete_record', $fileId_pVar);
        }
        $params_pVar = array($fileId_pVar);
        $params_pVar = array_merge($params_pVar, $extra_sql_params_pVar);
        $sql_pVar = 'DELETE FROM `%tfiles` WHERE `file_id` = %d' . $extra_sql_pVar;
        $ret_pVar = parent::execute_gFunc($sql_pVar, __FILE__, __LINE__, $params_pVar);
        if($ret_pVar === false) {
            return($ret_pVar);
        }
        if($returnFileName_pVar) {
            return($fileName_pVar);
        }
        return(true);
    }

    public static function files_file_exists_gFunc($target_dir_index_pVar, $target_name_pVar)
    {
        $params_pVar = array($target_dir_index_pVar, $target_name_pVar, $target_name_pVar);
        $sql_pVar = 'SELECT count(`file_id`) as `n` FROM `%tfiles` WHERE `location` = %s AND (`full_name` = %s OR `custom_name` = %s)';
        $row_pVar = parent::getField_gFunc($sql_pVar, __FILE__, __LINE__, $params_pVar);
        if($row_pVar === false) {
            return(true); // ak sa nepodaril select, preistotu vratim true
        }
        if($row_pVar) {
            return(true);
        }
        else {
            return(false);
        }
    }

    public static function files_get_records_gFunc($ref_tag_pVar, $ref_id_pVar, $like_pVar = false, $log_pVar = false)
    {
        if(is_array($ref_id_pVar)) {
            $ref_multi_pVar = true;
        }
        else {
            $ref_multi_pVar = false;
        }

        $params_pVar = array($ref_tag_pVar, $ref_id_pVar);
        $sql_pVar = $sql_pVar = 'SELECT * FROM `%tfiles` as `f`';

        if($log_pVar) {
            $sql_pVar .= 'LEFT JOIN `%tfiles_log` as `log` ON `log`.`file_id`=`f`.`file_id` ';
        }

        if(!$like_pVar) {
            $sql_pVar .= ' WHERE `f`.`ref_tag` = %s';
        }
        else {
            $sql_pVar .= ' WHERE `f`.`ref_tag` LIKE %s';
        }
        if(!empty($ref_id_pVar)) {
            if($ref_multi_pVar) {
                $sql_pVar .= ' AND `f`.`ref_value` IN (%ad)';
            }
            else {
                $sql_pVar .= ' AND `f`.`ref_value` = %d';
            }
        }
        else {
            unset($params_pVar[1]);
        }

        $row_pVar = parent::getResultArray_gFunc($sql_pVar, __FILE__, __LINE__, $params_pVar, 'file_id', 1);
        return($row_pVar);
    }

    public static function files_get_file_by_original_md5_gFunc($md5Sum_pVar, $location_pVar)
    {
        $sql_pVar = 'SELECT * FROM `%tfiles` WHERE (`original_md5` = %s OR `md5` = %s) AND location = %s';
        $row_pVar = parent::getResult_gFunc($sql_pVar, __FILE__, __LINE__, array($md5Sum_pVar, $md5Sum_pVar, $location_pVar));
        return($row_pVar);
    }

    public static function files_get_file_name_gFunc($location_pVar, $fileName_pVar)
    {
        $fullFileName = explode('/', $fileName_pVar);
        $fileName_pVar = array_pop($fullFileName);

        $prefix_pVar = '';
        $images_pVar = main_gClass::getConfigSectionVar_gFunc('images');
        if(isset($images_pVar[$location_pVar])) {
            $images_pVar = explode(',', $images_pVar[$location_pVar]);
            foreach ($images_pVar as $v_pVar) {
                $image_pVar = explode('=', $v_pVar);
                if(strlen($image_pVar[0]) && substr($fileName_pVar, 0, strlen($image_pVar[0])) === $image_pVar[0]) {
                    $prefix_pVar = $image_pVar[0];
                }
            }
        }

        $fileName_pVar = substr($fileName_pVar, strlen($prefix_pVar));
        $fileName_pVar = trim(implode('/', $fullFileName) . '/' . $fileName_pVar, '/');

        $sql_pVar = 'SELECT * FROM `%tfiles` WHERE `location` = %s AND (`custom_name` = %s OR (`custom_name` IS NULL AND `full_name` = %s))';

        $row_pVar = parent::getResult_gFunc($sql_pVar, __FILE__, __LINE__, array($location_pVar, $fileName_pVar, $fileName_pVar));

        if(is_array($row_pVar)) {
            // nasiel sa
            $row_pVar['prefix'] = $prefix_pVar;
            return($row_pVar);
        }


        $fileName_pVar = str_replace('.mp4', '.flv', $fileName_pVar);
        $sql_pVar = 'SELECT * FROM `%tfiles` WHERE `location` = %s AND (`custom_name` = %s OR (`custom_name` IS NULL AND `full_name` = %s))';
        $row_pVar = parent::getResult_gFunc($sql_pVar, __FILE__, __LINE__, array($location_pVar, $fileName_pVar, $fileName_pVar));
        if(is_array($row_pVar)) {
            $row_pVar['full_name'] = str_replace('.flv', '.mp4', $row_pVar['full_name']);
            $row_pVar['prefix'] = $prefix_pVar;
            return($row_pVar);
        }

        return(false);
    }

    public static function files_log_gFunc($change_type, $fileId_pVar)
    {
        $sql_pVar = "INSERT INTO `%tfiles_log` (`user_id`, `change_datetime`, `change_type`, `file_id`, `change_info`)
			SELECT  %d, now(), %s, %d, concat_ws(',',`file_type`,`location`,`full_name`,`custom_name`,`ref_tag`,`ref_value`,`size`,`original_md5`,`md5`,`copyright`)
			FROM `%tfiles` WHERE `file_id` = %d";
        parent::execute_gFunc($sql_pVar, __FILE__, __LINE__, array(session_gClass::getUserDetail_gFunc('user_id'), $change_type, $fileId_pVar, $fileId_pVar));
    }

    public static function eshop_getTransportTypes_gFunc($selectBox_pVar = true)
    {
        $sql_pVar = 'SELECT * FROM `%teshop_transport_types` WHERE `transport_type_status` <> \'deleted\'';
        $row_pVar = parent::getResultArray_gFunc($sql_pVar, __FILE__, __LINE__, false, 'transport_type_id');
        foreach ($row_pVar as $rk_pVar=>$r_pVar) {
            $row_pVar[$rk_pVar]['transport_type_name'] = $row_pVar[$rk_pVar][main_gClass::getLanguage_gFunc() . '_transport_type_name'];
        }

        if(!$selectBox_pVar) {
            return($row_pVar);
        }
        $data_pVar = array();
        foreach ($row_pVar as $k_pVar=>$v_pVar) {
            $data_pVar[$v_pVar['transport_type_id']] = $v_pVar['transport_type_name'] . ' ' . string_gClass::priceFormat_gFunc($v_pVar['transport_type_price'], $v_pVar['transport_type_currency'], 'skk');
        }
        return($data_pVar);
    }

    public static function eshop_getPaymentTypes_gFunc($selectBox_pVar = true)
    {
        $sql_pVar = 'SELECT `pt`.*, `t`.* FROM `%teshop_payment_types` as `pt` LEFT JOIN `%teshop_transport_types` as `t` ON `t`.`transport_type_id`=`pt`.`transport_type_id` WHERE `t`.`transport_type_status` <> \'deleted\' AND `pt`.`payment_type_status` <> \'deleted\' ORDER BY `t`.`'.main_gClass::getLanguage_gFunc().'_transport_type_name`, `pt`.`'.main_gClass::getLanguage_gFunc().'_payment_type_name`';
        $rows_pVar = parent::getResultArray_gFunc($sql_pVar, __FILE__, __LINE__, false, 'payment_type_id');
        foreach ($rows_pVar as $rk_pVar=>$r_pVar) {
            $row_pVar[$rk_pVar]['payment_type_name'] = $rows_pVar[$rk_pVar][main_gClass::getLanguage_gFunc() . '_payment_type_name'];
        }
        if(!$selectBox_pVar) {
            return($rows_pVar);
        }
        $transport_id_pVar = false;
        $data_pVar = array();
        foreach ($rows_pVar as $k_pVar=>$v_pVar) {
            if($transport_id_pVar !== (int)$v_pVar['transport_type_id']) {
                $transport_id_pVar = (int)$v_pVar['transport_type_id'];
                $data_pVar[$transport_id_pVar] = array('optgroup'=>$v_pVar['transport_type_name']);
            }
            $data_pVar[$transport_id_pVar][$v_pVar['payment_type_id']] = $v_pVar['payment_type_name'];
        }
        return($data_pVar);
    }

    public static function initRightsNames_gFunc($fileName_pVar)
    {
        $sql_pVar = 'SELECT * FROM `%taccess__names` as `an`
						LEFT JOIN `%taccess__names_categories` as `anc`
						ON `an`.`access_category_id`=`anc`.`access_category_id`';
        $rows_pVar = parent::getResultArray_gFunc($sql_pVar, __FILE__, __LINE__);

        $str_pVar = PHP_OPEN;
        foreach ($rows_pVar as $k_pVar=>$v_pVar) {
            $aliases_pVar = explode(',', $v_pVar['alias']);
            foreach ($aliases_pVar as $kk_pVar=>$vv_pVar) {
                if($kk_pVar == 0) {
                    $str_pVar .= NL . 'define(\'s_' . $v_pVar['prefix'] . '_' . $vv_pVar . '\',' . $v_pVar['access_id'] . ');' ;
                }
                else {
                    $str_pVar .= NL . 'define(\'' . $vv_pVar . '\',' . $v_pVar['access_id'] . ');' ;
                }
            }
        }
        $str_pVar .= PHP_CLOSE;
        file_put_contents($fileName_pVar, $str_pVar);
    }
}
