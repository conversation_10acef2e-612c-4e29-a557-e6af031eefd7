<?xml version="1.0" encoding="utf-8" ?>
<truEngine-document xmlns:te="http://www.truengine.sk" te:access="s_system_admin">

	<te:source name="maintainance_stats_per_hour" varname="data1" />
	<te:source name="maintainance_stats_per_hour" varname="data2" />

    <script type='text/javascript' src='https://www.google.com/jsapi'></script>

    <script type='text/javascript'>
      google.load('visualization', '1', {'packages':['annotatedtimeline']});
      google.setOnLoadCallback(drawChart);
      function drawChart() {
      	// graf 1
        var data = new google.visualization.DataTable();
		data.addColumn('datetime', 'Dátum');
		data.addColumn('number', 'Document executed');

        data.addRows([
        	<?php $n = 0; ?>
        	<te:for each="@{data1:data} as @{row}">
        		<?php
        			if(strlen(@{request:month})) {
						$year = intval(substr(@{request:month}, 0, 4));
						$month = intval(substr(@{request:month}, 5, 2));

						if(@{row:year} != $year || @{row:month} != $month) {
							continue;
						}
					}
        		?>
        		<?php $n++; if($n!=1) { echo ','; } ?>
	        	[new Date(@{row:year}, @{row:month} , @{row:day}, @{row:hour}), @{row:document_executed}]
        	</te:for>
        ]);

        var chart1 = new google.visualization.AnnotatedTimeLine(document.getElementById('chart_div1'));
        chart1.draw(data, {displayAnnotations: true});

        // graf 2
        var data = new google.visualization.DataTable();
		data.addColumn('datetime', 'Dátum');
		data.addColumn('number', 'Generation time');
        data.addRows([
        	<?php $n = 0; ?>
        	<te:for each="@{data2:data} as @{row}">
        		<?php
        			if(strlen(@{request:month})) {
						$year = intval(substr(@{request:month}, 0, 4));
						$month = intval(substr(@{request:month}, 5, 2));

						if(@{row:year} != $year || @{row:month} != $month) {
							continue;
						}
					}
        		?>
        		<?php $n++; if($n!=1) { echo ','; } ?>
	        	[new Date(@{row:year}, @{row:month} , @{row:day}, @{row:hour}), <?php echo @{row:document_executed}?@{row:document_duration_sum}/@{row:document_executed}:0; ?>]
        	</te:for>
        ]);

        var chart2 = new google.visualization.AnnotatedTimeLine(document.getElementById('chart_div2'));
        chart2.draw(data);

      }
    </script>

	<h3>Počet zobrazených dokumentov</h3>
    <te:for each="@{data1:months} as @{kmonth}=>@{month}">
    	<te:if test="@{request:month} == @{kmonth}">
    			<a href="/@{doc}?month=@{kmonth}"><strong>@{month}</strong></a>
    		<te:else />
    			<a href="/@{doc}?month=@{kmonth}">@{month}</a>
    	</te:if>
    </te:for>
	<div id='chart_div1' style='width: 680px; height: 240px;'></div>
	<h3>Čas generovania dokumentu</h3>
    <te:for each="@{data1:months} as @{kmonth}=>@{month}">
    	<te:if test="@{request:month} == @{kmonth}">
    			<a href="/@{doc}?month=@{kmonth}"><strong>@{month}</strong></a>
    		<te:else />
    			<a href="/@{doc}?month=@{kmonth}">@{month}</a>
    	</te:if>
    </te:for>
	<div id='chart_div2' style='width: 680px; height: 240px;'></div>

</truEngine-document>
