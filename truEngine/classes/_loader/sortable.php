<?php

class sortable_gClass extends source_gClass
{
    protected function getData()
    {
        $type_pVar = $this->params['type'];
        $sort_pVar = $this->params['sort'];
        $sort_pVar = trim($sort_pVar, ' ,');
        $sort_pVar = explode(',', $sort_pVar);

        switch($type_pVar) {
            case 'modul':
            case 'program':
            case 'predmet':
            case 'kategoria':
            case 'podkategoria':
                foreach($sort_pVar as $k_pVar=>$v_pVar) {
                    $sql_pVar = 'UPDATE `%titems_test_questions__values` SET enum_value_order = %d WHERE enum_id = %d';
                    db_public_gClass::execute_gFunc($sql_pVar, __FILE__, __LINE__, array($k_pVar*10, $v_pVar));
                }
                break;
        }

        return(array($sort_pVar));
    }
}

class sortable extends sortable_gClass {}
