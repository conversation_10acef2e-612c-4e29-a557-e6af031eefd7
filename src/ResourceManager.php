<?php

namespace Mixedtype\Mixedtype;

use Illuminate\Support\Facades\Cookie;
use Illuminate\Support\Str;
use Mixedtype\Mixedtype\Models\Resource;
use Mixedtype\Mixedtype\Models\ResourceType;
use Mixedtype\Mixedtype\Models\Uuid;

class ResourceManager
{
    static private ?int $pcUuidNumericId = null;
    static private $invalidated = false;

    static function getPcUuidNumericId() : int
    {
        $expireDays = 60;

        if(self::$pcUuidNumericId !== null) {
            return self::$pcUuidNumericId;
        }

        $userId = auth()->id();
        $uuid = null;
        $uuidString = Cookie::get('key', null);

        if($uuidString === null) {
            $uuidString = (string)Str::uuid();
        }
        else {
            $uuids = Uuid::where('uuid', $uuidString)
                ->get();
            if(!$uuids->count()) {
                $uuidString = (string)Str::uuid();
            }
            else {
                $uuid = $uuids->where('user_id', $userId)->first();
            }
        }

        if($uuid === null) {
            $uuid = new Uuid();
            $uuid->uuid = $uuidString;
            $uuid->user_id = $userId;
            $uuid->access_count = 0;
            $uuid->created_at = now();
        }
        $uuid->access_count++;
        $uuid->expires_at = now()->addDays($expireDays);
        $uuid->updated_at = now();
        $uuid->save();

        Cookie::queue('key', $uuidString, 60*24*$expireDays);

        self::$pcUuidNumericId = $uuid->id;
        return self::$pcUuidNumericId;
    }

    static function resourceAlloc($resourceTypeStr, $resourceId, $allocatorId, $timeoutMins = 3, $deleteMins = 6) : void
    {
        $resourceTypeId = ResourceType::getResourceTypeId($resourceTypeStr);
        if($resourceTypeId === null) {
            throw new \Exception("Resource type not found ($resourceTypeStr)");
        }

        $resource = Resource::where('resource_type_id', $resourceTypeId)
            ->where('resource_id', $resourceId)
            ->where('allocator_id', $allocatorId)
            ->first();
        if($resource === null) {
            $resource = new Resource();
            $resource->resource_type_id = $resourceTypeId;
            $resource->resource_id = $resourceId;
            $resource->allocator_id = $allocatorId;
            $resource->first_allocated_at = now();
        }
        $resource->last_allocated_at = now();
        $resource->timeouted = false;
        $resource->timeout_at = now()->addMinutes($timeoutMins);
        $resource->delete_at = now()->addMinutes($deleteMins);
        $resource->save();

        self::invalidate();
    }

    static function resourceGet($resourceTypeStr, $resourceId, $allocatorId = null)
    {
        self::invalidate();

        $resourceTypeId = ResourceType::getResourceTypeId($resourceTypeStr);
        if($resourceTypeId === null) {
            throw new \Exception("Resource type not found ($resourceTypeStr)");
        }

        return Resource::where('resource_type_id', $resourceTypeId)
            ->where('resource_id', $resourceId)
            ->when($allocatorId, function($query) use ($allocatorId) {
                return $query->where('allocator_id', $allocatorId);
            })
            ->get();
    }

    static public function invalidate() : void
    {
        if(self::$invalidated) {
            return;
        }
        Uuid::where('expires_at', '<', now())->delete();
        Resource::where('delete_at', '<', now())->delete();
        Resource::where('timeout_at', '<', now())->update(['timeouted' => true]);
        self::$invalidated = true;
    }
}
