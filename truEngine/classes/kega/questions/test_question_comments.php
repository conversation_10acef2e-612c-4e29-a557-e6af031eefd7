<?php

class test_question_comments_gClass extends table_gClass
{
    protected function initTable_gFunc()
    {
        if(isset($this->params['delete_id'])) {
            $comment_pVar = items_gClass::getItemComment_gFunc('test_questions', $this->params['delete_id']);
            if(is_array($comment_pVar)) {
                $user_id_pVar = session_gClass::getUserDetail_gFunc('user_id');
                $deleteAccess_pVar = false;
                if($comment_pVar['user_id'] == $user_id_pVar) {
                    if(session_gClass::userHasRightsAccess_gFunc(s_test_delete_owner_comment)) {
                        $deleteAccess_pVar = true;
                    }
                }
                if(!$deleteAccess_pVar && session_gClass::userHasRightsAccess_gFunc(s_test_delete_comment)) {
                    $deleteAccess_pVar = true;
                }

                if($deleteAccess_pVar) {;
                    items_gClass::deleteItemComment_gFunc('test_questions', $this->params['delete_id']);
                }
            }
            unset($this->params['delete_id']);
        }

        if(session_gClass::userHasRightsAccessAction_gFunc(s_test_show_comments)) {
            $comments_pVar = items_gClass::getItemComments_gFunc('test_questions', $this->params['item_id']);
            $this->setData_gFunc($comments_pVar);
        }
        else {
            $this->setData_gFunc(array());
        }

        if(isset($this->params['columns'])) {
            $this->setColumnsFromString_gFunc($this->params['columns']);
        }
    }
}

class test_question_comments extends test_question_comments_gClass { }
