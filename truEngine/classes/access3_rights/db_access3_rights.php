<?php

class db_access3_rights_gClass extends db_gClass
{
    static public function loadRights_gFunc($role_name_pVar, $user_id_pVar, &$from_pVar)
    {

        $ret_pVar = array();

        if(!empty($role_name_pVar)) {
            $query_string_pVar = 'SELECT * FROM `%taccess__roles_rights` WHERE `role_id`=%s';
            $data_pVar = self::getResultArray_gFunc($query_string_pVar, __FILE__, __LINE__, $role_name_pVar);
            foreach ($data_pVar as $k_pVar=>$v_pVar) {
                if($v_pVar['role_right_state'] === 'allow') {
                    $ret_pVar[(int)$v_pVar['role_right_id']] = true;
                    session_session_gClass::setFromRights_gFunc($from_pVar, $v_pVar['role_right_id'], true, 'role', $role_name_pVar);
                }
                else {
                    if($role_name_pVar != 'superuser') {
                        $ret_pVar[(int)$v_pVar['role_right_id']] = false;
                    }
                    session_session_gClass::setFromRights_gFunc($from_pVar, $v_pVar['role_right_id'], false, 'role', $role_name_pVar);
                }
            }
        }

        if(is_numeric($user_id_pVar) && $user_id_pVar) {
            $query_string_pVar = 'SELECT * FROM `%taccess__users_rights` WHERE `user_id`=%d';
            $data_pVar = self::getResultArray_gFunc($query_string_pVar, __FILE__, __LINE__, $user_id_pVar);
            foreach ($data_pVar as $k_pVar=>$v_pVar) {
                if($v_pVar['user_right_state'] === 'allow') {
                    if(!isset($ret_pVar[(int)$v_pVar['user_right_id']])) {
                        $ret_pVar[(int)$v_pVar['user_right_id']] = true;
                    }
                    session_session_gClass::setFromRights_gFunc($from_pVar, $v_pVar['user_right_id'], true, 'user', $user_id_pVar);
                }
                else {
                    if($role_name_pVar != 'superuser') {
                        $ret_pVar[(int)$v_pVar['user_right_id']] = false;
                    }
                    session_session_gClass::setFromRights_gFunc($from_pVar, $v_pVar['user_right_id'], false, 'user', $user_id_pVar);
                }
            }
        }

        return($ret_pVar);
    }

    static function getRoles_gFunc($index_by_pVar = false)
    {
        $sql_pVar = 'SELECT `v`.`enum_field_value` as `name` ,`v`.`' . main_gClass::getLanguage_gFunc() . '_enum_field_name_item` AS `title` FROM %titems_users__values as `v`
						LEFT JOIN `%titems_users__fields` as `f` ON `v`.`enum_field_id` = `f`.`field_id`
						WHERE `f`.`tag` = \'user_role\'
						ORDER BY `v`.`enum_value_order`';
        $roles_pVar = self::getResultArray_gFunc($sql_pVar, __FILE__, __LINE__, false, $index_by_pVar);
        return($roles_pVar);
    }

    static function getRoleRights_gFunc($roleName_pVar)
    {
        $sql_pVar = '';

        //$rights_pVar = self::getResultArray_gFunc($sql_pVar, __FILE__, __LINE__, false, '')
    }

}
