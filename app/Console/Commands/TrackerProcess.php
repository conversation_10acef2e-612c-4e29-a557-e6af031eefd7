<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Mixedtype\Tracker\Processor;

class TrackerProcess extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'tracker:process';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        Processor::run();
    }
}
