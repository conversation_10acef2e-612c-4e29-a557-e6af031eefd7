<?php 

if(!isStamp_gFunc('access4_groups', 'vytvorenie tabuliek pre modul access4_groups')) {
	$sql_pVar = "
		CREATE TABLE IF NOT EXISTS `%taccess__groups` (
		  `group_id` int(10) unsigned NOT NULL auto_increment,
		  `group_type_id` int(10) unsigned NOT NULL,
		  `sk_group_name` varchar(255) NOT NULL default '',
		  PRIMARY KEY  (`group_id`)
		) ENGINE=InnoDB;
	";
	if(!db_public_gClass::execute_gFunc($sql_pVar, __FILE__, __LINE__)) {
		return(false);
	}

	$sql_pVar = "
		CREATE TABLE IF NOT EXISTS `%taccess__groups_types` (
		  `group_type_id` int(10) unsigned NOT NULL auto_increment,
		  `sk_group_type_name` varchar(255) NOT NULL default '',
		  PRIMARY KEY  (`group_type_id`)
		) ENGINE=InnoDB;
	";
	if(!db_public_gClass::execute_gFunc($sql_pVar, __FILE__, __LINE__)) {
		return(false);
	}

	$sql_pVar = "
		CREATE TABLE IF NOT EXISTS `%taccess__groups_rights` (
		  `rule_id` int(10) unsigned NOT NULL auto_increment,
		  `group_id` int(10) unsigned NOT NULL,
		  `group_right_id` int(10) unsigned NOT NULL,
		  `group_right_state` enum('allow', 'deny'),
		  `group_right_object` int(10) unsigned default NULL,
		  PRIMARY KEY  (`rule_id`)
		) ENGINE=InnoDB;
	";
	if(!db_public_gClass::execute_gFunc($sql_pVar, __FILE__, __LINE__)) {
		return(false);
	}

	$sql_pVar = "
		CREATE TABLE IF NOT EXISTS `%taccess__groups_users` (
		  `rule_id` int(10) unsigned NOT NULL auto_increment,
		  `group_id` int(10) unsigned NOT NULL,
		  `user_id` int(10) unsigned NOT NULL,
		  PRIMARY KEY  (`rule_id`)
		) ENGINE=InnoDB;
	";
	if(!db_public_gClass::execute_gFunc($sql_pVar, __FILE__, __LINE__)) {
		return(false);
	}

	$sql_pVar = "
		CREATE TABLE IF NOT EXISTS `%taccess__groups_roles` (
		  `rule_id` int(10) unsigned NOT NULL auto_increment,
		  `group_id` int(10) unsigned NOT NULL,
		  `role_id` varchar(255) NOT NULL,
		  PRIMARY KEY  (`rule_id`)
		) ENGINE=InnoDB;
	";
	if(!db_public_gClass::execute_gFunc($sql_pVar, __FILE__, __LINE__)) {
		return(false);
	}
	
	$sql_pVar = 'INSERT INTO `%taccess__groups_types` (`group_type_id`, `sk_group_type_name`)
					VALUES (1, \'Základné skupiny\')';
	if(!db_public_gClass::execute_gFunc($sql_pVar, __FILE__, __LINE__)) {
		return(false);
	}	

	stamp_gFunc('access4_groups', 'vytvorenie tabuliek pre modul access4_groups');
}

return(true);
