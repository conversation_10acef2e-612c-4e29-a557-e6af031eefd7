
		var teEdit = new Object();
		var teEditIds = new Array();
		var teEditToolbar = false;
		
		window.addEvent('domready', teEditInit);
		
		function teEditInit()
		{			
			getElementsByClassName('doc_edit_mouseout').each(
					function (el) {
						teEditIds[teEditIds.length] = el.id;
					}
			);
			
			teEditToolbar = document.createElement('div');
			teEditToolbar.className = 'doc_edit_toolbar_container';
			teEditToolbar.setAttribute("id",'doc_edit_toolbar_container');
			
			var buttons = new Array('bold', 'italic', 'underline', 'undo', 'redo', 'indent', 'outdent', '<h2>');
			
			for(i = 0; i < buttons.length; i++) {
				teEditToolbar.innerHTML = teEditToolbar.innerHTML + "<img onclick=\"document.execCommand('" + buttons[i] + "',null,false);\" src=\"/images/edit/" + buttons[i] + ".png\" />";
			}
			teEditToolbar.innerHTML = teEditToolbar.innerHTML + '<a href="#" onclick="teEditBlockEnd(0); return(false);" class="doc_edit">Koniec</a>';
			document.body.appendChild(teEditToolbar);
		}
		
		function teEditBlockMouseOver(block)
		{
			if(teEdit[block.id]) {
		
			}
			else {
				block.className='doc_edit_mouseover';
			}
		}
		
		function teEditBlockMouseOut(block)
		{
			if(teEdit[block.id]) {
				
			}
			else {
				block.className='doc_edit_mouseout';
			}
		}
		
		function teEditBlockStart(block, target)
		{
			teEditBlockEnd(0);
			teEdit[block.id] = target;
			block.contentEditable = true;
			block.className='doc_edit_mouseout';
		
			pos = GetAbsPosition(block);
			$('doc_edit_toolbar_container').style.display = 'block';
			$('doc_edit_toolbar_container').style.left = (pos.x - 1) + 'px';
			$('doc_edit_toolbar_container').style.top = (pos.y - $('doc_edit_toolbar_container').offsetHeight - 2) + 'px';
			
		}
		
		function teEditBlockEnd(block, key)
		{
			if(block == 0) {
				for (var i in teEdit) {
					if(teEdit[i]) {
						teEditBlockEnd($(i), i);
					}
				}
				return;
			}
			block.contentEditable = 'inherit';
			if($('doc_edit_toolbar_container')) {
				$('doc_edit_toolbar_container').style.display = 'none';
			}
			
			var req = new Request({
				method: 'post',
				url: web_dir,
				data: {
					'ajax' : 'true' ,
					'doc' : 'sk/ajax/doc-edit',
					'data' : block.innerHTML,
					'key' : key,
					'target' : teEdit[block.id]
				},
				onSuccess: function(responseText, responseXML) {
					if(getAjaxResponse(responseXML, 'result') == '1') {
						/*
						var sel = $('select_' + selector_id);
						removeAllOptions(sel);
						
						var data = responseXML.getElementsByTagName('data');
						var items = data[0].getElementsByTagName('item');
						
						for(var i = 0; i < items.length; i++) {
							var id = getAjaxResponse(items[i], 'id');
							var label = getAjaxResponse(items[i], 'label');
							appendOldSchool(sel, label, id);
						}
						unselectAllOptions(sel);
						*/
					}
					else {
						alert('Dáta sa nepodarilo uložiť.');
					}
				},
				onFailure: function(xhr) {
					alert('Dáta sa nepodarilo uložiť.');
				}
				}).send();
			
				teEdit[block.id] = false;
		}
		
		function teEditSwitchLinks(disable)
		{
			if(disable) {
				var a = document.getElementsByTagName('a');
				for(i = 0; i < a.length; i++) {
					if(a[i].className != 'doc_edit') {
						a[i].setAttribute("onclick", "return(false);");
					}
				}
				$('doc_edit_switch_links1').style.display = 'none';
				$('doc_edit_switch_links2').style.display = 'inline';
			}
			else {
				var a = document.getElementsByTagName('a');
				for(i = 0; i < a.length; i++) {
					if(a[i].className != 'doc_edit') {
						a[i].setAttribute("onclick", "return(true);");
					}
				}
				$('doc_edit_switch_links1').style.display = 'inline';
				$('doc_edit_switch_links2').style.display = 'none';
			}
		}
		
		