<div id="{{ $getComponentId() }}" x-data>
<table class="table table-selectable card-table table-vcenter datatable">
    <thead>
    <tr>
{{--        <th>#</th>--}}
        <th>Názov literatúry</th>
        <th><PERSON>ri</th>
        <th>Info</th>
        <th></th>
    </tr>
    </thead>
    <tbody>

{{--        alpine js render table from items--}}
        <template x-for="item in $store.{{ \Mixedtype\Components\ComponentIdManager::getComponentId() }}.items">
            <tr>
                <td style="max-width:600px">
                    <a href="#" class="text-reset" tabindex="-1" x-text="item.name"></a><br />
                    <ul>
                        <template x-for="index in item.kapitola.length">
                            <li x-text="item.kapitola"></li>
                        </template>
                    </ul>
                </td>
            </tr>
        </template>



        @php $n = 0; @endphp
        @foreach($items as $item)
            <tr>
{{--                <td class="w-1">{{ ++$n }}</td>--}}
                <td style="max-width:600px">
                    <a href="#" class="text-reset" tabindex="-1">{{ $item->name }}</a><br />
                    @php
                        $kapitoly = explode('|', $item->kapitola);
                        $links = explode('|', $item->link);
                        $keywords = explode('|', $item->keywords);
                    @endphp
                    @if(count($kapitoly) > 1 || !empty($kapitoly[0]) || !empty($links[0]) || !empty($keywords[0]))

                        <ul>
                            @foreach($kapitoly as $index => $kapitola)
                                <li>
                                    {{ implode(', ', array_filter([ $kapitoly[$index], $links[$index]], function($value) { return !empty($value); })) }}<br />
                                    <small class="text-muted">{{ $keywords[$index] }}</small>
                                </li>
                            @endforeach
                        </ul>
                    @endif
                </td>
                <td>{{ $item->autor }}</td>
                <td>
                    {{ $item->type }}<br />
                    {{ $item->vydavatelstvo }}<br />
                    {{ $item->year }}<br />
                    {{ $item->info }}
                </td>
                <td class="text-end">
                    <span class="dropdown">
                      <button class="btn dropdown-toggle align-text-top" data-bs-boundary="viewport" data-bs-toggle="dropdown">...</button>
                      <div class="dropdown-menu dropdown-menu-end">
                        <a class="dropdown-item" href="{{ \Mixedtype\Multisite\Multisite::route('literatura.edit', null, null, ['book' => $item->qmd5]) }}"> Upraviť </a>
                        <a class="dropdown-item" href="/sk/otazky/otazky?filter={{ urlencode('literatura_md5=LIKE(%' . $item->qmd5 . '%)') }}"> Zobraziť otázky </a>
                        <a class="dropdown-item text-red" href="#" x-on:click="if (confirm('Naozaj chcete zmazať túto literatúru?')) $store.{{ \Mixedtype\Components\ComponentIdManager::getComponentId() }}.remoteGet('delete', { 'book': '{{ $item->qmd5 }}' })"> Zmazať </a>
                      </div>
                    </span>
                </td>
            </tr>
        @endforeach
    </tbody>
</table>
    {{ $slot }}
    <style>
        [x-cloak] { display: none !important; }
    </style>
    <script>
        document.addEventListener('alpine:init', () => {

            class @alpineComponentClass{{'_final'}} extends AlpineComponent {

                initCustom() {
                    // alert(4);
                }
            }

            if(typeof @alpineComponentClass === "function") {
                Object.getOwnPropertyNames(@alpineComponentClass.prototype)
                    .filter(prop => prop != 'constructor')
                    .forEach(prop => @alpineComponentClass{{'_final'}}.prototype[prop] = @alpineComponentClass.prototype[prop])

                //@alpineComponentClass{{'_final'}}.prototype.test = function() {}
            }
            Alpine.store('{{ $getComponentId() }}', new @alpineComponentClass{{'_final'}}('{{ $getComponentId() }}'))
            Alpine.store('{{ $getComponentId() }}').setFullUrl('{{ \Mixedtype\Multisite\Multisite::route('component', null, null, ['component_id' => $getComponentId(), 'action' => '']) }}');
            Alpine.store('{{ $getComponentId() }}').initCustom()
            Alpine.store('{{ $getComponentId() }}').items = @json($items)


            //todo: vykreslit tabulku cez alpine

        });
    </script>

</div>
