<?php

namespace Mixedtype\FormsNew\InputComponents;

use Closure;
use Illuminate\View\View;
use Mixedtype\FormsNew\FieldComponent\Field;

class Input extends Field
{
    public function __construct(public string $flags = '', public string $config = '{}')
    {
        parent::__construct($flags, $config);
    }
    /**
     * Get the view / contents that represent the component.
     */
    public function render(): View|Closure|string
    {
        return view('mixedtype-formsnew::components.inputs.input');
    }
}
