<?xml version="1.0" encoding="utf-8" ?>
<truEngine-document xmlns:te="http://www.truengine.sk">

<?php
if(!isset(@{items_name})) {
	@{items_name} = 'item';
}
?>

<script type="text/javascript">
	function show@{items_name}ListDetails(id, show)
	{
		if(show) {
			document.getElementById('more_' + id).style.display='block';
			document.getElementById('a_show_' + id).style.display='none';
			document.getElementById('a_hide_' + id).style.display='block';
			document.getElementById('a_show_' + id + '_2').style.display='none';
			document.getElementById('a_hide_' + id + '_2').style.display='block';
		}
		else {
			document.getElementById('more_' + id).style.display='none';
			document.getElementById('a_show_' + id).style.display='block';
			document.getElementById('a_hide_' + id).style.display='none';
			document.getElementById('a_show_' + id + '_2').style.display='block';
			document.getElementById('a_hide_' + id + '_2').style.display='none';
		}
	}
</script>
<te:for each="@{items} as @{k_item}=>@{item}">
	<div class="itemlist parametric-@{item:parametric}" te:if="substr(@{k_item}, 0, 1) != '_'">
		<div class="nav">
			<a href="#" onclick="show@{items_name}ListDetails(@{item:item_id}+'_'+@{k_item}, 1); return(false);" id="a_show_@{item:item_id}_@{k_item}" title="Viac detailov">+</a>
			<a href="#" onclick="show@{items_name}ListDetails(@{item:item_id}+'_'+@{k_item}, 0); return(false);" id="a_hide_@{item:item_id}_@{k_item}" style="display:none" title="Menej detailov">--</a>
		</div>
		<table cellpadding="0" cellspacing="0" style="width:100%">
			<tr>
				<td class="label" colspan="2" style="font-weight:bold; color:#222222;">
					<span te:print="no" class="autor">Autor: <te:include name="/sk/components/autor" autor="@user=@{item:owner_id}" /></span>
					<?php @{item_name} = @{item}[@{item:language} . '_name'];  ?>
					<span style="display:inline; font-variant:small-caps;">
						@{item_name}
					</span>

					<te:if test="count(@{item:foto})">
						<div class="clear"></div>
						<div style="padding:0px 10px 10px 0px; height:80px; width:80px; float:left;">
						 <?php @{src} = isset(@{item:otazka_media:0}) ? @{item:foto:0:src} : '' ; ?>
						 <?php @{thumbnail} = isset(@{item:foto:0:th1_}) ? @{item:foto:0:th1_:src} : '' ; ?>
						 <te:include name="/sk/components/movie" src="@{src}" thumbnail="@{thumbnail}" />
						</div>
					</te:if>


					<div te:if="!empty(@{item:description})" style="color:#444444;font-weight:normal;">
						<?php @{item_description} = @{item}[@{item:language} . '_description'];  ?>
						<?php echo htmlspecialchars_decode(@{item_description}); ?>
					</div>
					<hr class="clear" />
				</td>
			</tr>
			<tr>
				<td colspan="2">
					<img src="/images/icons/@{item:language}.jpg" alt="@{item:language}" title="@{item:language}" />&amp;nbsp;
					<te:if test="@{item:smer} != -1 &amp;&amp; @{item:smer} != ''"> <span class="lomka">/</span> <span style="color:red">smer @{item:smer}</span></te:if>
					<te:if test="@{item:rocnik} != -1 &amp;&amp; @{item:rocnik} != ''"> <span class="lomka">/</span> <span style="color:green">@{item:rocnik}</span></te:if>
					<span class="lomka">/</span> <span style="color:#1111cc"><te:string id="otázok" /> @{item:pocet_otazok}</span>
					<te:if test="isset(@{item:cas}) &amp;&amp; !empty(@{item:cas})">
						<span class="lomka">/</span> <span style="color:#888800"><te:string id="čas" /> @{item:cas} min</span>
					</te:if>
					&amp;nbsp;&amp;nbsp;&amp;nbsp;<a te:access="s_test_test_run" href="/testy/start?template_id=@{item:item_id}" style="margin-right:50px; font-weight:bold; font-size:12px;text-decoration:none;" title="@L{Spustiť skúšanie}">&gt;&gt;</a>
				</td>
			</tr>
			<tr>
				<td></td>
				<td>
						<a href="#" onclick="show@{items_name}ListDetails(@{item:item_id}+'_'+@{k_item}, 1); return(false);" id="a_show_@{item:item_id}_@{k_item}_2" title="Viac detailov">Zobraziť podrobnosti testu</a>
						<a href="#" onclick="show@{items_name}ListDetails(@{item:item_id}+'_'+@{k_item}, 0); return(false);" id="a_hide_@{item:item_id}_@{k_item}_2" style="display:none" title="Menej detailov">Skryť podrobnosti testu</a>
				</td>
			</tr>

		</table>
		<div id="more_@{item:item_id}_@{k_item}" style="display:none">
			<table cellpadding="0" cellspacing="0">
				<tr>
					<td class="label"><te:string id="Počet otázok" />:</td>
					<td>@{item:pocet_otazok}</td>
				</tr>
				<tr>
					<td class="label"><te:string id="Parametrický" />:</td>
					<td>@{item:parametric_enum_name_item}</td>
				</tr>
				<tr>
					<td class="label"><te:string id="Spôsob testovania" />:</td>
					<td>@{item:sposob_testovania_enum_name_item}</td>
				</tr>

				<tr>
					<td class="label"><te:string id="Minimálny počet možností" />:</td>
					<td>
						<te:if test="@{item:moznosti_min} == 0">
							<te:string id="Nenastavené" /><te:else />@{item:moznosti_min}
						</te:if>
					</td>
				</tr>
				<tr>
					<td class="label"><te:string id="Maximálny počet možností " />:</td>
					<td>
						<te:if test="@{item:moznosti_max} == 0">
							<te:string id="Nenastavené" /><te:else />@{item:moznosti_max}
						</te:if>
					</td>
				</tr>
				<tr>
					<td class="label"><te:string id="Spôsob testovania" />:</td>
					<td>@{item:sposob_testovania_enum_name_item}</td>
				</tr>
				<tr>
					<td class="label"><te:string id="Čas na testovanie (max)" />:</td>
					<td>
						<te:if test="@{item:cas} == 0">
							<te:string id="Nenastavené" /><te:else />@{item:cas} min
						</te:if>
					</td>
				</tr>

				<tr>
					<td class="label"><te:string id="Náhodné miešanie otázok" />:</td>
					<td>@{item:miesanie_otazok_enum_name_item}</td>
				</tr>
				<tr>
					<td class="label"><te:string id="Náhodné miešanie odpovedí" />:</td>
					<td>@{item:miesanie_odpovedi_enum_name_item}</td>
				</tr>

				<tr>
					<td class="label"><te:string id="Preferované kľúčové slová" />:</td>
					<td>@{item:preferred_keywords}</td>
				</tr>
				<tr>
					<td class="label"><te:string id="Preferovaní autori" />:</td>
					<td>@{item:preferred_authors}</td>
				</tr>

				<tr>
					<td class="label"><te:string id="Povoliť video a flash" />:</td>
					<td>@{item:media_enum_name_item}</td>
				</tr>
				<tr>
					<td class="label"><te:string id="Index obtiažnosti" />:</td>
					<td>@{item:index_obtiaznosti}</td>
				</tr>

				<tr>
					<td class="label"><te:string id="Ohodnotiť test" />:</td>
					<td>@{item:ohodnotit_test_enum_name_item}</td>
				</tr>
				<tr>
					<td class="label"><te:string id="Hall of fame" />:</td>
					<td>@{item:halloffame_enum_name_item}</td>
				</tr>

				<tr te:if="isset(@{item:hranica_uspesnosti}) &amp;&amp; @{item:hranica_uspesnosti}">
					<td class="label"><te:string id="Kritérium pre úspešné absolvovanie (%)" />:</td>
					<td>@{item:hranica_uspesnosti}</td>
				</tr>
				<tr te:if="isset(@{item:excelentne}) &amp;&amp; @{item:excelentne}">
					<td class="label"><te:string id="Kritérium pre excelentné absolvovanie (%)" />:</td>
					<td>@{item:excelentne}</td>
				</tr>
				<tr te:if="isset(@{item:typ_hodnotenia}) &amp;&amp; @{item:typ_hodnotenia}">
					<td class="label"><te:string id="Typ hodnotenia" />:</td>
					<?php ob_start(); ?>::<te:string id="@{item:typ_hodnotenia_enum_name_item} - vysvetlenie" /><?php @{tmp} = ob_get_contents(); ob_end_clean(); ?>
					<td><div style="float:left"><te:string id="@{item:typ_hodnotenia_enum_name_item}" />&amp;nbsp;&amp;nbsp;</div> <img class="info_icon tipz" src="/sk/images/icons/info_small.png" title="@{tmp}" /></td>
				</tr>

				<tr>
					<td colspan="2">
							<hr />
							<!--  <a te:access="s_test_template_new_test" href="/testy/novy-cvicny-test/?template_id=@{item:item_id}" style="margin-right:50px"><te:string id="Nový cvičný test" /></a>  -->
							<!--  <a te:access="s_test_template_new_official_test" href="/testy/novy-oficialny-test/?template_id=@{item:item_id}" style="margin-right:50px"><te:string id="Nový oficiálny test" /></a> -->
							<a te:access="s_test_test_run" href="/testy/start?template_id=@{item:item_id}" style="margin-right:50px"><te:string id="Spustiť skúšanie" /></a>
							<a te:access="s_test_generate_print" href="/testy/generovat-tlacove-predlohy?template_id=@{item:item_id}" style="margin-right:50px"><te:string id="Generovať tlačové predlohy" /></a>
							<te:if test="!isset(@{instances}) || @{instances} == 'yes'">
								<a te:access="s_test_list_instances" href="/testy/zoznam-testovani?test_id=@{item:item_id}" style="margin-right:50px"><te:string id="Zoznam testovaní" /></a>
							</te:if>
							<te:if test="isset(@{item:halloffame}) &amp;&amp; @{item:halloffame} == 'yes'">
								<a te:access="s_test_hall_of_fame" href="/testy/hall-of-fame?template_id=@{item:item_id}"><te:string id="Hall of fame" /></a>
							</te:if>
							<a te:access="s_test_literatura" href="/literatura/resers?template_id=@{item:item_id}" style="margin-right:50px"><te:string id="Rešerš&amp;nbsp;literatúry" /></a>
							<a te:access="s_test_edit_template" href="/testy/editovat-sablonu/?item_id=@{item:item_id}" style="margin-right:50px"><te:string id="Editovať&amp;nbsp;test" /></a>
							<a te:access="s_test_add_test" href="/@{doc}?copy_id=@{item:item_id}" style="margin-right:50px" onclick="return(confirm('@L{Chcete vytvoriť nový neparametrický test s rovnakými nastaveniami?}'));"><te:string id="Vytvoriť&amp;nbsp;neparametrický&amp;nbsp;test" /></a>
					</td>
				</tr>
			</table>
		</div>

		<div class="clear"></div>
	</div>
</te:for>

</truEngine-document>
