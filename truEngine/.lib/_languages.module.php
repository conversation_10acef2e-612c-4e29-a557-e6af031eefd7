<?php
if(isset($_SERVER['SCRIPT_FILENAME']) && strtolower($_SERVER['SCRIPT_FILENAME']) == strtolower(str_replace("\\",'/',__FILE__))) { header("HTTP/1.0 404 Not Found");  return(false); }
if(isset($GLOBALS['file__languages_pVar'])) return(true);
$GLOBALS['file__languages_pVar']=true;


class languageManager_gClass
{
	static function addLanguage_gFunc($lng_pVar, $languageName_pVar, $sourceLng_pVar = 'sk')
	{
		if(strlen($lng_pVar) != 2) {
			return(false);
		}
		
		$items_pVar = items_gClass::getItems_gFunc('');
		unset($items_pVar['filter']);
		unset($items_pVar['_order_by_fields']);
		foreach($items_pVar as $item_pVar) {
			if($item_pVar['name'] == '') {
				continue;
			}
			$languages_pVar = explode(',', $item_pVar['languages']);
			if(!in_array($lng_pVar, $languages_pVar)) {
				$languages_pVar[] = $lng_pVar;
				$languages_pVar = implode(',', $languages_pVar);
				$sql_pVar = 'UPDATE `%titems___data` SET `languages` = %s WHERE `item_id` = %d';
				db_public_gClass::execute_gFunc($sql_pVar, __FILE__, __LINE__, array($languages_pVar, $item_pVar['item_id']));
				db_items_gClass::applyLanguagesToTables_gFunc($item_pVar['name']);
			}
		}
		
		$sql_pVar = 'SHOW TABLES';
		$tables_pVar = db_public_gClass::getResultArray_gFunc($sql_pVar, __FILE__, __LINE__);
		$tblPrefix_pVar = main_gClass::getConfigVar_gFunc('table_prefix', 'db');
		
		foreach($tables_pVar as $table_pVar) {
			$table_pVar = reset($table_pVar);
			if(substr($table_pVar, 0, strlen($tblPrefix_pVar)) != $tblPrefix_pVar) {
				continue;
			}
			if(substr($table_pVar, 0, strlen($tblPrefix_pVar) + 6) == $tblPrefix_pVar . 'items_') {
				$is_items_pVar = true;
			}
			else {
				$is_items_pVar = false;
			}
			
			$sql_pVar = 'SHOW COLUMNS FROM `' . $table_pVar . '`';
			$fields_pVar = db_public_gClass::getResultArray_gFunc($sql_pVar, __FILE__, __LINE__, false, 'Field');
			$lngFields_pVar = array();
			foreach($fields_pVar as $k_pVar=>$v_pVar) {
				if(substr($k_pVar, 0, 3) == 'sk_') {
					$lngFields_pVar[] = $v_pVar;
				}
			}
			
			if(count($lngFields_pVar)) {
				foreach($lngFields_pVar as $field_pVar) {
					$fieldName_pVar = substr($field_pVar['Field'], 3);
					if(!$is_items_pVar) {
						if(!isset($fields_pVar[$lng_pVar . '_' . $fieldName_pVar])) {
							// insert field
							$sql_pVar = 'ALTER TABLE ' . $table_pVar . ' ADD COLUMN `' . $lng_pVar . '_' . $fieldName_pVar . '` ' . $field_pVar['Type'] . ' DEFAULT ' . (is_null($field_pVar['Default']) ? 'NULL':'%s') . ' ' . ($field_pVar['Null'] == 'YES' ? '':'NOT') . ' NULL AFTER `' . $field_pVar['Field'] . '`';
							db_public_gClass::execute_gFunc($sql_pVar, __FILE__, __LINE__, $field_pVar['Default']);
						}
						else {
							// update field, ak je to treba
						}
					}
					
					if(!preg_match('/' . $tblPrefix_pVar . 'items_.*__data/', $table_pVar) && $table_pVar != $tblPrefix_pVar . 'strings') {
						$sql_pVar = 'UPDATE ' . $table_pVar . ' SET `' . $lng_pVar . '_' . $fieldName_pVar . '` = `' . $sourceLng_pVar . '_' . $fieldName_pVar . '` WHERE `' . $lng_pVar . '_' . $fieldName_pVar . '` IS NULL';
						db_public_gClass::execute_gFunc($sql_pVar, __FILE__, __LINE__);
					}
				}
			}
		}
		
	}
}

class translate_form_gClass extends source_gClass {
	protected function getData()
	{
		$key_pVar = main_gClass::getInputString_gFunc('key', main_gClass::SRC_REQUEST_pVar);
		if(!empty($key_pVar)) {
			if(main_gClass::getSessionData_gFunc('translate_key') == $key_pVar) {
				$sData_pVar = main_gClass::getSessionData_gFunc('translate_data');
				foreach($sData_pVar as $k_pVar=>$v_pVar) {
					$update_pVar = main_gClass::getInputString_gFunc('str_' . $k_pVar, main_gClass::SRC_POST_pVar, false, null, true, true);
					if(!is_null($update_pVar)) {
						if(strpos($update_pVar, '//') !== false) {
							for($i=0; $i<300; $i++) {
								$update_pVar = str_replace('\\\\', '\\', $update_pVar);
							}
						}
						if(strpos($update_pVar, '&amp;') !== false) {
							for($i=0; $i<300; $i++) {
								$update_pVar = str_replace('&amp;amp;', '&', $update_pVar);
							}
						}
						$update_pVar = str_replace('&amp;', '&', $update_pVar);
						$update_pVar = str_replace('&lt;', '<', $update_pVar);
						$update_pVar = str_replace('&gt;', '>', $update_pVar);
						$sql_pVar = 'UPDATE `' . $v_pVar['table'] . '` SET `' . $v_pVar['field'] . '` = %xs WHERE ' . $v_pVar['key'];
						//echo $sql_pVar . '<br />' . $update_pVar . '<hr />';
						db_public_gClass::execute_gFunc($sql_pVar, __FILE__, __LINE__, $update_pVar);
					}
				}
			}
		}
		
		$from_pVar = main_gClass::getLanguage_gFunc();
		$to_pVar = $this->params['to'];
		
		$from_pVar = substr($from_pVar, 0, 2);
		$to_pVar = substr($to_pVar, 0, 2);
		
		$data_pVar = array();
		$tblPrefix_pVar = main_gClass::getConfigVar_gFunc('table_prefix', 'db');
		
		// naplnim z tbl strings
		$sql_pVar = 'SELECT `id`, `' . $from_pVar .'_text` as `from`, `' . $to_pVar .'_text` as `to` FROM `%tstrings`';
		$sql_pVar .= ' WHERE `' . $from_pVar .'_text` IS NOT NULL AND `' . $from_pVar .'_text` <> \'\'';
		if(isset($this->params['empty']) && intval($this->params['empty'])) {
			$sql_pVar .= ' AND (`' . $to_pVar .'_text` IS NULL OR `' . $to_pVar .'_text` = \'\')';
		}
		$tmp_pVar = db_public_gClass::getResultArray_gFunc($sql_pVar, __FILE__, __LINE__);
		foreach($tmp_pVar as $k_pVar=>$v_pVar) {
			$data_pVar[] = array('from'=>htmlspecialchars($v_pVar['from']), 'to'=>htmlspecialchars($v_pVar['to']), 'table'=>($tblPrefix_pVar . 'strings'), 'field'=>($to_pVar .'_text'), 'key'=>('`id`=' . $v_pVar['id']));
		}
		
		// naplnim z ostatnych tabuliek
		
		$sql_pVar = 'SHOW TABLES';
		$tables_pVar = db_public_gClass::getResultArray_gFunc($sql_pVar, __FILE__, __LINE__);
		
		foreach($tables_pVar as $table_pVar) {
			$table_pVar = reset($table_pVar);
			if(substr($table_pVar, 0, strlen($tblPrefix_pVar)) != $tblPrefix_pVar) {
				continue;
			}
			if($table_pVar == $tblPrefix_pVar . 'tests') {
				continue;
			}
			if(preg_match('/' . $tblPrefix_pVar . 'items_.*__data/', $table_pVar) || $table_pVar == $tblPrefix_pVar . 'strings') {
				continue;
			}
			
			$sql_pVar = 'SHOW COLUMNS FROM `' . $table_pVar . '`';
			$fields_pVar = db_public_gClass::getResultArray_gFunc($sql_pVar, __FILE__, __LINE__, false, 'Field');
			$lngFields_pVar = array();
			foreach($fields_pVar as $k_pVar=>$v_pVar) {
				if(substr($k_pVar, 0, 3) == $from_pVar . '_') {
					$lngFields_pVar[] = $v_pVar;
				}
			}
			
			if(count($lngFields_pVar)) {
				foreach($lngFields_pVar as $field_pVar) {
					$fieldName_pVar = substr($field_pVar['Field'], 3);
					if($fieldName_pVar == 'default_value') {
						continue;
					}
					if(isset($fields_pVar[$to_pVar . '_' . $fieldName_pVar])) {
						$sql_pVar = 'SHOW COLUMNS FROM ' . $table_pVar . ' WHERE `Key`=\'PRI\'';
						$pri_keys_pVar = db_public_gClass::getResultArray_gFunc($sql_pVar, __FILE__, __LINE__);
						if(count($pri_keys_pVar) != 1) {
							continue;
						}
						$key_name_pVar = $pri_keys_pVar[0]['Field'];
							
						$sql_pVar = 'SELECT `' . $key_name_pVar . '`, `' . $from_pVar .'_'. $fieldName_pVar .'` as `from`, `' . $to_pVar .'_' . $fieldName_pVar . '` as `to` FROM `' . $table_pVar . '`';
						$sql_pVar .= ' WHERE `' . $from_pVar .'_' . $fieldName_pVar . '` IS NOT NULL AND `' . $from_pVar .'_' . $fieldName_pVar . '` <> \'\''; 
						if(isset($this->params['empty']) && intval($this->params['empty'])) {
							$sql_pVar .= ' AND (`' . $to_pVar .'_' . $fieldName_pVar . '` IS NULL OR `' . $to_pVar .'_' . $fieldName_pVar . '` = \'\')';
						}
						$tmp_pVar = db_public_gClass::getResultArray_gFunc($sql_pVar, __FILE__, __LINE__);
						foreach($tmp_pVar as $k_pVar=>$v_pVar) {
							$data_pVar[] = array('from'=>htmlspecialchars($v_pVar['from']), 'to'=>$v_pVar['to'], 'table'=>$table_pVar, 'field'=>($to_pVar .'_' . $fieldName_pVar), 'key'=>('`' . $key_name_pVar . '`=' . addslashes($v_pVar[$key_name_pVar])));
						}						
					}
				}
			}
		}
				
		$key_pVar = uniqid('translate_', true);
		main_gClass::setPhpSessionVar_gFunc('translate_data', $data_pVar);
		main_gClass::setPhpSessionVar_gFunc('translate_key', $key_pVar);
		$data_pVar['key'] = $key_pVar;
		return($data_pVar);
	}
}

class translate_form extends translate_form_gClass {}

return(true);