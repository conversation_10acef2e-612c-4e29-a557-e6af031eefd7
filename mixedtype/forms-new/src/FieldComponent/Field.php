<?php

namespace Mixedtype\FormsNew\FieldComponent;


use Closure;
use Illuminate\View\View;

class Field extends \Illuminate\View\Component
{
    public $flagsArray = [];
    public $configArray = [];

    public function __construct(public string $flags = '', public string $config = '{}')
    {
        $flags = str_replace(' ', '|', $this->flags);
        $flags = str_replace(',', '|', $flags);
        $this->flags = $flags;
        $this->flagsArray = [];
        foreach(explode('|', $this->flags) as $flag) {
            $this->flagsArray[$flag] = true;
        }
        $this->config = str_replace("'", '"', $this->config);
        $this->configArray = json_decode($this->config, true);
    }

    function xText($prop, $hideIfEmpty = false)
    {
        $name = $this->attributes->get('name');
        $ret = ' x-text="$store[\''. \Mixedtype\Components\ComponentIdManager::getComponentId() . '\'].fields[\''. $name . '\'].'. $prop .'"';
        if($hideIfEmpty) {
            $ret .= ' x-cloak x-show="$store[\''. \Mixedtype\Components\ComponentIdManager::getComponentId() . '\'].fields[\''. $name . '\'].'. $prop .' && $store[\''. \Mixedtype\Components\ComponentIdManager::getComponentId() . '\'].fields[\''. $name . '\'].'. $prop .'.length"';
        }
        return $ret;
    }


    function xOn($event, $method)
    {
        // call local alpine.js method
        // todo: how can I define method on alpinejs component?
        return '';
    }


    function xError()
    {
        $name = $this->attributes->get('name');
        return '$store[\''. \Mixedtype\Components\ComponentIdManager::getComponentId() . '\'].fields[\''. $name . '\'].error';
    }

    function getForm()
    {
        $component = \Mixedtype\Components\ComponentIdManager::getComponent();
        if($component instanceof \Mixedtype\FormsNew\FormComponent\Form) {
            return $component;
        }
        throw new \Exception('Current component is not a form.');
    }


    /**
     * Get the view / contents that represent the component.
     */
    public function render(): View|Closure|string
    {
        return view('mixedtype-formsnew::components.field');
    }
}
