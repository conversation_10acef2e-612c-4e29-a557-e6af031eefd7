<?php
if(isset($_SERVER['SCRIPT_FILENAME']) && strtolower($_SERVER['SCRIPT_FILENAME']) == strtolower(str_replace("\\",'/',__FILE__))) { header("HTTP/1.0 404 Not Found");  return(false); }
if(isset($GLOBALS['file__backup_pVar'])) return(true);
$GLOBALS['file__backup_pVar']=true;

if(!modules_gClass::isModuleRegistred_gFunc('forms'))
{
    return(false);
}

define('BACKUP_EOL', CR . NL);

class restore_gClass
{
	private $fileName_pVar;
	private $file_pVar;
	private $imported_pVar;
	private $cache_pVar;
	
	function __construct($fileName_pVar)
	{
		$this->fileName_pVar = $fileName_pVar;
		$this->file_pVar = fopen($fileName_pVar, 'r');
		$this->imported_pVar = array();
		$this->cache_pVar = array();
		set_time_limit(120);
	}
	
	function __destruct()
	{
		if($this->file_pVar) {
			fclose($this->file_pVar);
		}
		unlink($this->fileName_pVar);
	}
	
	function restore_gFunc()
	{
		log_gClass::logQueriesOff_gFunc();

		$info_pVar = fgets($this->file_pVar);
		$info_pVar = unserialize($info_pVar);
		$settings_pVar = explode(',', $info_pVar['settings']);
		
		$logData_pVar = array('user_id'=>session_gClass::getUserDetail_gFunc('user_id'),
							  'datetime'=>'now()',
							  'type'=>'restore',
							  'settings'=>implode(',', $settings_pVar) . '; ' . $info_pVar['backup_time'],
							  'comment'=>addslashes(stripcslashes($info_pVar['comment'])));
		db_public_gClass::insertData_gFunc('%tbackup_log', '%d,%r,%s,%s,%s', $logData_pVar, __FILE__, __LINE__);
		
		log_gClass::write_gFunc('SYSTEM_DOCUMENT', '1');
		
		$imported_pVar = array();
		
		while(!feof($this->file_pVar)) {
			$header_pVar = $this->readHeader_gFunc();
			if($header_pVar === true) {
				continue;
			}
			if($header_pVar === false) {
				error_gClass::fatal_gFunc(__FILE__, __LINE__);
				break;
			}
			
			// inicializacia
			if(!isset($this->imported_pVar[$header_pVar['table']])) {
				$table_pVar = $header_pVar['table'];

				// items_*__data
				if(substr($table_pVar, 0, 6) == 'items_') {
					if(strlen($table_pVar) > 12 && substr($table_pVar, -6) == '__data') {
						$sql_pVar = 'DELETE FROM `%t' . $header_pVar['table'] . '`';
						db_public_gClass::execute_gFunc($sql_pVar, __FILE__, __LINE__);
						$sql_pVar = 'DELETE FROM `%t' . str_replace('__data', '__changes', $header_pVar['table']) . '`';
						db_public_gClass::execute_gFunc($sql_pVar, __FILE__, __LINE__);
						$sql_pVar = 'DELETE FROM `%t' . str_replace('__data', '__change_details', $header_pVar['table']) . '`';
						db_public_gClass::execute_gFunc($sql_pVar, __FILE__, __LINE__);
						$sql_pVar = 'DELETE FROM `%t' . str_replace('__data', '__comments', $header_pVar['table']) . '`';
						db_public_gClass::execute_gFunc($sql_pVar, __FILE__, __LINE__);
					}
					// items_*__tree__defs
					elseif(strlen($table_pVar) > 18 && substr($table_pVar, -12) == '__tree__defs') {
						$sql_pVar = 'DELETE FROM `%t' . $header_pVar['table'] . '`';
						db_public_gClass::execute_gFunc($sql_pVar, __FILE__, __LINE__);
						$sql_pVar = 'DELETE FROM `%t' . str_replace('__tree__defs', '__tree__values', $header_pVar['table']) . '`';
						db_public_gClass::execute_gFunc($sql_pVar, __FILE__, __LINE__);
					}
					// items_*__values:tree
					elseif(strlen($table_pVar) > 18 && substr($table_pVar, -13) == '__values:tree') {
						if(!empty($header_pVar['params'])) {
							$sName_pVar = substr($header_pVar['table'], 0 , - 13);
							$this->cache_pVar[$sName_pVar] = array();
							$this->cache_pVar[$sName_pVar]['tree_field_ids'] = array();
							$this->cache_pVar[$sName_pVar]['tree_field_names'] = array();
							$params_pVar = '\'' . implode('\',\'', explode(',', $header_pVar['params'])) . '\'';
							$sql_pVar = 'SELECT `field_id`, `tag` FROM `%t' . $sName_pVar . '__fields` WHERE `tag` IN (%r)';
							$fields_pVar = db_public_gClass::getResultArray_gFunc($sql_pVar, __FILE__, __LINE__, $params_pVar);
							foreach($fields_pVar as $v_pVar) {
								$this->cache_pVar[$sName_pVar]['tree_field_names'][$v_pVar['field_id']] = $v_pVar['tag'];
								$this->cache_pVar[$sName_pVar]['tree_field_ids'][$v_pVar['tag']] = $v_pVar['field_id'];
							}
							$sql_pVar = 'DELETE FROM `%t' . $sName_pVar . '__values` WHERE `enum_field_id` IN (%r)';
							db_public_gClass::execute_gFunc($sql_pVar, __FILE__, __LINE__, implode(',', $this->cache_pVar[$sName_pVar]['tree_field_ids']));
						}
					}
				}
				elseif($table_pVar === 'files_data') {
					// nic nerobim...
				}
				else {
						$sql_pVar = 'TRUNCATE TABLE `%t' . $table_pVar . '`';
						db_public_gClass::execute_gFunc($sql_pVar, __FILE__, __LINE__);
				}
				
				$this->imported_pVar[$header_pVar['table']] = true;
			}
		
			$tableType_pVar = 0;
			$table_pVar = $header_pVar['table'];
			
			if($header_pVar['table'] == 'items__data') {
				$tableType_pVar = 1;
			}
			elseif(substr($header_pVar['table'], 0, 6) == 'items_') {
				if(strlen($header_pVar['table']) > 19 && substr($header_pVar['table'], -13) == '__values:tree') {
					if(!empty($header_pVar['params'])) {
						$tableType_pVar = 2;
						$table_pVar = substr($header_pVar['table'], 0, -5);
						array_shift($header_pVar['fields']);
						$sName_pVar = substr($header_pVar['table'], 0 , - 13);
						$sql_pVar = 'SELECT MAX(`enum_id`) FROM `%t' . $table_pVar . '`';
						$this->cache_pVar[$sName_pVar]['next_enum_id'] = db_public_gClass::getField_gFunc($sql_pVar, __FILE__, __LINE__);
						$this->cache_pVar[$sName_pVar]['enum_ids_map'] = array();
						$this->cache_pVar[$sName_pVar]['next_enum_id']++;
					}
				}
				elseif(strlen($table_pVar) > 20 && substr($table_pVar, -14) == '__tree__values') {
					$tableType_pVar = 3;
				}
			}
			elseif($table_pVar === 'files_data') {
				$tableType_pVar = 4;
			}

			// tu nastavim fieldy, ktore mam unsetnut.
			$unsetFields_pVar = array();
			$p_pVar = array_search('kbytes', $header_pVar['fields']);
			if($p_pVar !== false) { $unsetFields_pVar[] = $p_pVar; }
			$p_pVar = array_search('korektnost', $header_pVar['fields']);
			if($p_pVar !== false) { $unsetFields_pVar[] = $p_pVar; }
			
			
			// unsetnem fieldy z hlavicky
			rsort($unsetFields_pVar, SORT_NUMERIC);
			foreach($unsetFields_pVar as $v_pVar) {
				unset($header_pVar['fields'][$v_pVar]);
			}
			
			// data
			$query_pVar = '';
			$query_data_pVar = array();
			$query_count_pVar = 0;
			for($i_pVar = 0; $i_pVar < $header_pVar['count']; $i_pVar++) {
				$str_pVar = fgets($this->file_pVar);
				$str_pVar = trim($str_pVar);
				$tmp_pVar = explode(chr(0xb7), $str_pVar);
				foreach($tmp_pVar as $k_pVar=>$v_pVar) {
					if($v_pVar === '\N') {
						$tmp_pVar[$k_pVar] = null;
					}
					else {
						$tmp_pVar[$k_pVar] = str_replace(chr(0xa8), LF, str_replace(chr(0xb8), CR, $v_pVar));
					}
				}
				
				if($tableType_pVar == 1) {
					continue;
				}
				elseif($tableType_pVar == 2) { // items___values:tree
					$tmp_pVar[2] = $this->cache_pVar[$sName_pVar]['tree_field_ids'][$tmp_pVar[0]];
					$this->cache_pVar[$sName_pVar]['enum_ids_map'][$tmp_pVar[1]] = $this->cache_pVar[$sName_pVar]['next_enum_id'];
					$tmp_pVar[1] = $this->cache_pVar[$sName_pVar]['next_enum_id'];
					array_shift($tmp_pVar);
					$this->cache_pVar[$sName_pVar]['next_enum_id']++;
				}
				elseif($tableType_pVar == 3) { // items___tree_values
					if(!is_null($tmp_pVar[2]) && isset($this->cache_pVar[$sName_pVar]['enum_ids_map'][$tmp_pVar[2]])) {
						$tmp_pVar[2] = $this->cache_pVar[$sName_pVar]['enum_ids_map'][$tmp_pVar[2]];
					}
					if(!is_null($tmp_pVar[3]) && isset($this->cache_pVar[$sName_pVar]['enum_ids_map'][$tmp_pVar[3]])) {
						$tmp_pVar[3] = $this->cache_pVar[$sName_pVar]['enum_ids_map'][$tmp_pVar[3]];
					}
					if(!is_null($tmp_pVar[4]) && isset($this->cache_pVar[$sName_pVar]['enum_ids_map'][$tmp_pVar[4]])) {
						$tmp_pVar[4] = $this->cache_pVar[$sName_pVar]['enum_ids_map'][$tmp_pVar[4]];
					}
				}
				elseif($tableType_pVar == 4) {
					if($tmp_pVar[0] === 'files_data_end') {
						break;
					}
					$this->restoreFile_gFunc($tmp_pVar);
					continue;
				}

				// unsetnem fieldy
				foreach($unsetFields_pVar as $v_pVar) {
					unset($tmp_pVar[$v_pVar]);
				}
				
				// execute
				if(empty($query_pVar)) {
					$query_pVar = 'INSERT INTO `%t' . $table_pVar . '` (`' . implode('`,`', $header_pVar['fields']) . '`) VALUES ';
				}
				if($query_count_pVar) {
					$query_pVar .= ', ';
				}
				$query_pVar .= '(%s' . str_repeat(', %s', count($header_pVar['fields']) - 1) . ')';
				foreach($tmp_pVar as $v_pVar) {
					$query_data_pVar[] = $v_pVar;
				}
				$query_count_pVar++;
				if($query_count_pVar > 300) {
					db_public_gClass::execute_gFunc($query_pVar, __FILE__, __LINE__, $query_data_pVar);
					$query_pVar = '';
					$query_data_pVar = array();
					$query_count_pVar = 0;
				}
			}
			
			// done
			if($query_count_pVar) {
				db_public_gClass::execute_gFunc($query_pVar, __FILE__, __LINE__, $query_data_pVar);
				$query_pVar = '';
				$query_data_pVar = array();
				$query_count_pVar = 0;
			}
			
			if($tableType_pVar == 2 && modules_gClass::isModuleRegistred_gFunc('items')) { // items___values:tree
				db_items_gClass::applyLanguagesToTables_gFunc(substr($sName_pVar, 6));
			}
		}
	}
	
	private function readHeader_gFunc()
	{
		$header_pVar = fgets($this->file_pVar);
		$header_pVar = trim($header_pVar);
		if(!strlen($header_pVar)) {
			return(true);
		}
		$info_pVar = explode(chr(0xb7), $header_pVar);
		if(!isset($info_pVar[2])) {
			$info_pVar[2] = '';
		}
		if(!isset($info_pVar[3])) {
			$info_pVar[3] = '';
		}

		if($info_pVar[0] != '@#') {
			error_gClass::error_gFunc(__FILE__, __LINE__);
			return(false);
		}
		$ret_pVar = array();
		$ret_pVar['table'] = $info_pVar[1];
		$ret_pVar['count'] = $info_pVar[2];
		$ret_pVar['fields'] = explode('|', $info_pVar[3]);
		$ret_pVar['params'] = isset($info_pVar[4])? $info_pVar[4]:false;
		
		if($ret_pVar['table'] === 'files_data') {
			$ret_pVar['count'] = 99999;
		}
		return($ret_pVar);
	}

	private function restoreFile_gFunc(&$data_pVar)
	{
		$sql_pVar = 'SELECT * FROM `%tfiles` WHERE `md5` = \'' . $data_pVar[0] . '\'';
		$files_pVar = db_public_gClass::getResultArray_gFunc($sql_pVar, __FILE__, __LINE__);
		$filedefs_pVar = main_gClass::getConfigSectionVar_gFunc('files');
		foreach($files_pVar as $file_pVar) {
			$fileName_pVar = $file_pVar['full_name'];
			if(!isset($filedefs_pVar[$file_pVar['location']])) {
				continue;
			}
			$fileName_pVar = $filedefs_pVar[$file_pVar['location']] . $fileName_pVar;
			file_put_contents($fileName_pVar, base64_decode($data_pVar[1]));
		}
	}
}


class restore_form_gClass extends form_gClass {

	protected function initForm_gFunc($multiedit_pVar = false, $initFormRef_pVar = true)
	{
		$this->addField_gFunc('', 'file', 'filelist', 'Súbor');
	}
	
	protected function getData()
	{
		$data_pVar = $this->getFormData_gFunc();
		if($data_pVar['error_code'] !== self::RESULT_OK_pVar) {
			return($data_pVar);
		}
		
		$data_pVar = $this->getFormData_gFunc();
		if($data_pVar['error_code'] !== self::RESULT_OK_pVar) {
			return($data_pVar);
		}

	    $fileInfo_pVar = main_gClass::getInputFile('file');
    	if(!is_array($fileInfo_pVar)) {
    		// subor nebol uploadovany vobec, resp. nebolo pole formularu.
    		return(false);
    	}
    	
    	if(!is_array($fileInfo_pVar['error'])) {
    		foreach ($fileInfo_pVar as $k_pVar=>$v_pVar) {
    			$fileInfo_pVar[$k_pVar] = array(0 => $v_pVar);
    		}
    	}

    	if(!isset($fileInfo_pVar['error'][0])) {
	    	return(false);
    	}
		$fileInfo_pVar['error']=array(0=>$fileInfo_pVar['error'][0]);

    	$file_error_pVar = $fileInfo_pVar['error'][0];
    	
    	if($file_error_pVar !== UPLOAD_ERR_OK && $file_error_pVar !== UPLOAD_ERR_NO_FILE) {
   			return(false);
    	}
    	
    	if($file_error_pVar === UPLOAD_ERR_OK) {
			// uploadnem subor do tmp adresara, a zapisem do tabulky importov
			$target_dir_pVar = main_gClass::getConfigVar_gFunc('documents_dir', 'main');
			$target_dir_pVar .= '.tmp/';
			$target_name_pVar = 'restore_' . sprintf('%1.22f', microtime(true)) . '.tmp';
			
	    	if(modules_gClass::isModuleRegistred_gFunc('upload')) {
	    		if(upload_gClass::moveUploadedFile_gFunc('file', 0, $target_dir_pVar, $target_name_pVar) === false) {
	    			return(false);
	       		}
	    	}
	    	else {
	    		if(move_uploaded_file($fileInfo_pVar['tmp_name'][0], $target_dir_pVar . $target_name_pVar) === false) {
	    			return(false);
	    		}
	    	}
    	}
    	else { // UPLOAD_ERR_NO_FILE
			$target_dir_pVar = main_gClass::getConfigVar_gFunc('documents_dir', 'main');
			$target_dir_pVar .= '.tmp/';
			$target_name_pVar = 'restore_' . date('Ymd') . '.dat';
			if(!file_exists($target_dir_pVar . $target_name_pVar)) {
				return(false);
			}
    	}
		
    	$restore_pVar = new restore_gClass($target_dir_pVar .$target_name_pVar);
    	$restore_pVar->restore_gFunc();
    	
    	unset($restore_pVar);
    	
		return($data_pVar);
	}
}

class restore_form extends restore_form_gClass {}

return(true);
