<?php
namespace Mixedtype\Forms;

use Illuminate\Support\Facades\Blade;
use Illuminate\Support\ServiceProvider;


class FormsServiceProvider extends ServiceProvider
{

    public function boot()
    {

        $this->loadRoutesFrom(__DIR__.'/../routes/web.php');
        $this->loadViewsFrom(__DIR__.'/../resources/views', 'mixedtype-forms');
        $this->loadMigrationsFrom(__DIR__.'/../database/migrations');

        Blade::component(\Mixedtype\Forms\Components\Form::class, 'form');
        FormFactory::registerFormFieldClass(\Mixedtype\Forms\Components\Field::class, 'field');
        FormFactory::registerFormFieldClass(\Mixedtype\Forms\Components\Input::class, 'input');
        FormFactory::registerFormFieldClass(\Mixedtype\Forms\Components\Hidden::class, 'hidden');
        FormFactory::registerFormFieldClass(\Mixedtype\Forms\Components\Text::class, 'text');
        FormFactory::registerFormFieldClass(\Mixedtype\Forms\Components\Password::class, 'password');
        FormFactory::registerFormFieldClass(\Mixedtype\Forms\Components\Tags::class, 'tags');
        FormFactory::registerFormFieldClass(\Mixedtype\Forms\Components\Combobox::class, 'combobox');
        FormFactory::registerFormFieldClass(\Mixedtype\Forms\Components\Userlist::class, 'userlist');
        FormFactory::registerFormFieldClass(\Mixedtype\Forms\Components\Textarea::class, 'textarea');
        FormFactory::registerFormFieldClass(\Mixedtype\Forms\Components\File::class, 'file');
        FormFactory::registerFormFieldClass(\Mixedtype\Forms\Components\Select::class, 'select');
        FormFactory::registerFormFieldClass(\Mixedtype\Forms\Components\Checklist::class, 'checklist');
        FormFactory::registerFormFieldClass(\Mixedtype\Forms\Components\Label::class, 'label');
        FormFactory::registerFormFieldClass(\Mixedtype\Forms\Components\Group::class, 'group');
        FormFactory::registerFormFieldClass(\Mixedtype\Forms\Components\Submit::class, 'submit');
        FormFactory::registerFormFieldClass(\Mixedtype\Forms\Components\SelectTree::class, 'select-tree');
        FormFactory::registerFormFieldClass(\Mixedtype\Forms\Components\Str::class, 'str');
        FormFactory::registerFormFieldClass(\Mixedtype\Forms\Components\Errors::class, 'errors');

        FormFactory::registerFormClass(FakeForm::class, 'fake-form');


        if ($this->app->runningInConsole()) {

        }
    }


    public function register()
    {


    }
}
