@props([
    'name',
    'value',
    'options'
])

<ul class="checklist">
    @foreach($options as $valueId => $label)
        <li class="tree-item tree-{{ $valueId }}"><label>
                <input
                    type="checkbox"
                    name="{{ $name }}[]"
                    value="{{ $valueId }}"
                    @if(is_array($value) && in_array($valueId, $value)) checked @endif
                /> {{ $label }}</label></li>
    @endforeach
</ul>

