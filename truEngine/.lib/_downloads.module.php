<?php
if(isset($_SERVER['SCRIPT_FILENAME']) && strtolower($_SERVER['SCRIPT_FILENAME']) == strtolower(str_replace("\\",'/',__FILE__))) { header("HTTP/1.0 404 Not Found");  return(false); }
if(isset($GLOBALS['file__downloads_pVar'])) return(true);
$GLOBALS['file__downloads_pVar']=true;

if(!modules_gClass::isModuleRegistred_gFunc('db'))
{
    return(false);
}

if(!modules_gClass::isModuleRegistred_gFunc('items'))
{
    return(false);
}

if(!modules_gClass::isModuleRegistred_gFunc('forms'))
{
    return(false);
}

class downloads_additem_gClass extends form_gClass
{
	function __construct($action_pVar = 'get')
	{
		parent::__construct($action_pVar);
	}

	protected function initForm_gFunc()
	{
		// musim ziskat data
		$data_pVar = array();
		if(isset($this->params) && count($this->params)) {
			$data_pVar = $this->params;
		}
		$this->form_init_pVar = true;
		items_gClass::initFormRef_gFunc('downloads', $this, $data_pVar);
		$this->addHiddenField_gFunc('group1', 'downloads', null, 'downloads');
		$this->addHiddenField_gFunc('group2', 'documents', null, 'documents');
	}


	protected function getData()
	{
		$data_pVar = $this->getFormData_gFunc();
		if($data_pVar['error_code'] !== self::RESULT_OK_pVar) {
			return($data_pVar);
		}

		// ulozim item
		items_gClass::editItemByForm_gFunc('downloads', $this);

		$data_pVar = $this->getFormData_gFunc();
		return($data_pVar);
	}
}

class downloads_additem extends downloads_additem_gClass
{

}

class downloads_list_gClass extends source_gClass
{
	protected function getData()
	{
		$filter_pVar = array();
		if(isset($this->params['url_filter'])) {
			$filter_pVar['url_filter'] = $this->params['url_filter'];
		}
		if(isset($this->params['filter'])) {
			$filter_pVar['filter'] = $this->params['filter'];
		}
		/*
		if(!count($filter_pVar)) {
			return(array());
		}*/

		$items_pVar = db_downloads_gClass::getFiles_gFunc($filter_pVar);

		return($items_pVar);
	}
}

class downloads_list extends downloads_list_gClass
{

}

class downloads_list_categories_gClass extends source_gClass
{
	protected function getData()
	{
		$categories_pVar = db_downloads_gClass::getCategories_gFunc();

		foreach ($categories_pVar as $k_pVar=>$v_pVar) {
			$categories_pVar[$k_pVar]['enum_field_value'] = '<a href="'.main_gClass::makeUrl_gFunc('/downloads/category?item_id='.$categories_pVar[$k_pVar]['enum_id']).'">' . str_replace('_', ' ', $categories_pVar[$k_pVar]['enum_field_value']) . '</a>';
			foreach ($v_pVar as $kk_pVar=>$vv_pVar) {
				if(substr($kk_pVar, 3) === 'url_name') {
					$categories_pVar[$k_pVar][$kk_pVar] = str_replace('-', ' - ', $categories_pVar[$k_pVar][$kk_pVar]);
				}

				if(substr($kk_pVar, 2, 1) === '_') { // lng
					if(!isset($categories_pVar[$k_pVar][substr($kk_pVar,3)])) {
						$categories_pVar[$k_pVar][substr($kk_pVar,3)] = '';
					}
					if(!empty($categories_pVar[$k_pVar][substr($kk_pVar,3)])) {
						$categories_pVar[$k_pVar][substr($kk_pVar,3)] .= '<br />';
					}
					$categories_pVar[$k_pVar][substr($kk_pVar,3)] .= '[' . substr($kk_pVar, 0, 2) . ']&nbsp;' . $categories_pVar[$k_pVar][$kk_pVar];
				}
			}
		}

		if(modules_gClass::isModuleRegistred_gFunc('tables')) {
			$table_pVar = new table_gClass();
			$table_pVar->setData_gFunc($categories_pVar);
			if(isset($this->params['columns'])) {
				$table_pVar->setColumnsFromString_gFunc($this->params['columns']);
			}

			return($table_pVar->getData());
		}

		return($categories_pVar);
	}
}

class downloads_additemvalue_gClass extends form_gClass
{
	protected $enum_field_id_pVar;

	protected function initForm_gFunc()
	{
		if(isset($this->params['item_id']) && $this->params['item_id']>0) {
			$isItemId_pVar = true;
		}
		else {
			$isItemId_pVar = false;
		}

		$info_pVar = db_items_gClass::getInfo_gFunc('downloads');

		$this->addFieldset_gFunc('main', '');
		$this->addField_gFunc('main', 'enum_field_value', 'varchar', 'Tag:', true);

		foreach ($info_pVar['languages'] as $language) {
			$this->addFieldset_gFunc($language, $language);
			$this->addField_gFunc($language, $language . '_enum_field_name_item', 'varchar', '['.$language.'] Názov:', true);
			$this->addField_gFunc($language, $language . '_enum_field_name_group', 'varchar', '['.$language.'] Názov (množné číslo):', true);
			$this->addField_gFunc($language, $language . '_url_name', 'varchar', '['.$language.'] Url:', true);
		}
		$this->addHiddenField_gFunc('enum_field_id', $this->enum_field_id_pVar, '/[0-9]+/');

		if($isItemId_pVar) {
			$this->addHiddenField_gFunc('item_id', $this->params['item_id'], '/[0-9]+/');
			$this->setVar_gFunc('submit_button_title', $this->params['submit_button_title_update']);
			// selectnem pouzivatela a inicializujem hodnoty
			$data_pVar = db_downloads_gClass::getItemDownloadsValue_gFunc($this->params['item_id']);
			if(is_array($data_pVar)) {
				$this->setDefaultValue_pVar('main', 'enum_field_value', $data_pVar['enum_field_value']);
				foreach ($info_pVar['languages'] as $language) {
					$this->setDefaultValue_pVar($language, $language . '_enum_field_name_item', $data_pVar[$language . '_enum_field_name_item']);
					$this->setDefaultValue_pVar($language, $language . '_enum_field_name_group', $data_pVar[$language . '_enum_field_name_group']);
					$this->setDefaultValue_pVar($language, $language . '_url_name', $data_pVar[$language . '_url_name']);
				}
			}
		}
		else {
			$this->addHiddenField_gFunc('item_id', 0, '/[0]+/');
			$this->setVar_gFunc('submit_button_title', $this->params['submit_button_title_add']);
		}

		$this->setVar_gFunc('submit_button_title_add', $this->params['submit_button_title_add']);
		$this->setVar_gFunc('submit_button_title_update', $this->params['submit_button_title_update']);
		return;
	}

	protected function getData()
	{
		$data_pVar = $this->getFormData_gFunc();
		if($data_pVar['error_code'] !== self::RESULT_OK_pVar) {
			return($data_pVar);
		}

		$info_pVar = db_items_gClass::getInfo_gFunc('downloads');

		// ulozim polozku
		$valueId_pVar = $this->getFieldValue_gFunc('item_id');
		$data_pVar = array();
		$data_pVar['enum_field_value'] = $this->getFieldValue_gFunc('enum_field_value');
		$data_pVar['enum_field_id'] = $this->getFieldValue_gFunc('enum_field_id');
		foreach ($info_pVar['languages'] as $language) {
			$data_pVar[$language . '_enum_field_name_item'] = $this->getFieldValue_gFunc($language . '_enum_field_name_item');
			$data_pVar[$language . '_enum_field_name_group'] = $this->getFieldValue_gFunc($language . '_enum_field_name_group');
			$data_pVar[$language . '_url_name'] = $this->getFieldValue_gFunc($language . '_url_name');

			$data_pVar[$language . '_url_name'] = str_replace(' ', '_', $data_pVar[$language . '_url_name']);
			$data_pVar[$language . '_url_name'] = string_gClass::removeDiacritic_gFunc($data_pVar[$language . '_url_name']);
		}

		$data_pVar['enum_field_value'] = str_replace(' ', '_', $data_pVar['enum_field_value']);
		$data_pVar['enum_field_value'] = string_gClass::removeDiacritic_gFunc($data_pVar['enum_field_value']);



		if($valueId_pVar) {
			// aktualizujem
			db_downloads_gClass::updateItemDownloadsValue_gFunc($valueId_pVar, $data_pVar);
		}
		else {
			// novy
			db_downloads_gClass::newItemDownloadsValue_gFunc($data_pVar);
		}

		$data_pVar = $this->getFormData_gFunc();
		return($data_pVar);
	}

}

class downloads_addcategory extends downloads_additemvalue_gClass
{
	protected $enum_field_id_pVar = 11;
}


class downloads_list_categories extends downloads_list_categories_gClass
{

}

class db_downloads_gClass extends db_gClass
{
	static public function getCategories_gFunc()
	{
		$sql_pVar = 'SELECT * FROM `%titems_downloads_values` WHERE `enum_field_id`=11';
		$data_pVar = self::getResultArray_gFunc($sql_pVar, __FILE__, __LINE__);
		return($data_pVar);
	}

	static public function getItemDownloadsValue_gFunc($id_pVar)
	{
		$sql_pVar = 'SELECT * FROM `%titems_downloads_values` WHERE `enum_id`=%d';
		$data_pVar = self::getResult_gFunc($sql_pVar, __FILE__, __LINE__, $id_pVar);
		return($data_pVar);
	}

	static public function updateItemDownloadsValue_gFunc($valueId_pVar, $data_pVar)
	{
		unset($data_pVar['enum_field_value']);

    	$sql_pVar = 'UPDATE `%titems_downloads_values` SET ';
    	$sql_params_pVar = array();

    	foreach ($data_pVar as $k_pVar=>$v_pVar) {
    		if(count($sql_params_pVar)) {
    			$sql_pVar .= ', ';
    		}
    		$sql_pVar .= ' `' . $k_pVar . '`=%s';
    		$sql_params_pVar[] = $v_pVar;
    	}

    	$sql_pVar .= ' WHERE `enum_id`=%d';
    	$sql_params_pVar[] = $valueId_pVar;

    	self::execute_gFunc($sql_pVar, __FILE__, __LINE__, $sql_params_pVar);

    	db_items_gClass::applyLanguagesToTables_gFunc('downloads');
		db_items_gClass::cacheDataToTree_gFunc('downloads', 'categories');
	}

	static public function newItemDownloadsValue_gFunc($data_pVar)
	{

    	$sql_pVar = 'INSERT INTO `%titems_downloads_values` SET ';
    	$sql_params_pVar = array();

    	foreach ($data_pVar as $k_pVar=>$v_pVar) {
    		if(count($sql_params_pVar)) {
    			$sql_pVar .= ', ';
    		}
    		$sql_pVar .= ' `' . $k_pVar . '`=%s';
    		$sql_params_pVar[] = $v_pVar;
    	}

    	self::execute_gFunc($sql_pVar, __FILE__, __LINE__, $sql_params_pVar);

    	db_items_gClass::applyLanguagesToTables_gFunc('downloads');
		db_items_gClass::cacheDataToTree_gFunc('downloads', 'categories');
	}

	static public function getFiles_gFunc($filter_pVar = array())
	{
		if(modules_gClass::isModuleRegistred_gFunc('items')) {
			$filter_pVar['_order_by'] = 'group3,group2,group1';
			$files_pVar = items_gClass::getItems_gFunc('downloads', $filter_pVar);
		}
		else {
			$files_pVar = array();
		}
		return($files_pVar);
	}



}


return(true);
