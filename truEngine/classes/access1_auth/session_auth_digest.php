<?php


class session_auth_digest_gClass extends session_gClass
{

    public function isLoggedOn_gFunc()
    {
        if($this->userID_pVar === 0) {
            return(false);
        }
        else {
            return(true);
        }
    }

    protected function authSession_gFunc()
    {
        $timeout_pVar = (int)main_gClass::getInputInteger_gFunc('auth_timeout_pVar', main_gClass::SRC_SESSION_pVar);
        if($timeout_pVar) {
            if($timeout_pVar < time()) {
                // timeouted
                // dokument sa mi zobrazi, ak po timeouted stlacim cancel
                $this->destroySession_gFunc();
                log_gClass::write_gFunc('AUTH_DIGEST_TIMEOUT');
                return(false);
            }
        }
        if(self::$loginPrompt_pVar === false) {
            // sem sa dostanem ak zacinam autentifikaciu
            // ak slacim cancel (bez zadania mena/hesla), tak sa mi zobrazi tento dokument.
            if(modules_gClass::isModuleRegistred_gFunc('https') && https_gClass::enabled_gFunc()) {
                https_gClass::check_gFunc(); // redirect
            }
            $this->clearSesion_gFunc();
            $realm_pVar = main_gClass::getConfigVar_gFunc('realm', 'access1_auth');
            header('HTTP/1.1 401 Unauthorized');
            header('WWW-Authenticate: Digest realm="' . $realm_pVar .
                '",qop="auth",nonce="' . uniqid() . '",opaque="' . md5($realm_pVar) . '"');
            log_gClass::write_gFunc('AUTH_DIGEST_LOGINPROMPT');
            return(false);
        }
        else {
            $realm_pVar = main_gClass::getConfigVar_gFunc('realm', 'access1_auth');
            $login_pVar = trim(main_gClass::getConfigVar_gFunc('login', 'access1_auth'));
            $password_pVar = trim(main_gClass::getConfigVar_gFunc('password', 'access1_auth'));
            $passOk_pVar = false;
            $loggedNow_pVar = false;

            $remoteAddr_pVar = main_gClass::getInputString_gFunc('auth_remote_addr_pVar', main_gClass::SRC_SESSION_pVar);
            $userAgent_pVar = main_gClass::getInputString_gFunc('auth_user_agent_pVar', main_gClass::SRC_SESSION_pVar);
            $userName_pVar = main_gClass::getInputString_gFunc('auth_user_name_pVar', main_gClass::SRC_SESSION_pVar);
            $authMethod_pVar = main_gClass::getInputString_gFunc('auth_method_pVar', main_gClass::SRC_SESSION_pVar);

            if(strlen($login_pVar) && strlen($password_pVar)) {
                $authDigestData_pVar = $this->http_digest_parse_gFunc(main_gClass::getServerVar_gFunc('PHP_AUTH_DIGEST', false));
                if(is_array($authDigestData_pVar)) {
                    // $authDigestData_pVar['username']

//                echo $authDigestData_pVar['uri'];
                    $A1_pVar = md5($authDigestData_pVar['username'] . ':' . $realm_pVar . ':' . $password_pVar);
                    $A2_pVar = md5(main_gClass::getServerVar_gFunc('REQUEST_METHOD') . ':' . $authDigestData_pVar['uri']);
                    $validResponse_pVar = md5($A1_pVar . ':' . $authDigestData_pVar['nonce'] . ':' . $authDigestData_pVar['nc'] . ':' . $authDigestData_pVar['cnonce'] . ':' . $authDigestData_pVar['qop'] . ':' . $A2_pVar);
                }

                if(is_array($authDigestData_pVar)
                    && $login_pVar === $authDigestData_pVar['username']
                    && $authDigestData_pVar['response'] == $validResponse_pVar) {
                    if(empty($remoteAddr_pVar)) {
                        $loggedNow_pVar = true;
                        $remoteAddr_pVar = main_gClass::getServerVar_gFunc('REMOTE_ADDR');
                        $userAgent_pVar = main_gClass::getServerVar_gFunc('HTTP_USER_AGENT');
                        $userName_pVar = $login_pVar;
                        $authMethod_pVar = 'digest';
                    }
                    if($remoteAddr_pVar === main_gClass::getServerVar_gFunc('REMOTE_ADDR')
                        && $userAgent_pVar === main_gClass::getServerVar_gFunc('HTTP_USER_AGENT')
                        && $userName_pVar === $authDigestData_pVar['username']
                        && $authMethod_pVar === 'digest') {
                        $passOk_pVar = true;
                        if($loggedNow_pVar) {
                            main_gClass::setPhpSessionVar_gFunc('auth_remote_addr_pVar', $remoteAddr_pVar, true, true);
                            main_gClass::setPhpSessionVar_gFunc('auth_user_agent_pVar', $userAgent_pVar, true, true);
                            main_gClass::setPhpSessionVar_gFunc('auth_user_name_pVar', $login_pVar, true, true);
                            main_gClass::setPhpSessionVar_gFunc('auth_method_pVar', 'digest', true, true);
                        }
                    }
                }
            }

            if($passOk_pVar === true) {
                main_gClass::setPhpSessionVar_gFunc('auth_timeout_pVar', time()+120);
                if(modules_gClass::isModuleRegistred_gFunc('https') && https_gClass::enabled_gFunc()) {
                    if(!main_gClass::getInputBoolean_gFunc('loggedon_pVar', main_gClass::SRC_COOKIE_pVar)) {
                        setcookie('loggedon_pVar', '1', null, main_gClass::getConfigVar_gFunc('web_dir_gVar', 'runtime_pVar'), null, null, true);
                    }
                }
                if($loggedNow_pVar) {
                    main_gClass::regenerateSessionId_gFunc();
                    log_gClass::write_gFunc('AUTH_DIGEST_LOGGEDON', $login_pVar);
                }
                $this->userID_pVar = $login_pVar;
                log_gClass::write_gFunc('AUTH_DIGEST_ACCESS', $login_pVar);
            }
            else {
                //// sem sa dostanem ak zadam zle heslo alebo login, alebo mi nesedi ip
                $realm_pVar = main_gClass::getConfigVar_gFunc('realm', 'access1_auth');
                header('HTTP/1.1 401 Unauthorized');
                header('WWW-Authenticate: Digest realm="' . $realm_pVar .
                    '",qop="auth",nonce="' . uniqid() . '",opaque="' . md5($realm_pVar) . '"');
                log_gClass::write_gFunc('AUTH_DIGEST_UNAUTHORIZED');
                return(false);
            }
        }
        return(true);
    }

    protected function authUser_gFunc()
    {
        // nie je potreba autentifikovat usera.. Vzdy mi staci autentifikovat session. (je to o tom istom)
    }

    protected function destroySession_gFunc()
    {
        main_gClass::setPhpSessionVar_gFunc('auth_timeout_pVar', 0);
        if(modules_gClass::isModuleRegistred_gFunc('https') && https_gClass::enabled_gFunc()) {
            setcookie('loggedon_pVar', '0', null, main_gClass::getConfigVar_gFunc('web_dir_gVar', 'runtime_pVar'), null, null, true);
            main_gClass::regenerateSessionId_gFunc();
        }
        $realm_pVar = main_gClass::getConfigVar_gFunc('realm', 'access1_auth');
        header('HTTP/1.1 401 Unauthorized');
        header('WWW-Authenticate: Digest realm="' . $realm_pVar .
            '",qop="auth",nonce="' . uniqid() . '",opaque="' . md5($realm_pVar) . '"');
    }

    private function http_digest_parse_gFunc($txt_pVar)
    {
        // protect against missing data
        $needed_parts_pVar = array('nonce'=>1, 'nc'=>1, 'cnonce'=>1, 'qop'=>1, 'username'=>1, 'uri'=>1, 'response'=>1);
        $data_pVar = array();

        $x_pVar = explode(',', $txt_pVar);
        foreach($x_pVar as $k_pVar=>$v_pVar) {
            $v_pVar = trim($v_pVar);
            $p_pVar = strpos($v_pVar, '=');
            if($p_pVar === false) {
                continue;
            }
            $left_pVar = substr($v_pVar, 0, $p_pVar);
            $right_pVar = substr($v_pVar, $p_pVar+1);
            $left_pVar = trim($left_pVar);
            if(!empty($right_pVar) && ($right_pVar[0] == '"' || $right_pVar[0] == '\'')) {
                $right_pVar = substr($right_pVar, 1, -1);
            }
            $right_pVar = trim($right_pVar);
            $data_pVar[$left_pVar] = $right_pVar;
            unset($needed_parts_pVar[$left_pVar]);
        }

        return $needed_parts_pVar ? false : $data_pVar;
    }

    private function clearSesion_gFunc()
    {
        main_gClass::setPhpSessionVar_gFunc('auth_remote_addr_pVar', 0);
        main_gClass::setPhpSessionVar_gFunc('auth_user_agent_pVar', 0);
        main_gClass::setPhpSessionVar_gFunc('auth_user_name_pVar', 0);
        main_gClass::setPhpSessionVar_gFunc('auth_method_pVar', 0);
    }

    protected function _userHasRights_gFunc($actionID_pVar, $objectID_pVar = false, $status_pVar = self::ACCESS_ALL_pVar, $logValue_pVar = false, $userID_pVar = false, $childResult_pVar = null)
    {
        if($userID_pVar === $this->userID_pVar && $this->isLoggedOn_gFunc()) {
            // ak je prihlaseny, ma administratorske prava
            return(parent::_userHasRights_gFunc($actionID_pVar, $objectID_pVar, $status_pVar, $logValue_pVar, $userID_pVar, true));
        }
        else {
            return(parent::_userHasRights_gFunc($actionID_pVar, $objectID_pVar, $status_pVar, $logValue_pVar, $userID_pVar, false));
        }
    }
}

