<?php

class test_user_instances_gClass extends source_gClass
{
    protected function getData()
    {
        $order_by_pVar = $this->params['_order_by'];
        $adapter_pVar = items_gClass::getAdapter_gFunc('test_instance');

        $data_pVar = $adapter_pVar->getItems_gFunc($this->params, array('_order_by', $order_by_pVar));
        return($data_pVar);
    }

    function getItemsFields_gFunc()
    {
        $adapter_pVar = items_gClass::getAdapter_gFunc('test_instance');
        return($adapter_pVar->getItemsFields_gFunc());
    }

    function getItemsFilterFields_gFunc()
    {
        $adapter_pVar = items_gClass::getAdapter_gFunc('test_instance');
        return($adapter_pVar->getItemsFilterFields_gFunc());
    }
}

class test_user_instances extends test_user_instances_gClass
{

}
