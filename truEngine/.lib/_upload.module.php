<?php
if(isset($_SERVER['SCRIPT_FILENAME']) && strtolower($_SERVER['SCRIPT_FILENAME']) == strtolower(str_replace("\\",'/',__FILE__))) { header("HTTP/1.0 404 Not Found");  return(false); }
if(isset($GLOBALS['file__upload_pVar'])) return(true);
$GLOBALS['file__upload_pVar']=true;

if(!modules_gClass::isModuleRegistred_gFunc('db'))
{
    return(false);
}

define('UPLOAD_ERR_CANT_MOVE', 100);

class upload_gClass
{
	static private $upload_data_pVar = false;
	/**
	 * Vrati pole $_FILES. Pole ale predtym upravi, ako keby boli vsetky subory multiple.
	 *
	 * @return array
	 */
	static private function _getFILES_gFunc()
	{
		if(self::$upload_data_pVar !== false) {
			return(self::$upload_data_pVar);
		}

		$upload_data_pVar = array();
		foreach ($_FILES as $k_pVar=>$v_pVar) {
			if(is_array($v_pVar['name'])) {
				$upload_data_pVar[$k_pVar] = $v_pVar;
			}
			else {
				$fileData_pVar = array();
				foreach ($v_pVar as $kk_pVar=>$vv_pVar) {
					$fileData_pVar[$kk_pVar] = array($vv_pVar);
				}
				$upload_data_pVar[$k_pVar] = $fileData_pVar;
			}
		}

		return(self::$upload_data_pVar);
	}

	/**
	 * Uploadne $_FILES[$uploadName_pVar] do tmp_uploads adresara,
	 * spravi o tom zaznam v db, a vrati id.
	 *
	 * @param unknown_type $uploadName_pVar
	 * @return unknown
	 */
	static public function handleUploadFile_gFunc($uploadName_pVar, $enbled_extensions_pVar=false, $max_size_pVar=false)
	{
		$session_id_pVar = session_id();

		$tmpUploadDir_pVar = self::getTmpUploadDir_gFunc();
		$FILES_pVar = self::_getFILES_gFunc();

		// skontrolujem ci upload existuje
		if(!isset($FILES_pVar[$uploadName_pVar])) {
			// upload neexistuje
			return(UPLOAD_ERR_NO_FILE);
		}

		// skontrolujem ci nema nastavenu chybu
		if($FILES_pVar['error'][0] != UPLOAD_ERR_OK) {
			return($FILES_pVar['error'][0]);
		}

		// skontrolujem extension
		if(is_array($enbled_extensions_pVar)) {
			$pathParts_pVar = pathinfo($FILES_pVar['name'][0]);
			if(array_search($pathParts_pVar['extension']) === false) {
				return(UPLOAD_ERR_EXTENSION);
			}
		}

		// skontrolujem max_size
		if($max_size_pVar !== false) {
			if($max_size_pVar < $FILES_pVar['size'][0]) {
				return(UPLOAD_ERR_FORM_SIZE);
			}
		}

		// move_uploaded_file
		$newName_pVar = tempnam($tmpUploadDir_pVar, 'upload_');
		if(!move_uploaded_file($FILES_pVar['tmp_name'][0], $newName_pVar)) {
			return(UPLOAD_ERR_CANT_MOVE);
		}

		// zapis do DB
		$uploadInfo_pVar = array();
		$uploadInfo_pVar['name'] = $FILES_pVar['name'][0];
		$uploadInfo_pVar['type'] = $FILES_pVar['type'][0];
		$uploadInfo_pVar['size'] = $FILES_pVar['size'][0];
		$uploadInfo_pVar['tmp_name'] = $newName_pVar;
		db_upload_gClass::insertTmpUpload_gFunc($session_id_pVar, $uploadName_pVar, $uploadInfo_pVar);

		return(UPLOAD_ERR_OK);
	}

	/**
	 * Vracia pole formulara
	 * array('fields'=> array(
	 *						array('name'=>name,
	 * 							  'type'=>type, // file, hidden
	 * 					 ),
	 * 		 'values' => array(
	 * 						array('name'=>name,
	 *				   	  		  'type'=>type,
	 * 				 		  	  'size'=>size,
	 * 						)
	 * 					)
	 * );
	 *
	 * x['values'][0]['name']
	 * // vo values mozu chybat niektore ciselne indexy. Su tam iba tie hodnoty, ktore boli uploadovane.
	 * // teda count(x['values']) dava skutocny pocet uz uploadovanych suborov do tohto $uploadName_pVar
	 *
	 *
	 *
	 * vo values su aktualne hodnoty suborov (ich nazvy), aby som vedel okontrolo
	 *
	 * @param unknown_type $uploadName_pVar
	 * @return unknown
	 */
	static public function getFormData_gFunc($uploadName_pVar)
	{
		$session_id_pVar = session_id();

		$ret_pVar = array();
/*
		UPLOADY MUSIM HANDLOVAT NA VSTUPE.. NAPR. KED ODLOGUJE POUZIVATELA, ABY SA UPLOAD SPRACOVAL.
		DO SESSION MUSIM PRED UPLOADOM ULOZIT UPLOADNAME, A BUDEM HANDLOVAT IBA TIE UPLOADY,
		ABY NEMOHOL DAKTO ZAHLTIT SERVER.
		PRE N NASOBNE UPLOADY V POLI MUSIM DEFINOVAT MAX POCET SUBOROV.


		*************ZATIAL SPRAVIM ASI IBA PROVIZORNE VSETKO.. TEDA METODU MOVEUPLOADEDFILE, KTORA UPLOADNE SUBOR PRIAMO...
*/
		return($ret_pVar);
	}

	/**
	 * Vyhlada subor $uploadName_pVar v databaze, skopiruje ho na cielove miesto,
	 * a vrati info z databazy ako pole.
	 * Vymaze zaznam z DB.
	 *
	 * @param unknown_type $uploadName_pVar
	 */
	static public function moveUploadedFile_gFunc($uploadName_pVar, $uploadFileIndex_pVar, $target_dir_pVar, $target_name_pVar = false)
	{

	}

	/**
	 * vrati info o uploade (info z databazy)
	 *
	 * @param unknown_type $uploadName_pVar
	 */
	static public function getUploadInfo_gFunc($uploadName_pVar)
	{

	}

	static public function getTmpUploadDir_gFunc()
	{
		return(main_gClass::getConfigVar_gFunc('documents_dir', 'main') . '.tmp/.uploads/');
	}

}

class db_upload_gClass extends db_gClass
{
	/**
	 * vlozi zaznam do tabulky uploadov
	 *
	 * @param unknown_type $session_id_pVar
	 * @param unknown_type $uploadName_pVar
	 * @param unknown_type $uploadInfo_pVar
	 */
	static public function insertTmpUpload_gFunc($session_id_pVar, $uploadName_pVar, $uploadInfo_pVar)
	{
		self::deleteTmpUpload_gFunc($session_id_pVar, $uploadName_pVar);

		$sql_pVar = 'INSERT INTO `%ttmp_uploads` (`sesion_str_id`, `upload_time`, '
					.'`upload_name`, `u_name`, `u_type`, `u_size`, `u_tmp_name`) VALUES ('
					.'%s, now(), %s, %s, %s, %d, %s)';
		$data_pVar = array($session_id_pVar, $uploadName_pVar, $uploadInfo_pVar['name'],
							$uploadInfo_pVar['type'], $uploadInfo_pVar['size'], $uploadInfo_pVar['tmp_name']);
		self::insert_gFunc($sql_pVar, __FILE__, __LINE__, $data_pVar);
	}

	/**
	 * vrati tabulku tmp uploadov pre usera, indexovanu podla upload_name
	 *
	 * @param unknown_type $session_id_pVar
	 * @return unknown
	 */
	static public function getTmpUploadsForSession_gFunc($session_id_pVar)
	{
		$cacheName_pVar = 'uploads_tmp_'.$session_id_pVar;
		$data_pVar = self::getCachedResult_gFunc($cacheName_pVar);
		if($data_pVar !== false) {
			return($data_pVar);
		}
		$sql_pVar = 'SELECT * FROM `%ttmp_uploads` WHERE `session_str_id`=%s';
		$data_pVar = self::getResultArray_gFunc($sql_pVar, __FILE__, __LINE__, array($session_id_pVar), 'upload_name');

		self::cacheResult_gFunc($cacheName_pVar, $data_pVar);
		return($data_pVar);
	}

	/**
	 * Zmaze subory z databazy aj z filesystemu
	 *
	 * @param unknown_type $session_id_pVar
	 */
	static public function deleteTmpUploadsForSession_gFunc($session_id_pVar)
	{
		$data_pVar = self::getTmpUploadsForSession_gFunc($session_id_pVar);
		$tmpUploadsDir_pVar = upload_gClass::getTmpUploadDir_gFunc();
		foreach ($data_pVar as $v_pVar) {
			$fileName_pVar = $tmpUploadsDir_pVar . $data_pVar['u_tmp_name'];
			if(file_exists($fileName_pVar)) {
				unlink($fileName_pVar);
			}
		}
		$sql_pVar = 'DELETE FROM `%ttmp_uploads` WHERE `session_str_id`=%s';
		self::execute_gFunc($sql_pVar, __FILE__, __LINE__, array($session_id_pVar));
	}

	/**
	 * Zmaze jeden upload uploadName
	 *
	 * @param unknown_type $upload_id_pVar
	 * @param unknown_type $tmp_name_pVar
	 */
	static public function deleteTmpUpload_gFunc($session_id_pVar, $uploadName_pVar)
	{
		$data_pVar = self::getTmpUploadsForSession_gFunc($session_id_pVar);
		foreach ($data_pVar as $k_pVar=>$v_pVar) {
			if($v_pVar['upload_name'] !== $uploadName_pVar) {
				continue;
			}
			$tmpUploadsDir_pVar = upload_gClass::getTmpUploadDir_gFunc();
			$fileName_pVar = $tmpUploadsDir_pVar . $tmp_name_pVar;
			if(file_exists($fileName_pVar)) {
				unlink($fileName_pVar);
			}

			$sql_pVar = 'DELETE FROM `%ttmp_uploads` WHERE `upload_id`=%d';
			self::execute_gFunc($sql_pVar, __FILE__, __LINE__, array($v_pVar['upload_id']));
		}
	}
}


return(true);
