@charset "iso-8859-1";

/*******************************************************************************
*  marginalNotes.css : 2004-06-28 : Ruthsarian Layouts
* ------------------------------------------------------------------------------
*  The purpose of this stylesheet is to add the ability to create marginal notes
*  within the page. These are elements which are placed inside #contentColumn 
*  and then floated left or right, so as to appear inside the margin.
*
*  The problem is, these notes can't be created inside the .inside sub-block
*  of #contentColumn. So to fix this, we will have to remove all vertical
*  padding on .inside classes which are inside the #contentColumn block.
*  The missing padding will be conpensated for by applying vertical padding
*  to the #contentColumn block.
*
*  Keep in mind that these notes will also have .inside classes which will
*  also be found inside #contentColumn so we have to set those values back.
*
*  FYI: side column widths play a big role. those values are set in base.css.
*       once i decide to really accept this approach to marginal notes i will
*       integrate some of this CSS into base.css so you only have one place to
*       make changes to change the width of the side columns.
*
*******************************************************************************/

#contentColumn
{
	padding: 0.5em 0;
}
#contentColumn .inside
{
	padding: 0 1.5em;
}
* html #contentColumn .inside
{
	padding-top: 1px;
	padding-bottom: 1px;
}

/*-*-*-*-*-*-*-*-*-*-*-*-*-*/

#contentColumn div.marginNotesRight, #contentColumn div.marginNotesLeft
{
	position: relative;
	overflow: visible;	/* fix for IE italics bug */
	z-index: 10;
	padding: 1px 0;
	border: solid 1px #000;
	border-width: 1px 0;
}
#contentColumn div.marginNotesLeft
{
	float: left;
	clear: left;
	width: 199px;
	margin: -1px 2px 0 -200px;	/* the -1px top margin is to hide the 
					   double border when two notes are 
					   stacked on top of each other */
	background-color: #ffe;
	border-color: #bb8;
}
#contentColumn div.marginNotesRight
{
	float: right;
	clear: right;
	width: 199px;
	margin: -1px -200px 0 2px;	/* ditto */
	background-color: #885;
	border-color: #dd9;
	color: #fff;
}
/* :root is a pseudo-selector that mozilla understands. this keeps the 
	mozilla-specific hack within mozilla but won't validate */
:root #contentColumn div.marginNotesLeft,
:root #contentColumn div.marginNotesRight
{
	margin-top: -2px;	/* an attempt to compensate for Mozilla's 
				   off-by-one bug */
}
#contentColumn div.marginNotesLeft .inside, 
#contentColumn div.marginNotesRight .inside
{
	padding: 0;
	margin: 0.5em 1em;
}

/*-*-*-*-*-*-*-*-*-*-*-*-*-*/

/* \*/
	* html #contentColumn div.inside
	{
		float: left;
		width: auto;
		margin: 0;
		height: 1%;
	}
	* html #contentColumn div.marginNotesLeft
	{
		margin: -1px 0 0 -100px;	/* margin-left * 2 it seems for IE6 */
	}
	* html #contentColumn div.marginNotesRight
	{
		margin: -1px -100px 0 0;	/* margin-left * 2 it seems for IE6 */
	}
	* html #contentColumn div.marginNotesLeft div.inside,
	* html #contentColumn div.marginNotesRight div.inside
	{
		float: none;
		width: auto;
		height: auto;
	}
/* */

/******************************************************************************/
