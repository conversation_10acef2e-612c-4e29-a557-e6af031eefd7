<?php


class itemsAdapter_test_rozpis_prace extends itemsAdapter_gClass
{
    function getItemsFields_gFunc()
    {
        $fields_pVar = array(
            array('tag' => 'user_id',
                'type' => 'user_id',
                'sk_name' => 'Používateľ'),
            array('tag' => 'join_date',
                'type' => 'datetime',
                'sk_name' => 'Prihlásil sa'),
            array('tag' => 'prepare_date',
                'type' => 'datetime',
                'sk_name' => 'Zadanie'),
            array('tag' => 'koordinator_id',
                'type' => 'user_id',
                'sk_name' => 'koordinator_id'),
            array('tag' => 'predmet',
                'type' => 'xvarchar',
                'len' => 255,
                'sk_name' => 'Predmet'),
            array('tag' => 'rocnik',
                'type' => 'int',
                'sk_name' => 'Ročník'),
            array('tag' => 'kniha',
                'type' => 'xtext',
                'sk_name' => 'Kniha'),
            array('tag' => 'kniha_cast',
                'type' => 'xvarchar',
                'len' => 255,
                'sk_name' => 'Strany'),
            array('tag' => 'poznamka',
                'type' => 'xtext',
                'sk_name' => 'Poznámka')
        );
        return ($fields_pVar);
    }

    function getEnumFieldsValues_gFunc($where_str_pVar = '', $where_data_pVar = array(), $columns_pVar = '*', $index_by_pVar = false)
    {
        return (array());
    }

    function getItems_checkRights_gFunc($filter_pVar)
    {
        return (session_gClass::userHasRightsAccess_gFunc(s_test_rozpis_prace));
    }

    protected function _pagerData_gFunc($where_str_pVar, $where_data_pVar, $order_by_pVar, $offset_pVar, $pageLen_pVar)
    {
        $sql_pVar = '
						SELECT
								`D`.*,
								CONCAT_WS(\' \', `u1`.`first_name`, `u1`.`last_name`) as `user`,
								CONCAT_WS(\' \', `u2`.`titul_pred`, `u2`.`first_name`, `u2`.`last_name`, `u2`.`titul_za`) as `koordinator`,
								`u1`.`last_name` as `user_last_name`,
								`u2`.`last_name` as `koordinator_last_name`

							FROM `%ttest_work` as `D`
							LEFT JOIN `%titems_users__data` as `u1` ON `D`.`user_id` = `u1`.`item_id`
							LEFT JOIN `%titems_users__data` as `u2` ON `D`.`koordinator_id` = `u2`.`item_id`

		';
        $where_str_pVar .= ' AND `D`.`status` = \'active\'';

        $sql_pVar .= $where_str_pVar;

        $order_by_pVar = str_replace('`D`.`user_last_name`', '`u1`.`last_name`', $order_by_pVar);
        $order_by_pVar = str_replace('`D`.`koordinator_last_name`', '`u2`.`last_name`', $order_by_pVar);

        $sql_pVar .= $order_by_pVar;

        $sql_pVar .= ' LIMIT %d, %d';
        $where_data_pVar[] = $offset_pVar;
        $where_data_pVar[] = $pageLen_pVar;

        $data_pVar = db_public_gClass::getResultArray_gFunc($sql_pVar, __FILE__, __LINE__, $where_data_pVar);

        $itemIds_pVar = array();
        foreach ($data_pVar as $v_pVar) {
            if ($v_pVar['user_id']) {
                $itemIds_pVar[] = $v_pVar['user_id'];
            }
            if ($v_pVar['koordinator_id']) {
                $itemIds_pVar[] = $v_pVar['koordinator_id'];
            }
        }
        $filesTmp_pVar = main_gClass::getFiles_gFunc('items_users_foto', $itemIds_pVar, false);

        if (count($filesTmp_pVar)) {
            foreach ($data_pVar as $k_pVar => $v_pVar) {
                if (isset($filesTmp_pVar['items_users_foto_' . $v_pVar['user_id']])) {
                    $data_pVar[$k_pVar]['user_foto'] = $filesTmp_pVar['items_users_foto_' . $v_pVar['user_id']];
                    unset($itemIds_pVar[array_search($v_pVar['user_id'], $itemIds_pVar)]);
                }
                if (isset($filesTmp_pVar['items_users_foto_' . $v_pVar['koordinator_id']])) {
                    $data_pVar[$k_pVar]['koordinator_foto'] = $filesTmp_pVar['items_users_foto_' . $v_pVar['koordinator_id']];
                    unset($itemIds_pVar[array_search($v_pVar['koordinator_id'], $itemIds_pVar)]);
                }
            }
        }
        if (count($itemIds_pVar)) {
            $filesTmp_pVar = main_gClass::getFiles_gFunc('items_users_foto_rec', $itemIds_pVar, false);
            if (count($filesTmp_pVar)) {
                foreach ($data_pVar as $k_pVar => $v_pVar) {
                    if (isset($filesTmp_pVar['items_users_foto_rec_' . $v_pVar['user_id']])) {
                        $data_pVar[$k_pVar]['user_foto'] = $filesTmp_pVar['items_users_foto_rec_' . $v_pVar['user_id']];
                        unset($itemIds_pVar[array_search($v_pVar['user_id'], $itemIds_pVar)]);
                    }
                    if (isset($filesTmp_pVar['items_users_foto_rec_' . $v_pVar['koordinator_id']])) {
                        $data_pVar[$k_pVar]['koordinator_foto'] = $filesTmp_pVar['items_users_foto_rec_' . $v_pVar['koordinator_id']];
                        unset($itemIds_pVar[array_search($v_pVar['koordinator_id'], $itemIds_pVar)]);
                    }
                }
            }
        }

        $web_dir_pVar = main_gClass::getConfigVar_gFunc('web_dir', 'runtime');
        if (substr($web_dir_pVar, -1) == '/') {
            $web_dir_pVar = substr($web_dir_pVar, 0, -1);
        }

        foreach ($data_pVar as $k_pVar => $v_pVar) {
            if ($v_pVar['user_id'] && session_gClass::userHasRightsInfo_gFunc(s_users_edit_user)) {
                $data_pVar[$k_pVar]['user'] = '<a href="' . main_gClass::makeUrl_gFunc('/pouzivatelia/editovat-pouzivatela?item_id=' . $v_pVar['user_id']) . '">' . $data_pVar[$k_pVar]['user'] . '</a>';
            }

            if (isset($v_pVar['user_foto'])) {
                $data_pVar[$k_pVar]['user_foto'] = '<img src="' . $web_dir_pVar . $v_pVar['user_foto']['th1_']['src'] . '" />';
            } else {
                $data_pVar[$k_pVar]['user_foto'] = '<img src="/sk/images/icons/nofoto.jpg" />';
            }
            if (isset($v_pVar['koordinator_foto'])) {
                $data_pVar[$k_pVar]['koordinator_foto'] = '<img src="' . $web_dir_pVar . $v_pVar['koordinator_foto']['th1_']['src'] . '" />';
            } else {
                $data_pVar[$k_pVar]['koordinator_foto'] = '<img src="/sk/images/icons/nofoto.jpg" />';
            }

            if (!empty($data_pVar[$k_pVar]['user'])) {
                $data_pVar[$k_pVar]['user'] .= '<br />' . $data_pVar[$k_pVar]['user_foto'];
            }
            if (!empty($data_pVar[$k_pVar]['koordinator'])) {
                $data_pVar[$k_pVar]['koordinator'] .= '<br />' . $data_pVar[$k_pVar]['koordinator_foto'];
            }
            if (!empty($data_pVar[$k_pVar]['join_date'])) {
                $data_pVar[$k_pVar]['join_date'] = timeFormat_gClass::time_gFunc(strtotime($data_pVar[$k_pVar]['join_date']));
            }


        }

        return ($data_pVar);
    }

    protected function _nonPagerData_gFunc($where_str_pVar, $where_data_pVar, $order_by_pVar)
    {
        return (array());
    }

    protected function _getCountData_gFunc($where_str_pVar, $where_data_pVar)
    {
        $sql_pVar = '
						SELECT
								count(`item_id`)
							FROM `%ttest_work` as `D`
		';
        $where_str_pVar .= ' AND `D`.`status` = \'active\'';

        $sql_pVar .= $where_str_pVar;
        $data_pVar = db_public_gClass::getField_gFunc($sql_pVar, __FILE__, __LINE__, $where_data_pVar);


        return ($data_pVar);
    }

    protected function _getOrderByFields_gFunc()
    {
        return (array('user_last_name' => 'Meno', 'koordinator_last_name' => 'Koordinátor', 'rocnik' => 'Ročník', 'predmet' => 'Predmet', 'join_date' => 'Prihlásil sa', 'kniha' => 'Kniha', 'kniha_cast' => 'Strany', 'poznamka' => 'Poznámka'));
    }

    function getItemsFilterFields_gFunc()
    {
        $fields_pVar = $this->getItemsFields_gFunc();
        return ($fields_pVar);
    }

}

