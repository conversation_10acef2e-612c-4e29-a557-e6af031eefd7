<?php
if(isset($_SERVER['SCRIPT_FILENAME']) && strtolower($_SERVER['SCRIPT_FILENAME']) == strtolower(str_replace("\\",'/',__FILE__))) { header("HTTP/1.0 404 Not Found");  return(false); }
if(isset($GLOBALS['file__cms_edit_pVar'])) return(true);
$GLOBALS['file__cms_edit_pVar']=true;

class cms_edit_gClass
{
	
    public static function te_edit_gFunc($rule_pVar, $asAttribute_pVar = false)
    {
    	$target_pVar = $rule_pVar->getAttributeValue_gFunc('te:edit');
    	
    	$openCode_pVar = PHP_OPEN . 'if(session_gClass::isDocEdit_gFunc()) {' . PHP_CLOSE;
    	$openCode_pVar .= '<div class="doc_edit_container"><div ondblclick="teEditBlockStart(this, \'' . $target_pVar . '\');" id="' . uniqid('teedit_') . '" onmouseover="teEditBlockMouseOver(this);" onmouseout="teEditBlockMouseOut(this);" class="doc_edit_mouseout">';
    	$openCode_pVar .= PHP_OPEN . '}' . PHP_CLOSE;
    	
    	$closeCode_pVar = PHP_OPEN . 'if(session_gClass::isDocEdit_gFunc()) {' . PHP_CLOSE;
    	$closeCode_pVar .= '</div></div>';
    	$closeCode_pVar .= PHP_OPEN . '}' . PHP_CLOSE;
    	
        if($asAttribute_pVar) {
        	$rule_pVar->elementDataPrefix_pVar = $openCode_pVar . $rule_pVar->elementDataPrefix_pVar;
	        $rule_pVar->elementDataSuffix_pVar = $rule_pVar->elementDataSuffix_pVar . $closeCode_pVar;
	        $rule_pVar->attributeUnset_gFunc('te:edit');
        }
        else {
        	$rule_pVar->elementPrefix_pVar = $openCode_pVar . $rule_pVar->elementPrefix_pVar;
	        $rule_pVar->elementSuffix_pVar = $rule_pVar->elementSuffix_pVar . $closeCode_pVar;
        	$rule_pVar->elementDisplayType_pVar = 'inside';
        }
    }
	
	static function isDocEdit_gFunc()
	{
		if(session_gClass::userHasRightsInfo_gFunc('s_document_edit')) {
			if(main_gClass::getSessionData_gFunc('doc_edit_mode')) {
				return(true);
			}
		}
		return(false);
	}
	
    static public function cacheStrings_gFunc($docName_gFunc, $strings_pVar)
    {
       foreach($strings_pVar as $string_pVar) {
       		// updatnem databazu
       		if(is_array($string_pVar)) {
       			$str_pVar = $string_pVar['str_pVar'];
       		}
       		else {
       			$str_pVar = $string_pVar;
       		}
       		if((modules_gClass::isModuleRegistred_gFunc('db'))) {
       			$sql_pVar = 'REPLACE INTO `%tstrings_cache` (`stringName`,`docName`) VALUES (%s, %s)';
       			db_public_gClass::execute_gFunc($sql_pVar, __FILE__, __LINE__, array($str_pVar, $docName_gFunc));
       		}
       }
    }
}

class doc_edit_gClass extends source_gClass
{
	protected function getData()
	{
		if(!session_gClass::userHasRightsAccess_gFunc(s_document_edit)) {
			return(array('result'=>0));
		}
		
		if(!modules_gClass::isModuleRegistred_gFunc('db')) {
		    return(array('result'=>0));
		}
		
		
		if(!isset($this->params['target']) || empty($this->params['target'])) {
			return(array('result'=>0));
		}
		
		$sql_pVar = 'SELECT * FROM `%tstrings` WHERE `' . main_gClass::getLanguage_gFunc() . '_name` = %s';
		$stringData_pVar = db_public_gClass::getResult_gFunc($sql_pVar, __FILE__, __LINE__, array($this->params['target']));
		
		if(!is_array($stringData_pVar)) {
			$sql_pVar = 'INSERT INTO `%tstrings` (`sk_name`, `en_name`, `' . main_gClass::getLanguage_gFunc() . '_text`) VALUES (%s, %s, %s)';
			db_public_gClass::insert_gFunc($sql_pVar, __FILE__, __LINE__, array($this->params['target'], $this->params['target'], $this->params['target']));
			$sql_pVar = 'SELECT * FROM `%tstrings` WHERE `' . main_gClass::getLanguage_gFunc() . '_name` = %s';
			$stringData_pVar = db_public_gClass::getResult_gFunc($sql_pVar, __FILE__, __LINE__, array($this->params['target']));
		}

		$data_pVar = array(main_gClass::getLanguage_gFunc() . '_text' => htmlspecialchars_decode($this->params['data']));
		$format_pVar = '%s';
		
		
		if($this->params['data'] !== $stringData_pVar[main_gClass::getLanguage_gFunc() . '_text']) {
			$data_pVar[main_gClass::getLanguage_gFunc() . '_last_update'] = 'now()';
			$format_pVar .= ', %r';
		}

		db_public_gClass::updateData_gFunc('%tstrings', $format_pVar, $data_pVar, '`id` = %d', array($stringData_pVar['id']), __FILE__, __LINE__);
		
		// zmazem cache subory aj db
		$sql_pVar = 'SELECT * FROM `%tstrings_cache` WHERE `stringName` = %s';
		$cache_pVar = db_public_gClass::getResultArray_gFunc($sql_pVar, __FILE__, __LINE__, array($this->params['target']));
		if(is_array($cache_pVar) && modules_gClass::isModuleRegistred_gFunc('cache')) {
			foreach($cache_pVar as $tmp_pVar) {
				$fileName_pVar = documentCache_gClass::getCacheFileName_gFunc($tmp_pVar['docName']);
				$fileName_pVar = fileSafe_gClass::unsafePath_gFunc($fileName_pVar);
				if(file_exists($fileName_pVar)) {
					unlink($fileName_pVar);
				}
			}
		}
		$sql_pVar = 'DELETE FROM `%tstrings_cache` WHERE `stringName` = %s';
		db_public_gClass::execute_gFunc($sql_pVar, __FILE__, __LINE__, array($this->params['target']));
		
		return(array('result'=>1));
	}	
}

class doc_edit extends doc_edit_gClass { }

return(true);