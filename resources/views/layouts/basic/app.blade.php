<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    @php
        $layout = \Mixedtype\Layouts\LayoutManager::getTheme();
    @endphp

    @include('includes.html-head', ['layout' => $layout])
</head>
<body><a name="top"></a>

<div id="pageWrapper">
    <hr class="hide" />
    <div id="masthead" class="inside" style="border:none;">
        @if(main_gClass::getConfigVar_gFunc('test', 'main'))
            <div style="background-color:#ffaaaa">
                <h1><a href="/">OPUS SAPIENTIÆ - TESTOVACIA VERZIA</a></h1>
            </div>
        @else
            <h1><a href="/">OPUS SAPIENTIÆ</a></h1>
        @endif
        @php echo \App\Legacy\LegacyApp::includeLegacyTemplate('/sk/components/layout-languages') @endphp
    </div>
    <hr class="hide" />
    <div class="hnav">
        @php echo \App\Legacy\LegacyApp::includeLegacyTemplate('/sk/components/layout-top-menu', [ 'layout' => $layout ]) @endphp
    </div>


    <div id="outerColumnContainer">
        <div id="innerColumnContainer">
            <hr class="hide" />
            <div id="leftColumn">
                <div class="inside">
                </div>
            </div>
            <hr class="hide" />
            <div id="rightColumn">
                <div class="inside">
                    <a href="/chyba">Help</a>

                    <div class="vnav">
                        @php echo \App\Legacy\LegacyApp::includeLegacyTemplate("/sk/components/layout-side-menu") @endphp
                    </div>
                </div>
            </div>
            <div id="contentColumn">

                <hr class="hide" />
                <a name="skipToContent" id="skipToContent"></a>
                <div class="inside">
                    @php echo \App\Legacy\LegacyApp::includeLegacyTemplate("/sk/components/check-isic") @endphp

                    {{ $slot }}
                </div>
            </div>
            <div class="clear mozclear"></div>
        </div>
    </div>

    <div class="hide" id="nsFooterClear"><!-- for NS4's sake --></div>
    <hr class="hide" />

    <div id="footer" class="inside">
        <div id="disclaimer">
            @php echo \App\Legacy\LegacyApp::includeLegacyTemplate("/sk/components/layout-disclaimer-sk") @endphp <br />
            @php echo \App\Legacy\LegacyApp::includeLegacyTemplate("/sk/components/layout-disclaimer-en") @endphp
        </div>

        @php
            $version = main_gClass::getVersion_gFunc();
            $version_date = date('j.n.Y', strtotime($version[0]));
        @endphp

        <p style="margin:0;">
            <span style="color:#bb9;">Verzia {{ $version[1] }} ({{ $version_date}})</span>
        </p>

    </div>
    <hr class="hide" />
</div>


@php echo \App\Legacy\LegacyApp::includeLegacyTemplate("/sk/components/layout-layout-menu", ['layout' => $layout]) @endphp
</body>
</html>
