<?xml version="1.0" encoding="utf-8" ?>
<truEngine-document xmlns:te="http://www.truengine.sk" te:access="s_system_superadmin">
<te:content-set name="title" value="@L{Obnovenie dát zo zálohy}" />

<te:source name="restore_form" varname="form">
		<te:source-param name="submit_button_title">@L{Obnoviť}</te:source-param>
</te:source>

<te:if test="@{form:error_code} != 'ok'">
	<div class="msg_warning">
		<ul>
			<li>@L{Obnovenim dát zo zálohy budú <strong>nenávratne prepísané</strong> vš<PERSON>ky bloky dát, ktoré záloha obsahuje.}</li>
			<li>Aby obnovenie zálohy prebehlo korektne, <strong>zablokujte prístup do databázy</strong> pre&amp;nbsp;ostatn<PERSON>ch používateľov (<a href="/system/technicka-odstavka">technická odstávka</a>). V opačnom prípade sa nedá zaručiť korektné obnovenie dát, a to ani v prípade, že počas obnovenia neboli vygenerované židne chybové hlásenia!</li>
		</ul>
	</div>
</te:if>

<te:include name="/sk/components/form" form="@@{form}" />

<te:if test="@{form:error_code} == 'ok'">
	OK
</te:if>

</truEngine-document>