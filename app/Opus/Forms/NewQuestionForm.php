<?php

namespace App\Opus\Forms;


use App\Items\Question;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use Mixedtype\Forms\Components\Userlist;
use Mixedtype\Forms\Form;
use Mixedtype\Forms\FormFactory;
use Mixedtype\Forms\Interface\FormInterface;

class NewQuestionForm extends BaseOpusForm implements FormInterface
{
    use NewQuestionFormValidate;

    private array $modules;
    private array $programs;
    private array $predmets;
    private array $problems;
    private array $subcategories;

    private array $parents;


    function __construct()
    {
        $this->modules = $this->getOpusValues('test_questions','modul', true, false);
        $this->programs = $this->getOpusValues('test_questions','program', true, false);
        $this->predmets = $this->getOpusValues('test_questions','predmet', true, false);
        $this->problems = $this->getOpusValues('test_questions','kategoria', true, false);
        $this->subcategories = $this->getOpusValues('test_questions','podkategoria', true, false);

        $this->getParentsForZaradenie();

        parent::__construct();
    }

    function getStateForNew()
    {
        return 'active';
    }

    function getSubmitMessageForEdit():string
    {
        return 'Otázka bola upravená.';
    }

    function getSubmitMessageForNew():string
    {
        return 'Otázka bola vytvorená.';
    }


    function getDefaultData():array
    {
        // http://kega.localhost/sk/otazky/editovat-otazku/?item_id=3244 // obrazok je ale nezobrazi sa???


        // http://kega.localhost/sk/otazky/editovat-otazku/?item_id=46 // s obrazkom
        // http://kega.localhost/sk/otazky/editovat-otazku/?item_id=154 // jeden keyword
        // http://kega.localhost/sk/otazky/editovat-otazku/?item_id=163
        // http://kega.localhost/sk/otazky/editovat-otazku/?item_id=1066
        // http://kega.localhost/sk/otazky/editovat-otazku/?item_id=3839 // zaradenie
        // http://kega.localhost/sk/otazky/editovat-otazku/?item_id=3035 // vysvetlenie odpovede + media v odpovedi + keywords !!! :)
        // http://kega.localhost/sk/otazky/editovat-otazku/?item_id=4064 // literatura-strana + keywords + dolezitost4
        // http://kega.localhost/sk/otazky/editovat-otazku/?item_id=2388 // --- existujuce media na zadani

        $item_id = request()->input('item_id', '');
        $data = [];


        if(!empty($item_id) && is_numeric($item_id)) {
//            $question = DB::table('kega_items_test_questions__data')
//                ->where('item_id', $item_id)
//                ->first();
            $legacyQuestion = \items_gClass::getItem_gFunc('test_questions', $item_id);
            if(!$legacyQuestion) {
                throw new \Exception('Question not found.');
            }

            $data['item_id'] = $item_id;
            $data['sk_otazka'] = $legacyQuestion['sk_otazka'];
            $data['en_otazka'] = $legacyQuestion['en_otazka'];
            if(is_array($legacyQuestion['sk_otazka_media']) && isset($legacyQuestion['sk_otazka_media'][0])) {
                $path = $legacyQuestion['sk_otazka_media'][0]['file'];
                $path = substr($path, strpos($path, '/.files'));
                $this->setExistingFile('sk_otazka_media', $path, $legacyQuestion['sk_otazka_media'][0]['src'], 'truengine-legacy-data');
            }
            if(is_array($legacyQuestion['en_otazka_media']) && isset($legacyQuestion['en_otazka_media'][0])) {
                $path = $legacyQuestion['en_otazka_media'][0]['file'];
                $path = substr($path, strpos($path, '/.files'));
                $this->setExistingFile('en_otazka_media', $path, $legacyQuestion['en_otazka_media'][0]['src'], 'truengine-legacy-data');
            }

            $data['sk_vysvetlenie'] = $legacyQuestion['sk_vysvetlenie'];
            $data['en_vysvetlenie'] = $legacyQuestion['en_vysvetlenie'];
            $data['literatura'] = $legacyQuestion['literatura'];
            $data['literatura_strana'] = $legacyQuestion['literatura_strana'];

            if(!empty($legacyQuestion['navrhovatel']) && str_starts_with($legacyQuestion['navrhovatel'], '@user=')) {
                $navrhovatel = DB::table('kega_items_users__data')
                    ->where('login', substr($legacyQuestion['navrhovatel'], 6))
                    ->first();
                if($navrhovatel) {
                    $data['navrhovatel'] = $navrhovatel->item_id;
                }
            }
            else {
                $data['navrhovatel'] = $legacyQuestion['navrhovatel'];
            }

            $data['sk_keywords'] = $legacyQuestion['sk_keywords'];
            $data['en_keywords'] = $legacyQuestion['en_keywords'];
            $data['dolezitost'] = $legacyQuestion['dolezitost'];


            $answers = [];
            foreach($legacyQuestion['answers.item_id'] as $key => $answer_id) {
                $answers[] = [
                    'answer_id' => $answer_id,
                    'sk_odpoved' => $legacyQuestion['answers.sk_odpoved'][$key],
                    'en_odpoved' => $legacyQuestion['answers.en_odpoved'][$key],
                    'sk_vysvetlenie' => $legacyQuestion['answers.sk_odpoved_vysvetlenie'][$key],
                    'en_vysvetlenie' => $legacyQuestion['answers.en_odpoved_vysvetlenie'][$key],
                    'sk_odpoved_media' => $legacyQuestion['answers.sk_odpoved_media'][$key],
                    'en_odpoved_media' => $legacyQuestion['answers.en_odpoved_media'][$key],
                    'spravnost' => $legacyQuestion['answers.spravnost'][$key],
                    'order' => $legacyQuestion['answers.order'][$key],
                ];
            }
            // order by order
            usort($answers, function($a, $b) {
                return $a['order'] <=> $b['order'];
            });

            $data['answers_count'] = count($legacyQuestion['answers.item_id']);
            foreach ($answers as $key => $answer) {
                $id = (int)$key + 1;

                $data['answers_answer_id___' . $id . '__'] = $answer['answer_id'];
                $data['answers_sk_odpoved___' . $id . '__'] = $answer['sk_odpoved'];
                $data['answers_en_odpoved___' . $id . '__'] = $answer['en_odpoved'];
                $data['answers_sk_vysvetlenie___' . $id . '__'] = $answer['sk_vysvetlenie'];
                $data['answers_en_vysvetlenie___' . $id . '__'] = $answer['en_vysvetlenie'];
                $data['answers_spravnost___' . $id . '__'] = $answer['spravnost'];

                if(is_array($answer['sk_odpoved_media']) && isset($answer['sk_odpoved_media'][0])) {
                    $path = $answer['sk_odpoved_media'][0]['file'];
                    $path = substr($path, strpos($path, '/.files'));
                    $this->setExistingFile('answers_sk_odpoved_media___' . $id . '__', $path, $answer['sk_odpoved_media'][0]['src'], 'truengine-legacy-data');
                }
                if(is_array($answer['en_odpoved_media']) && isset($answer['en_odpoved_media'][0])) {
                    $path = $answer['en_odpoved_media'][0]['file'];
                    $path = substr($path, strpos($path, '/.files'));
                    $this->setExistingFile('answers_en_odpoved_media___' . $id . '__', $path, $answer['en_odpoved_media'][0]['src'], 'truengine-legacy-data');
                }
            }



            //todo: zaradenie - aspon to 1 co je inicializovat
            for($i = 1; $i<=1; $i++) {
 //               categorizations_modul___1__
                if (!empty($legacyQuestion['modul'])) {
                    $field_id = DB::table('kega_items_test_questions__fields')
                        ->where('tag', 'modul')
                        ->pluck('field_id')
                        ->first();
                    $modul = $legacyQuestion['modul'];
                    $modul_id = DB::table('kega_items_test_questions__values')
                        ->where('enum_field_id', $field_id)
                        ->where('enum_field_value', $modul)
                        ->pluck('enum_id')
                        ->first();
                    $data['categorizations_modul___' . $i . '__'] = $modul_id;
                }

                if (!empty($legacyQuestion['program'])) {
                    $field_id = DB::table('kega_items_test_questions__fields')
                        ->where('tag', 'program')
                        ->pluck('field_id')
                        ->first();
                    $program = $legacyQuestion['program'];
                    $program_id = DB::table('kega_items_test_questions__values')
                        ->where('enum_field_id', $field_id)
                        ->where('enum_field_value', $program)
                        ->pluck('enum_id')
                        ->first();
                    $data['categorizations_program___' . $i . '__'] = $program_id;
                }

                if (!empty($legacyQuestion['predmet'])) {
                    $field_id = DB::table('kega_items_test_questions__fields')
                        ->where('tag', 'predmet')
                        ->pluck('field_id')
                        ->first();
                    $predmet = $legacyQuestion['predmet'];
                    $predmet_id = DB::table('kega_items_test_questions__values')
                        ->where('enum_field_id', $field_id)
                        ->where('enum_field_value', $predmet)
                        ->pluck('enum_id')
                        ->first();
                    $data['categorizations_predmet___' . $i . '__'] = $predmet_id;
                }

                if (!empty($legacyQuestion['kategoria'])) {
                    $field_id = DB::table('kega_items_test_questions__fields')
                        ->where('tag', 'kategoria')
                        ->pluck('field_id')
                        ->first();
                    $kategoria = $legacyQuestion['kategoria'];
                    $kategoria_id = DB::table('kega_items_test_questions__values')
                        ->where('enum_field_id', $field_id)
                        ->where('enum_field_value', $kategoria)
                        ->pluck('enum_id')
                        ->first();
                    $data['categorizations_kategoria___' . $i . '__'] = $kategoria_id;
                }

                if (!empty($legacyQuestion['podkategoria'])) {
                    $field_id = DB::table('kega_items_test_questions__fields')
                        ->where('tag', 'podkategoria')
                        ->pluck('field_id')
                        ->first();
                    $podkategoria = $legacyQuestion['podkategoria'];
                    $podkategoria_id = DB::table('kega_items_test_questions__values')
                        ->where('enum_field_id', $field_id)
                        ->where('enum_field_value', $podkategoria)
                        ->pluck('enum_id')
                        ->first();
                    $data['categorizations_podkategoria___' . $i . '__'] = $podkategoria_id;
                }
            }
            $data['categorizations_count'] = 1;

            $categories = DB::table('kega_items_test_questions__categories')
                ->where('question_id', $item_id)
                ->orderBy('id')
                ->get();
            if($categories->count()) {
                $i = 1;
                foreach($categories as $category) {
                    $data['categorizations_modul___' . $i . '__'] = $category->modul;
                    $data['categorizations_program___' . $i . '__'] = $category->program;
                    $data['categorizations_predmet___' . $i . '__'] = $category->predmet;
                    $data['categorizations_kategoria___' . $i . '__'] = $category->category;
                    $data['categorizations_podkategoria___' . $i . '__'] = $category->subcategory;
                    $i++;
                }
                $data['categorizations_count'] = $categories->count();
            }
        }
        else {
            $data['dolezitost'] = '3';
            $data['navrhovatel'] = Userlist::getCurrentUserId();
        }

        return $data;
    }

    private function getParentsForZaradenie():void
    {
        $allIds = [
            ...array_keys($this->modules),
            ...array_keys($this->programs),
            ...array_keys($this->predmets),
            ...array_keys($this->problems),
            ...array_keys($this->subcategories)
        ];

        $tmp = DB::table('kega_items_test_questions__tree__values')
            ->whereIn('enum_id', $allIds)
            ->get();

        $this->parents = [];
        foreach($tmp as $item) {
            if(!isset($this->parents[$item->enum_id])) {
                $this->parents[$item->enum_id] = [];
            }
            if($item->parent_value_id) {
                $this->parents[$item->enum_id][] = $item->parent_value_id;
            }

            if($item->grandparent_value_id) {
                $this->parents[$item->enum_id][] = $item->grandparent_value_id;
            }
        }
    }

    protected function getViewData()
    {
        return [
            'modules' => $this->getOpusValues('test_questions','modul', false, false),
            'programs' => $this->getOpusValues('test_questions','program', false, false),
            'predmets' => $this->getOpusValues('test_questions','predmet', false, false),
            'problems' => $this->getOpusValues('test_questions','kategoria', false, false),
            'subcategories' => $this->getOpusValues('test_questions','podkategoria', false, false),
            'tree' => $this->parents,
            ...parent::getViewData()
        ];
    }

    public function getTags($field, $query):array
    {
        $legacyAutocomplete = new \autocomplete_gClass();

        return [
            'results' => array_map(function($item) {
                return [
                    'id' => $item,
                    'text' => $item
                ];
            }, $legacyAutocomplete->getValues($field, $query, 20))
        ];
    }

    public function getAutocomplete($field, $query):array
    {
        $legacyAutocomplete = new \autocomplete_gClass();

        return array_map(function($item) {
                return [
                    'label' => $item,
                    'value' => $item
                ];
            }, $legacyAutocomplete->getValues($field, $query, 20));
    }

    public function getValues($field, $query):array
    {
        $legacyAutocomplete = new \autocomplete_gClass();

        return [
            'results' => array_map(function($item) {
                return [
                    'id' => $item,
                    'text' => $item
                ];
            }, $legacyAutocomplete->getValues($field, $query, 20))
        ];
    }


    function processSubmit(array $validated):void
    {
        $data = [];
        $user_id = Userlist::getCurrentUserId();
        $files = [];

        $itemId = (int)$validated['item_id'];
        if($itemId) {
            //$question = Question::getQuestion($itemId);
            $question = Question::find($itemId);
            if(!$question) {
                throw new \Exception('Question not found.');
            }

            $question->edited = 'yes';
            $this->setSubmitMessage($this->getSubmitMessageForEdit());
        }
        else {
            $question = Question::createQuestion([
                'status' => $this->getStateForNew(),
                'owner_id' => $user_id,
                'garant_id' => 0,
                'edited' => 'no',
                'insert_time' => date('Y-m-d H:i:s'),
            ]);
            $this->setSubmitMessage($this->getSubmitMessageForNew());
        }

        $question->update_time = date('Y-m-d H:i:s');
        $question->sk_otazka = $validated['sk_otazka'];
        $question->en_otazka = $validated['en_otazka'];

        $processedFile = $this->prepareUploadedFile('sk_otazka_media', 'items_test_questions_sk_otazka_media');
        if($processedFile) {
            $files[] = $processedFile;
            if (isset($processedFile['prop_value'])) {
                $question->sk_otazka_media = $processedFile['prop_value'];
            }
        }

        $processedFile = $this->prepareUploadedFile('en_otazka_media', 'items_test_questions_en_otazka_media');
        if($processedFile) {
            $files[] = $processedFile;
            if (isset($processedFile['prop_value'])) {
                $question->en_otazka_media = $processedFile['prop_value'];
            }
        }

        // save categorization to legacy fields
        if($validated['categorizations'][0]['modul'] === null) {
            $question->modul = null;
        }
        else {
            $question->modul = $this->modules[(int)$validated['categorizations'][0]['modul']];
        }
        if($validated['categorizations'][0]['program'] === null) {
            $question->program = null;
        }
        else {
            $question->program = $this->programs[(int)$validated['categorizations'][0]['program']];
        }
        if($validated['categorizations'][0]['predmet'] === null) {
            $question->predmet = null;
        }
        else {
            $question->predmet = $this->predmets[(int)$validated['categorizations'][0]['predmet']];
        }
        if($validated['categorizations'][0]['kategoria'] === null) {
            $question->kategoria = null;
        }
        else {
            $question->kategoria = $this->problems[(int)$validated['categorizations'][0]['kategoria']];
        }
        if($validated['categorizations'][0]['podkategoria'] === null) {
            $question->podkategoria = null;
        }
        else {
            $question->podkategoria = $this->subcategories[(int)$validated['categorizations'][0]['podkategoria']];
        }

        $question->sk_vysvetlenie = $validated['sk_vysvetlenie'];
        $question->en_vysvetlenie = $validated['en_vysvetlenie'];
        $question->literatura = $validated['literatura'];
        $question->literatura_strana = $validated['literatura_strana'];


        // navrhovatel @user=admin
        $user = DB::table('kega_items_users__data')
            ->where('item_id', $validated['navrhovatel'])
            ->first();
        if($user) {
            $question->navrhovatel = '@user=' . $user->login;
        }
        else {
            $question->navrhovatel = '@user=' . $validated['navrhovatel'];
        }

        // keywords  test, test2
        $question->sk_keywords = implode(',', array_map('trim', isset($validated['sk_keywords']) ? $validated['sk_keywords'] : []));
        $question->en_keywords = implode(',', array_map('trim', isset($validated['en_keywords']) ? $validated['en_keywords'] : []));

        // dolezitost _1
        $question->dolezitost = $validated['dolezitost'];



        $question->save();

        $this->saveCategories($question, $validated);

        $this->processFiles($files, $question->item_id);

        $this->saveAnswers($question, $validated);
    }

    private function saveAnswers($question, $validated)
    {
        $dbAnswerIds = DB::table('kega_items_test_answers__data')
            ->where('test_question', $question->item_id)
            ->pluck('item_id')
            ->toArray();


        $i = 0;
        foreach ($validated['answers'] as $answer) {
            $i++;

            $files = [];
            $processedFile = $this->prepareUploadedFile('sk_odpoved_media', 'items_test_answers_sk_odpoved_media', 'answers', $i);
            if($processedFile) {
                $files[] = $processedFile;
                if (isset($processedFile['prop_value'])) {
                    $answer['sk_odpoved_media'] = $processedFile['prop_value'];
                }
            }
            $processedFile = $this->prepareUploadedFile('en_odpoved_media', 'items_test_answers_en_odpoved_media', 'answers', $i);
            if($processedFile) {
                $files[] = $processedFile;
                if (isset($processedFile['prop_value'])) {
                    $answer['en_odpoved_media'] = $processedFile['prop_value'];
                }
            }

            if($answer['answer_id'] === null) {
                $answerId = DB::table('kega_items_test_answers__data')->insertGetId([
                    'test_question' => $question->item_id,
                    'status' => 'active',
                    'owner_id' => $question->owner_id,
                    'insert_time' => date('Y-m-d H:i:s'),
                    'update_time' => date('Y-m-d H:i:s'),
                    'spravnost' => $answer['spravnost'],
                    'order' => $i,
                    'sk_odpoved' => $answer['sk_odpoved'],
                    'en_odpoved' => $answer['en_odpoved'],
                    'sk_odpoved_vysvetlenie' => $answer['sk_vysvetlenie'],
                    'en_odpoved_vysvetlenie' => $answer['en_vysvetlenie'],
                ]);
            }
            else {
                DB::table('kega_items_test_answers__data')
                    ->where('item_id', $answer['answer_id'])
                    ->update([
                        'status' => 'active',
                        'owner_id' => $question->owner_id,
                        'update_time' => date('Y-m-d H:i:s'),
                        'spravnost' => $answer['spravnost'],
                        'order' => $i,
                        'sk_odpoved' => $answer['sk_odpoved'],
                        'en_odpoved' => $answer['en_odpoved'],
                        'sk_odpoved_vysvetlenie' => $answer['sk_vysvetlenie'],
                        'en_odpoved_vysvetlenie' => $answer['en_vysvetlenie'],
                    ]);
                $answerId = $answer['answer_id'];
            }

            $this->processFiles($files, $answerId);

            $dbAnswerIds = array_diff($dbAnswerIds, [$answer['answer_id']]);
        }

        // delete all remaining answers
        if(count($dbAnswerIds)) {
            DB::table('kega_items_test_answers__data')
                ->whereIn('item_id', $dbAnswerIds)
                ->update([
                    'status' => 'deleted',
                    'update_time' => date('Y-m-d H:i:s'),
                ]);
        }
    }

    private function saveCategories($question, $validated)
    {
        // najskor zmazem vsetky
        DB::table('kega_items_test_questions__categories')
            ->where('question_id', $question->item_id)
            ->delete();

        // a nasledne ich insertnem nanovo
        foreach($validated['categorizations'] as $category) {

            DB::table('kega_items_test_questions__categories')->insert([
                'question_id' => $question->item_id,
                'modul' => $category['modul'],
                'program' => $category['program'],
                'predmet' => $category['predmet'],
                'category' => $category['kategoria'],
                'subcategory' => $category['podkategoria'],
            ]);
        }
    }



}
