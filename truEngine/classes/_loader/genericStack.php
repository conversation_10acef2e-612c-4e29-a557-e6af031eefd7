<?php


abstract class genericStack_gClass extends truEngineBaseClass_gClass {
    private static $instances_pVar = array();
    private $stacks_pVar = array();
    private $lastStackId_pVar = -1;
    private $data_pVar = array();

    protected function __construct()
    {

    }

    protected static function getInstance_gFunc($instanceName_pVar)
    {
        if(!isset(self::$instances_pVar[$instanceName_pVar])) {
            self::$instances_pVar[$instanceName_pVar] = new $instanceName_pVar;
        }
        return(self::$instances_pVar[$instanceName_pVar]);
    }

    protected static function destroyInstance_gFunc($instanceName_pVar)
    {
        if(isset(self::$instances_pVar[$instanceName_pVar])) {
            unset(self::$instances_pVar[$instanceName_pVar]);
        }
    }

    protected static function genCreateStack_gFunc($instanceName_pVar)
    {
        $instance_pVar = self::getInstance_gFunc($instanceName_pVar);
        $instance_pVar->lastStackId_pVar++;
        $instance_pVar->stacks_pVar[$instance_pVar->lastStackId_pVar] = $instance_pVar->data_pVar;
        $instance_pVar->data_pVar = array();
    }

    protected static function genDestroyStack_gFunc($instanceName_pVar)
    {
        $instance_pVar = self::getInstance_gFunc($instanceName_pVar);
        if($instance_pVar->lastStackId_pVar < 0) {
            $instance_pVar->data_pVar = array();
            self::destroyInstance_gFunc();
            return;
        }

        $instance_pVar->data_pVar = $instance_pVar->stacks_pVar[$instance_pVar->lastStackId_pVar];
        unset($instance_pVar->stacks_pVar[$instance_pVar->lastStackId_pVar]);
        $instance_pVar->lastStackId_pVar--;
    }

    protected static function genGetStackData_gFunc($instanceName_pVar)
    {
        $instance_pVar = self::getInstance_gFunc($instanceName_pVar);
        return($instance_pVar->data_pVar);
    }

    protected static function genPush_gFunc($instanceName_pVar, $data_pVar)
    {
        $instance_pVar = self::getInstance_gFunc($instanceName_pVar);
        array_push($instance_pVar->data_pVar, $data_pVar);
    }

    protected static function genPop_gFunc($instanceName_pVar)
    {
        $instance_pVar = self::getInstance_gFunc($instanceName_pVar);
        return(array_pop($instance_pVar->data_pVar));
    }
}

