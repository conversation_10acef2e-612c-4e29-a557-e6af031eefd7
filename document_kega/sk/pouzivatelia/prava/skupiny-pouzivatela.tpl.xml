<?xml version="1.0" encoding="utf-8" ?>
<truEngine-document xmlns:te="http://www.truengine.sk" te:access="s_system_show_user_groups">

<te:source name="list_user_groups" varname="data" user_id="@{request:item_id}" columns="&amp;nbsp;,group_id,group_name" pager="50,@{request:page}" delete="@{request:delete}" group_id_add="@{request:group_id_add}" />

<te:content-set name="title" value="Skupiny používateľa @{data:_title}" />

<p te:access="s_system_add_user_group">
	<a href="#" onclick="$('add_user_group').style.display='block'; return(false);"><te:string id="Pridať skupinu používateľovi @{data:_title}" /></a>
	<div id="add_user_group" style="display:none;">
		<form action="/pouzivatelia/prava/skupiny-pouzivatela&amp;item_id=@{request:item_id}" method="post">
			@L{Pridať skupinu}
			<select name="group_id_add">
				<option></option>
				<te:for each="@{data:_groups} as @{group}">
					<option value="@{group:group_id}">@{group:group_name}</option>
				</te:for>
			</select> <te:string id="používateľovi @{data:_title}" />
			<input type="submit" value="@L{Pridať skupinu}" />
		</form>
	</div>
</p>


<te:include name="/sk/components/table" table="@@{data}">
	<te:include-param name="&amp;nbsp;">
		<a href="/pouzivatelia/prava/skupiny-pouzivatela&amp;item_id=@{request:item_id}&amp;delete=%%group_id%%" onclick="return(confirm('Naozaj chcete odobrať skupinu %%group_name%% používateľovi @{data:_title}?'));" te:access="s_system_delete_user_group">@L{Odobrať}</a>
	</te:include-param>
</te:include>

<p>
<a href="/pouzivatelia/prava/skupiny">@L{Skupiny}</a>
</p>

<te:content-set name="pdf_icon" value="@{REQUEST_URI}" />
</truEngine-document>