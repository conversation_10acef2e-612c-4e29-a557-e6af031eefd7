<?xml version="1.0" encoding="utf-8" ?>
<truEngine-layout xmlns:te="http://www.truengine.sk">&lt;?xml version="1.0" encoding="utf-8"?&gt;
&lt;!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN"
	"http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"&gt;
<html te:xmlns="http://www.w3.org/1999/xhtml" xml:lang="sk">
	<head>
        <?php
            // echo app('Illuminate\Foundation\Vite')(['resources/css/app.scss', 'resources/js/app.js']);
            ?>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet" integrity="sha384-QWTKZyjpPEjISv5WaRU9OFeRpok6YctnYmDr5pNlyT2bRjXh0JMhjY6hW+ALEwIH" crossorigin="anonymous" />
        <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js" integrity="sha384-YvpcrYf0tY3lHB60NNkmXc5s9fDVZLESaAA55NDzOxhy9GkcIdslK1eN7N6jIeHz" crossorigin="anonymous"></script>

		<te:include name="/sk/components/layout-head" layout="kega-modern" />
		<link rel="stylesheet" href="/css/v4.css" type="text/css" media="screen" />
		<link rel="stylesheet" href="/css/screen.css" type="text/css" media="screen" />
		<link rel="stylesheet" href="/css/print.css" type="text/css" media="print" />
		<link rel="stylesheet" href="/css/fixedWidth.css" type="text/css" media="screen" />

        <?php echo '<link rel="stylesheet" href="/css/layout/basic.css?t=' . config('opus.scripts_version') . '" type="text/css" />' ?>
	</head>
	<body onload="teInit();"><a name="top"></a>
		<te:include name="/sk/components/browser-version" />
		<div id="pageWrapper">

			<hr class="hide" />
			<div id="masthead" class="inside" style="border:none;">
				<te:if test="main_gClass::getConfigVar_gFunc('test', 'main')">
					<div style="background-color:#ffaaaa">
						<h1><a href="/">OPUS SAPIENTIÆ - TESTOVACIA VERZIA</a></h1>
					</div>
				<te:else />
					<h1><a href="/">OPUS SAPIENTIÆ</a></h1>
				</te:if>
<te:include name="/sk/components/layout-languages" />
			</div>
			<hr class="hide" />
			<div class="hnav">

			<te:include name="/sk/components/layout-top-menu" menupath="@@{menupath}" />

			</div>
			<div id="outerColumnContainer">
				<div id="innerColumnContainer">
					<hr class="hide" />
					<div id="leftColumn">
						<div class="inside">
						</div>
					</div>
					<hr class="hide" />
					<div id="rightColumn">
						<div class="inside">
<a href="/chyba">Help</a>

<div class="vnav">
	<te:include name="/sk/components/layout-side-menu"  menupath="@{menupath}"/>
</div>
						</div>
					</div>
					<div id="contentColumn">

						<hr class="hide" />
						<a name="skipToContent" id="skipToContent"></a>
						<div class="inside">
							<te:include name="/sk/components/check-isic" />
							<te:include name="/sk/components/layout-content" />
						</div>
					</div>
					<div class="clear mozclear"></div>
				</div>
			</div>



			<div class="hide" id="nsFooterClear"><!-- for NS4's sake --></div>
			<hr class="hide" />
			<div id="footer" class="inside">
<div id="disclaimer">
      		<te:include name="/sk/components/layout-disclaimer-sk" /><br />
      		<te:include name="/sk/components/layout-disclaimer-en" />
</div>
<p style="margin:0;">
	&amp;copy; truEngine. <a href="/truEngine" style="color:#000;">Generuje TruEngine</a>.<br />
	<span style="color:#bb9;"><?php
	$version = main_gClass::getVersion_gFunc();
	echo 'Last updated on ' . date('F j, Y', strtotime($version[0])) . ' ';
	echo '(build ' . $version[1] . ')';
	?></span>
</p>

			</div>
			<hr class="hide" />
		</div>
		<te:include name="/sk/components/layout-layout-menu" layout="basic" />
	</body>

</html>
</truEngine-layout>
