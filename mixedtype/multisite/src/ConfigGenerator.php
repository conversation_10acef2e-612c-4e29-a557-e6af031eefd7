<?php

namespace Mixedtype\Multisite;

use Mixedtype\Mixedtype\Helper;

class ConfigGenerator
{
    function generateSitesConfig() : array
    {
        $globalVars = $this->getConfigVars('MULTISITE_', [
            'sites' => ['app'],
            'languages' => ['en'],
            'path_prefix' => '/',
            'domain' => parse_url(env('APP_URL'), PHP_URL_HOST),
            'use_lng_prefix' => false,
        ]);
        if(empty($globalVars['sites'])) {
            throw new \Exception('No sites defined in .env file');
        }
        if(empty($globalVars['domain'])) {
            $globalVars['domain'] = parse_url(env('APP_URL'), PHP_URL_HOST);
        }

        $conf = [];

        foreach($globalVars['sites'] as $site) {
            $site_key = 'MULTISITE_' . strtoupper($site) . '_';
            $siteVars = $this->getConfigVars($site_key, [
                'languages' => $globalVars['languages'],
                'path_prefix' => $globalVars['path_prefix'],
                'domain' => $globalVars['domain'],
                'use_lng_prefix' => $globalVars['use_lng_prefix'],
            ]);

            foreach ($siteVars['languages'] as $language) {
                $language_key = $site_key . strtoupper($language) . '_';
                $lngVars = $this->getConfigVars($language_key, [
                    'domain' => $siteVars['domain'],
                    'path_prefix' => $siteVars['path_prefix'],
                    'use_lng_prefix' => $siteVars['use_lng_prefix'],
                ]);

                if(!isset($conf[$site])) {
                    $conf[$site] = [
                        'languages' => []
                    ];
                }

                $path = rtrim(!empty($lngVars['path_prefix'])?$lngVars['path_prefix']:($lngVars['use_lng_prefix'] ? ('/' . $language) : '/'), '/');
                if(empty($path)) {
                    $path = '/';
                }

                $conf[$site]['languages'][$language] = [
                    'domain' => $lngVars['domain'],
                    'path' => $path,
                    'locale' => $language,
                    'name' => 'language-' . $language,
                    'short_name' => strtoupper($language)
                ];
            }
        }

        return $conf;
    }

    private function getConfigVars($prefix, $defaults = []): array
    {
        $vars = [];
        foreach ($defaults as $key => $default) {

            if($key === 'languages' || $key === 'sites') {
                $vars[$key] = Helper::getEnvArray($key, $default, $prefix);
                continue;
            }
            if($key === 'path_prefix') {
                $default = rtrim($default, '/');
                $vars[$key] = rtrim(Helper::getEnvVar($key, $default, $prefix), '/');
                continue;
            }

            if($key === 'use_lng_prefix') {
                $vars[$key] = Helper::getEnvBoolean($key, $default, $prefix);
                continue;
            }

            $vars[$key] = Helper::getEnvVar($key, $default, $prefix);
        }


        return $vars;
    }
}