<IfModule mod_rewrite.c>
    <IfModule mod_negotiation.c>
        Options -MultiViews -Indexes
    </IfModule>

    RewriteEngine On

    # Handle Authorization Header
    RewriteCond %{HTTP:Authorization} .
    RewriteRule .* - [E=HTTP_AUTHORIZATION:%{HTTP:Authorization}]

    RewriteRule	^build/assets/.* -	[L]
    RewriteRule	^images/.* -	[L]

    # Redirect Trailing Slashes If Not A Folder...
    RewriteCond %{REQUEST_FILENAME} !-d
    RewriteCond %{REQUEST_URI} (.+)/$
    RewriteRule ^ %1 [L,R=301]


    RewriteRule ^\.files/.*$ index.php?seo=on&doc=$0	[B,L,QSA]
    RewriteRule	^livewire/livewire.js$ index.php		[L]
    RewriteRule	^livewire/livewire.min.js$ index.php	[L]


#    RewriteRule			^.*\.css$	-	[L,QSA]
#    RewriteRule			^.*\.jpg$	-	[L,QSA]
#    RewriteRule			^.*\.gif$	-	[L,QSA]
#    RewriteRule			^.*\.png$	-	[L,QSA]
#    RewriteRule			^.*\.js$	-	[L,QSA]
#    RewriteRule			^.*\.css$	-	[L,QSA]
#    RewriteRule			^.*\.mp4$	-	[L,QSA]
#    RewriteRule			^\/.*$		-	[L,QSA]


    RewriteCond %{REQUEST_FILENAME} !-d
    RewriteCond %{REQUEST_FILENAME} !-f
    RewriteRule ^.* index.php?seo=on&doc=$0 [L,QSA]
</IfModule>


