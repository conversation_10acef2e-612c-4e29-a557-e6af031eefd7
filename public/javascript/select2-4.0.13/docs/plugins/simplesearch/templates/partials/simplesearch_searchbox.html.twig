{% set min_chars = config.get('plugins.simplesearch.min_query_length', 3) %}
<div class="search-wrapper">
    <form name="search" data-simplesearch-form>
        <input
            name="searchfield"
            class="search-input"
            type="text"
            {% if min_chars > 0 %} min="{{- min_chars -}}" {% endif %}
            required
            placeholder="{{"PLUGIN_SIMPLESEARCH.SEARCH_PLACEHOLDER"|t}}"
            value="{{ query }}"
            data-search-invalid="{{ "PLUGIN_SIMPLESEARCH.SEARCH_FIELD_MINIMUM_CHARACTERS"|t(min_chars)|raw }}"
            data-search-separator="{{ config.system.param_sep }}"
            data-search-input="{{ base_url }}{{ config.plugins.simplesearch.route == '@self' ? '' : (config.plugins.simplesearch.route == '/' ? '' : config.plugins.simplesearch.route) }}/query"
        />
        {% if config.plugins.simplesearch.display_button %}
            <button type="submit" class="search-submit">
                <img src="{{ url('plugin://simplesearch/assets/search.svg') }}" />
            </button>
        {% endif %}
    </form>
</div>
