<?php

namespace App\Opus\Forms;

use Illuminate\Support\Facades\Storage;
use Illuminate\Validation\ValidationException;
use Mixedtype\Forms\Form;
use Mixedtype\Forms\Interface\FormInterface;

class HomepageSkSettingsForm extends Form implements FormInterface
{
    protected function getStorageFileName()
    {
        return 'homepage-text-sk.html';
    }

    function getDefaultData():array
    {
        return[
            'text' => Storage::exists($this->getStorageFileName()) ? Storage::get($this->getStorageFileName()):''
        ];
    }

    function getRules():array
    {
        return [
            'text' => 'required'
        ];
    }

    function processSubmit(array $validated):void
    {
        $failed = false;
        if(\session_gClass::userHasRightsInfo_gFunc(\s_system_superadmin)) {
            if(!Storage::put($this->getStorageFileName(), $validated['text'])) {
                $failed = true;
            }
        }
        else {
            $failed = true;
        }

        if($failed) {
            throw ValidationException::withMessages([
                'text' => 'Nepodarilo sa aktualizovať text.',
            ]);
        }
        else {
            $this->setSubmitMessage('Text bol úspešne aktualizovaný.');
        }
    }
}
