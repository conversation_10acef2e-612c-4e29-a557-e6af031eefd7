<?php

if(!isStamp_gFunc('items,access2_session', 'users email-public')) {
	$items_pVar = new install_item_gClass('users', false);
	$items_pVar->addField_gFunc('personal', 'email_public', 'enum', 'Zobraziť e-mail ostatným prihláseným používateľom', array('not_null'=>'yes', 'sk_default_value'=>'no', 'field_order'=>8));
	$items_pVar->setFormRule_gFunc('edit_item', 'email_public', 'edit');
	
	$items_pVar->addEnum_gFunc('email_public', 'yes', 'áno');
	$items_pVar->addEnum_gFunc('email_public', 'no', 'nie');
		
	$items_pVar->apply_gFunc();
	
	stamp_gFunc('items,access2_session', 'users email-public');
}


return(true);