<?php

class document_gClass extends truEngineBaseClass_gClass
{
    const DOCTYPE_DOCUMENT = 0;
    const DOCTYPE_LAYOUT = 1;
    private $docName_pVar;
    private $docType_pVar;
    public $httpErrCode_pVar;

    /**
     * $docName_pVar je relativna cesta ku korenu dokumentov.. Moze obsahovat uvodne lomitko,
     * ale aj nemusi. (nema vplyv na spracovanie, je hned odstranene).
     *
     * Ak $systemCall_pVar == true, tak su povolene dokumenty zacinajuce s .
     *
     * @param unknown_type $docName_pVar
     * @param unknown_type $docType_pVar
     * @return document_gClass
     */
    public static function getDocumentObject_gFunc($docName_pVar, $docType_pVar = self::DOCTYPE_DOCUMENT, $systemCall_pVar = false)
    {
        // odstranim uvodne lomitko
        if(!strlen($docName_pVar)) {
            $docName_pVar = '_default';
        }
        if($docName_pVar[0] == '/') {
            $docName_pVar = substr($docName_pVar, 1);
        }

        $tmpDocName_pVar = self::safeDocName_gFunc($docName_pVar, $docType_pVar, $systemCall_pVar);
        if($tmpDocName_pVar === false) {
            return new document404_gClass($docName_pVar, $docType_pVar);
        }
        else {
            $docName_pVar = $tmpDocName_pVar;
        }

        $dot_pVar = strrpos($docName_pVar, '.');
        if($dot_pVar === 0 || ($dot_pVar && substr($docName_pVar, $dot_pVar -1 , 1) === '/')) { // bodku na zaciatku nepovazujem za priponu (ani na zaciatku adresara /.)
            $dot_pVar = false;
        }
        if($dot_pVar !== false) {
            $ext_pVar = strtolower(substr($docName_pVar, $dot_pVar));

            //// tu osetrit pripony.. napr. xml, jpg, gif... dam dokument specialnym objektom
            if($ext_pVar == 'xml')
            {
                // ak *.tpl.xml, tak nemozem dat subor za ziadnych okolnosti
                if(strtolower(substr($docName_pVar, -8)) == '.tpl.xml') {
                    return new document404_gClass($docName_pVar, $docType_pVar);
                }
            }

            // todo...
            error_gClass::fatal_gFunc(__FILE__,__LINE__, $ext_pVar);
        }

        if(modules_gClass::isModuleRegistred_gFunc('templates', false)) {
            if(modules_gClass::isModuleRegistred_gFunc('cache', false)) {
                $templateName_pVar = self::getTemplateFileName_gFunc($docName_pVar);
                if(fileSafe_gClass::file_exists_gFunc($templateName_pVar)) {
                    //zistim ci existuje v cache
                    $fileName_pVar = self::getCacheFileName_gFunc($docName_pVar);
                    if(fileSafe_gClass::file_exists_gFunc($fileName_pVar)) {
                        // zistim, ci nebol dokument aktualizovany
                        $ftime1_pVar = filemtime(fileSafe_gClass::unsafePath_gFunc($fileName_pVar)); // filemtime nepodporuje wrapper
                        $ftime2_pVar = filemtime(fileSafe_gClass::unsafePath_gFunc($templateName_pVar)); // filemtime nepodporuje wrapper

                        if(fileSafe_gClass::file_exists_gFunc($templateName_pVar) && $ftime2_pVar < $ftime1_pVar) {
                            // nebol aktualizovany, mozem pouzit cache
                            if(modules_gClass::isModuleRegistred_gFunc('cache')) {
                                $obj_pVar = documentCache_gClass::getCacheObject_gFunc($docName_pVar, $docType_pVar);
                                if($obj_pVar !== null) {
                                    return $obj_pVar;
                                }
                                // ak sa nepodarilo nahrat z cache, pokracujem, a preparsujem dokument
                            }
                        }
                    }
                }
            }
            if(modules_gClass::isModuleRegistred_gFunc('templates')) {
                return documentTemplate_gClass::getTemplateObject_gFunc($docName_pVar, $docType_pVar, $systemCall_pVar);
            }
        }

        return new document_gClass($docName_pVar, $docType_pVar);
    }

    public static function getCacheFileName_gFunc($documentName_pVar)
    {
        $prefix_pVar = substr($documentName_pVar, 0, 3);
        if(in_array($prefix_pVar, array('sk/', 'en/', 'cz/'))) {
            $documentName_pVar = main_gClass::getLanguage_gFunc() . '/' . substr($documentName_pVar, 3);
        }
        else {
            $prefix_pVar = substr($documentName_pVar, 0, 12);
            if(in_array($prefix_pVar, array('.layouts/sk_', '.layouts/en_', '.layouts/cz_'))) {
                $documentName_pVar = '.layouts/'. main_gClass::getLanguage_gFunc() . '_' . substr($documentName_pVar, 12);
            }
        }

        $documentName_pVar = 'doc_' . $documentName_pVar;
        $documentName_pVar = str_replace('^', '^^', $documentName_pVar);
        $documentName_pVar = str_replace('/', '^', $documentName_pVar);
        $documentName_pVar = str_replace('\\', '^', $documentName_pVar);
        $documentName_pVar = str_replace('%', '^', $documentName_pVar);

        $pathCache_pVar = main_gClass::getPathForCache_gFunc();
        $fileName_pVar = $pathCache_pVar . $documentName_pVar . '.php';
        $fileName_pVar = fileSafe_gClass::safePath_gFunc($fileName_pVar);
        return($fileName_pVar);
    }

    public static function getTemplateFileName_gFunc($docName_pVar)
    {
        $pathWeb_pVar = main_gClass::getConfigVar_gFunc('documents_dir', 'main');
        $fileName_pVar = $pathWeb_pVar . $docName_pVar;

        if(is_dir($fileName_pVar)) {
            if(substr($fileName_pVar, -1) !== '/') {
                $fileName_pVar .= '/';
            }
            $fileName_pVar .= '_default';
        }
        $fileName_pVar .= '.tpl.xml';

        $fileName_pVar = fileSafe_gClass::safePath_gFunc($fileName_pVar);

        return($fileName_pVar);
    }


    public static function safeDocName_gFunc($docName_pVar, $docType_pVar, $systemCall_pVar = false)
    {
        // osetrim nazov dokumentu
        $docName_pVar = trim($docName_pVar);

        if(!strlen($docName_pVar))
        {
            return(false);
        }
        if(strpos($docName_pVar, '%') !== false) {
            return(false);
        }
        $docName_pVar = str_replace('\\', '/', $docName_pVar);
        while(strpos($docName_pVar, '/'.'/')) // // - nrmozem napisat priamo koli encoderu
        {
            $docName_pVar = str_replace('/'.'/', '/', $docName_pVar);
        }
        if($docName_pVar[0] == '/')
        {
            $docName_pVar = substr($docName_pVar, 1);
        }
        if(substr($docName_pVar, -1) == '/')
        {
            $docName_pVar = substr($docName_pVar, 0, -1);
        }
        if(strpos($docName_pVar, '..') !== false)
        {
            return(false);
        }
        if(!$systemCall_pVar) {
            if($docName_pVar[0] == '.' || strpos($docName_pVar, '/.') !== false)
            {
                // dokumenty zacinajuce bodkou su systemove (alebo aj adresare), teda do nich nemozem pristupovat priamo
                if($docType_pVar == self::DOCTYPE_DOCUMENT)
                {
                    return(false);
                }
                else if($docType_pVar == self::DOCTYPE_LAYOUT)
                {
                    if(substr($docName_pVar, 0, 9) != '.layouts/')
                    {
                        return(false);
                    }
                    if(strpos($docName_pVar, '/.') !== false) {
                        return(false);
                    }
                }
                else {
                    return(false);
                }
            }
        }
        if(strtolower(substr($docName_pVar, 0, 9)) == 'index.php')
        {
            return(false);
        }
        return($docName_pVar);
    }

    public function doc404_gFunc($giveDefaultDoc_pVar = false)
    {
        if(!$giveDefaultDoc_pVar) {
            if(modules_gClass::isModuleRegistred_gFunc('404')) {
                $ret_pVar = x404_gClass::doc404_gFunc($this->getDocName_gFunc());
                if($ret_pVar !== false) {
                    return($ret_pVar);
                }
            }
        }
        // vraciam default string.
        $doc_pVar = new document404_gClass($this->docName_pVar, $this->getDocType_gFunc());
        return($doc_pVar->execute_gFunc());
    }

    public function doc502_gFunc()
    {
        log_gClass::write_gFunc('ERROR 502 = temporary unavailable...');
        return('502');
    }

    function __construct($docName_pVar, $docType_pVar = self::DOCTYPE_DOCUMENT)
    {
        //$docName_pVar = $this->safeDocName_gFunc($docName_pVar, $docType_pVar);
        $this->docName_pVar = $docName_pVar;
        $this->docType_pVar = $docType_pVar;
        $this->httpErrCode_pVar = 200;
    }

    public function getDocName_gFunc()
    {
        return($this->docName_pVar);
    }

    public function getDocType_gFunc()
    {
        return($this->docType_pVar);
    }

    protected function setDocName_gFunc($docName_pVar)
    {
        $this->docName_pVar = $docName_pVar;
    }

    function execute_gFunc()
    {
        $pathWeb_pVar = main_gClass::getConfigVar_gFunc('documents_dir', 'main');
        $fileName_pVar = $pathWeb_pVar . $this->docName_pVar;

        if(!fileSafe_gClass::file_exists_gFunc($fileName_pVar)) {
            return($this->doc404_gFunc());
        }

        ob_start();
        include_once($fileName_pVar);
        $content_pVar = ob_get_contents();
        ob_end_clean();
        return($content_pVar);
    }

    /**
     * Oznaci aktivne linky, podla aktualneho dokumentu
     */
    static function menuSelectActiveLink_gFunc(&$menuArrayStruct_pVar)
    {
        $doc_pVar = callStack_gClass::getDocName_gFunc();
        if(strlen($doc_pVar)) {
            if($doc_pVar[0] == '/') {
                $doc_pVar = substr($doc_pVar, 1);
            }
            if(substr($doc_pVar, -1) == '/') {
                $doc_pVar = substr($doc_pVar, 0, -1);
            }
        }
        $doc_pVar = main_gClass::removeCurrentLanguagePrefixFromDocName_gFunc($doc_pVar);

        $defaultId_pVar = false;
        foreach ($menuArrayStruct_pVar['paths_pVar'] as $kk_pVar=>$path_pVar) {
            if($kk_pVar === 'group_pVar') {
                if(isset($menuArrayStruct_pVar['paths_pVar']['default_pVar'])) {
                    $defaultId_pVar = array($path_pVar=>$menuArrayStruct_pVar['paths_pVar']['default_pVar']);
                }
                continue;
            }
            if($kk_pVar === 'default_pVar') {
                continue;
            }
            if(isset($path_pVar['link_pVar'])) {
                // nie je to regularny vyraz
                if(!isset($path_pVar['link_pVar'][0])) {
                    continue;
                }
                $link_pVar = $path_pVar['link_pVar'];
                if($link_pVar[0] == '/') {
                    $link_pVar = substr($link_pVar, 1);
                }
                if($link_pVar == $doc_pVar) {
                    unset($menuArrayStruct_pVar['paths_pVar']);
                    self::menuSelectActiveLinkById_gFunc($menuArrayStruct_pVar, $path_pVar['id_pVar'], $defaultId_pVar);
                    return;
                }
            }
            else {
                // je to regularny vyraz
                if(!isset($path_pVar['path_pVar']) || !isset($path_pVar['path_pVar'][0])) {
                    continue;
                }

                //nastavim parametre, ak treba
                $docx_pVar = $doc_pVar;
                if(isset($path_pVar['params_pVar'])) {
                    $params_pVar = explode(',', $path_pVar['params_pVar']);
                    $n_pVar = 0;
                    foreach ($params_pVar as $v_pVar) {
                        $v_pVar = trim($v_pVar);
                        $requestValue_pVar = main_gClass::getInputString_gFunc($v_pVar, main_gClass::SRC_REQUEST_pVar, false, null);
                        if($requestValue_pVar !== null) {
                            if(!$n_pVar) {
                                $docx_pVar .= '?';
                            }
                            else {
                                $docx_pVar .= '&';
                            }
                            $docx_pVar .= $v_pVar . '=' . $requestValue_pVar;
                        }
                    }
                }

                $pattern_pVar = $path_pVar['path_pVar'];
                if($pattern_pVar[0] === '$') {
                    $pattern_pVar = substr($pattern_pVar, 1);
                }
                else {
                    $pattern_pVar = '/' . $pattern_pVar . '/i';
                }

                if(preg_match($pattern_pVar, $docx_pVar)) {
                    unset($menuArrayStruct_pVar['paths_pVar']);
                    self::menuSelectActiveLinkById_gFunc($menuArrayStruct_pVar, $path_pVar['id_pVar'], $defaultId_pVar);
                    return;
                }
            }
        }
        // musim spravit unset, aby mi to v HTML nezavadzalo (uz to nepotrebujem)
        unset($menuArrayStruct_pVar['paths_pVar']);
        if($defaultId_pVar !== false) {
            $v_pVar = reset($defaultId_pVar);
            $k_pVar = key($defaultId_pVar);
            $vv_pVar = main_gClass::getInputString_gFunc('menu_' . $k_pVar, main_gClass::SRC_SESSION_pVar);
            if($vv_pVar !== null) {
                $v_pVar = $vv_pVar;
            }
            self::menuSelectActiveLinkById_gFunc($menuArrayStruct_pVar, $v_pVar, $defaultId_pVar);
        }
    }

    /**
     * Najdem polozku menu (rekurzivne) a oznacim ju ako active
     */
    private static function menuSelectActiveLinkById_gFunc(&$menuArrayStruct_pVar, $menuId_pVar, $default_pVar)
    {
        if($default_pVar !== false) {
            $v_pVar = reset($default_pVar);
            $k_pVar = key($default_pVar);
            main_gClass::setPhpSessionVar_gFunc('menu_' . $k_pVar, $menuId_pVar);
        }

        //echo $menuId_pVar.'<pre>'; print_r($menuArrayStruct_pVar); exit;
        foreach ($menuArrayStruct_pVar as $k_pVar=>$v_pVar) {
            if($k_pVar === 'link' || $k_pVar === 'title' || $k_pVar === 'label' || $k_pVar === 'id') {
                continue;
            }
            if(isset($v_pVar['id']) && $v_pVar['id'] === $menuId_pVar) {
                $menuArrayStruct_pVar[$k_pVar]['active'] = true;
                return(true);
            }
            if(is_array($v_pVar)) {
                if(self::menuSelectActiveLinkById_gFunc($menuArrayStruct_pVar[$k_pVar], $menuId_pVar, false)) {
                    // a tuto linku oznacim ako opened
                    if(isset($menuArrayStruct_pVar['id'])) {
                        $menuArrayStruct_pVar['opened'] = true;
                    }
                    return(true);
                }
            }
        }
        return(false);
    }

    /*
    private static function _menuSelectActiveLink_gFunc(&$menuArrayStruct_pVar)
    {
    	$doc_pVar = callStack_gClass::getDocName_gFunc();
    	if(strlen($doc_pVar)) {
	    	if($doc_pVar[0] == '/') {
	    		$doc_pVar = substr($doc_pVar, 1);
	    	}
	    	if(substr($doc_pVar, -1) == '/') {
	    		$doc_pVar = substr($doc_pVar, 0, -1);
	    	}
    	}

    	$doc_pVar = main_gClass::removeCurrentLanguagePrefixFromDocName_gFunc($doc_pVar);

    	self::_rMenuSelectActiveLink_gFunc($menuArrayStruct_pVar, $doc_pVar, $menuArrayStruct_pVar['paths_pVar']);
    	// musim spravit unset, aby mi to v HTML nezavadzalo (uz to nepotrebujem)
    	unset($menuArrayStruct_pVar['paths_pVar']);

    }*/

    /*
	private static function _rMenuSelectActiveLink_gFunc(&$menuArrayStruct_pVar, $docName_pVar, &$paths_pVar)
	{
		if(isset($menuArrayStruct_pVar['id'])) {
			// spracujem podla path pola
			foreach ($paths_pVar as $v_pVar) {


			}
		}
		else {
			// spracujem podla link
			if(isset($menuArrayStruct_pVar['link'])) {
				$link_pVar = $menuArrayStruct_pVar['link'];
		    	if($link_pVar[0] == '/') {
		    		$link_pVar = substr($link_pVar, 1);
		    	}
		    	if(substr($link_pVar, -1) == '/') {
		    		$link_pVar = substr($link_pVar, 0, -1);
		    	}

		    	if($link_pVar == $docName_pVar) {
					$menuArrayStruct_pVar['active'] = true;
				}
			}
			return(true);
		}


		foreach ($menuArrayStruct_pVar as $k_pVar => $v_pVar) {
			if($k_pVar == 'paths_pVar' || $k_pVar == 'link' || $k_pVar == 'title' || $k_pVar == 'label' || $k_pVar == 'active') {
				continue;
			}
			if(self::_rMenuSelectActiveLink_gFunc($menuArrayStruct_pVar[$k_pVar], $docName_pVar, $paths_pVar)) {
				return(true); /// nasiel som, viac nehladam.. Oznacujem vzdy len jednu polozku v menu (prvu najdenu)
			}
		}
		return(false);
	}
	*/

    public static function menuAddItemsFromArray_gFunc($sourceData_pVar, &$menuTmpStack_pVar, $menuTmpStackId_pVar)
    {
        if(!isset($sourceData_pVar['tree_childs']) || !count($sourceData_pVar['tree_childs'])) {
            return;
        }
        $menuTmpStack_pVar[$menuTmpStackId_pVar]['tree_childs'] = $sourceData_pVar['tree_childs'];
        if(!isset($menuTmpStack_pVar[0]['paths_pVar'])) {
            $menuTmpStack_pVar[0]['paths_pVar'] = array();
        }
        foreach ($sourceData_pVar['paths_pVar'] as $k_pVar=>$v_pVar) {
            $menuTmpStack_pVar[0]['paths_pVar'][] = $v_pVar;
        }
    }
}
