<?php

class questions_tree_gClass extends source_gClass
{
    private $tree_pVar;

    protected function getData()
    {
        if(!main_gClass::getCacheTagStatus_gFunc('test_categories_stats')) {
            //	.................... ale cachujem mozno podla prvej, alebo prvej+fruhej urovne?... Aby som nemusel prepocitavat cely strom?
//			.. alebo jednoducho prapocitam cely strom radcej... pre teraz.
        }

        $this->tree_pVar = db_items_gClass::getTree_gFunc('test_questions', 'zaradenie');

        $sql_pVar = 'SELECT
						`modul`, `program`, `predmet`, `kategoria`, `podkategoria`, `status`,
						COUNT(`item_id`) as n_questions,
						count(sk_otazka is not null and sk_otazka <> \'\') as n_questions_sk,
						sum(en_otazka is not null and en_otazka <> \'\') as n_questions_en,
						sum(cz_otazka is not null and cz_otazka <> \'\') as n_questions_cz,
						sum(obtiaznost) as obtiaznost,
						sum(nekorektnost) as nekorektnost,
						min(aktualnost) as aktualnost
					FROM `%titems_test_questions__data`
					WHERE `status` <> \'deleted\'
					GROUP BY `modul`, `program`, `predmet`, `kategoria`, `podkategoria`, `status`;';
        $tmp_pVar = db_public_gClass::getResultArray_gFunc($sql_pVar, __FILE__, __LINE__);

        $nullFixed_pVar = false;
        $stats_pVar = array();
        foreach($tmp_pVar as $v_pVar) {
//            if(!$nullFixed_pVar && (
//                    $v_pVar['modul'] === null
//                    || $v_pVar['program'] === null
//                    || $v_pVar['predmet'] === null
//                    || $v_pVar['kategoria'] === null
//                    || $v_pVar['podkategoria'] === null)) {
//                $sql_pVar = 'UPDATE `%titems_test_questions__data` SET `modul` = \'\' WHERE `modul` IS NULL';
//                db_public_gClass::execute_gFunc($sql_pVar, __FILE__, __LINE__);
//                $sql_pVar = 'UPDATE `%titems_test_questions__data` SET `program` = \'\' WHERE `program` IS NULL';
//                db_public_gClass::execute_gFunc($sql_pVar, __FILE__, __LINE__);
//                $sql_pVar = 'UPDATE `%titems_test_questions__data` SET `predmet` = \'\' WHERE `predmet` IS NULL';
//                db_public_gClass::execute_gFunc($sql_pVar, __FILE__, __LINE__);
//                $sql_pVar = 'UPDATE `%titems_test_questions__data` SET `kategoria` = \'\' WHERE `kategoria` IS NULL';
//                db_public_gClass::execute_gFunc($sql_pVar, __FILE__, __LINE__);
//                $sql_pVar = 'UPDATE `%titems_test_questions__data` SET `podkategoria` = \'\' WHERE `podkategoria` IS NULL';
//                db_public_gClass::execute_gFunc($sql_pVar, __FILE__, __LINE__);
//                $nullFixed_pVar = true;
//            }
            $key_pVar = $v_pVar['modul'] . '/' . $v_pVar['program'] . '/' . $v_pVar['predmet'] . '/' . $v_pVar['kategoria'] . '/' . $v_pVar['podkategoria'];
            if(!isset($stats_pVar[$key_pVar])) {
                $stats_pVar[$key_pVar] = array();
            }
            if(!isset($stats_pVar[$key_pVar][$v_pVar['status']])) {
                $stats_pVar[$key_pVar][$v_pVar['status']] = array('n_questions'=>0, 'n_questions_sk'=>0, 'n_questions_en'=>0, 'n_questions_cz'=>0, 'obtiaznost'=>0, 'nekorektnost'=>0, 'aktualnost'=>0);
            }
            $stats_pVar[$key_pVar][$v_pVar['status']]['n_questions'] += $v_pVar['n_questions'];
            $stats_pVar[$key_pVar][$v_pVar['status']]['n_questions_sk'] += $v_pVar['n_questions_sk'];
            $stats_pVar[$key_pVar][$v_pVar['status']]['n_questions_en'] += $v_pVar['n_questions_en'];
            $stats_pVar[$key_pVar][$v_pVar['status']]['n_questions_cz'] += $v_pVar['n_questions_cz'];
            $stats_pVar[$key_pVar][$v_pVar['status']]['obtiaznost'] += $v_pVar['obtiaznost'];
            $stats_pVar[$key_pVar][$v_pVar['status']]['nekorektnost'] += $v_pVar['nekorektnost'];
            if($stats_pVar[$key_pVar][$v_pVar['status']]['aktualnost'] && $v_pVar['aktualnost']) {
                $stats_pVar[$key_pVar][$v_pVar['status']]['aktualnost'] = min($v_pVar['aktualnost'], $stats_pVar[$key_pVar][$v_pVar['status']]['aktualnost']);
            }
            elseif($v_pVar['aktualnost']) {
                $stats_pVar[$key_pVar][$v_pVar['status']]['aktualnost'] = $v_pVar['aktualnost'];
            }
        }
        //echo '<pre>'; print_r($this->tree_pVar['_defs']); echo '</pre>';
        //echo '<pre>'; print_r($this->tree_pVar['_values']); echo '</pre>';
        foreach($stats_pVar as $path_pVar=>$stat_pVar) {
            $path_pVar = rtrim($path_pVar, '/');
            $pathArray_pVar = explode('/', $path_pVar);
            $this->applyStats_gFunc($this->tree_pVar['_struct']['childs'], $pathArray_pVar, $stat_pVar, array_keys($this->tree_pVar['_defs']));
        }

        $this->setFilter_gFunc($this->tree_pVar['_struct']['childs']
            , array(10=>'modul', 11=>'program', 12=>'predmet', 13=>'kategoria', 14=>'podkategoria'));

        return($this->tree_pVar);
    }

    private function applyStats_gFunc(&$childs_pVar, $pathArray_pVar, $stats_pVar, $defs_pVar)
    {
        $pathValue_pVar = array_shift($pathArray_pVar);
        $defValue_pVar = array_shift($defs_pVar);
        $found_pVar = false;
        foreach($childs_pVar as $k_pVar=>$child_pVar) {
            if($child_pVar['enum_field_value'] === $pathValue_pVar) {
                $found_pVar = true;
                if(!isset($child_pVar['stats'])) {
                    $childs_pVar[$k_pVar]['stats'] = $this->addStats_gFunc($stats_pVar);
                    $childs_pVar[$k_pVar]['stats'] = $this->statsCountTotal_gFunc($childs_pVar[$k_pVar]['stats']);
                }
                else {
                    $childs_pVar[$k_pVar]['stats'] = $this->addStats_gFunc($stats_pVar, $childs_pVar[$k_pVar]['stats']);
                    $childs_pVar[$k_pVar]['stats'] = $this->statsCountTotal_gFunc($childs_pVar[$k_pVar]['stats']);
                }

                if(count($pathArray_pVar)) {
                    if(!isset($child_pVar['childs']) || !is_array($child_pVar['childs'])) {
                        $childs_pVar[$k_pVar]['childs'] = array();
                    }
                    $this->applyStats_gFunc($childs_pVar[$k_pVar]['childs'], $pathArray_pVar, $stats_pVar, $defs_pVar);
                }
                break;
            }
        }

        if(!$found_pVar) {
            $value_pVar = array();
            $value_pVar['dummy'] = true;
            $value_pVar['enum_field_value'] = $pathValue_pVar;
            $value_pVar['enum_id'] = 0;
            if(empty($pathValue_pVar)) {
                $value_pVar['sk_enum_field_name_item'] = '***nezaradene';
            }
            else {
                $value_pVar['sk_enum_field_name_item'] = '***' .$defValue_pVar;
                foreach($this->tree_pVar['_values'][$this->tree_pVar['_defs'][$defValue_pVar]] as $v_pVar) {
                    if($v_pVar['enum_field_value'] == $pathValue_pVar) {
                        $value_pVar['sk_enum_field_name_item'] = $v_pVar['sk_enum_field_name_item'];
                        $value_pVar['enum_id'] = $v_pVar['enum_id'];
                        break;
                    }
                }
            }
            if(count($pathArray_pVar)) {
                $value_pVar['childs'] = array();
            }
            $childs_pVar[] = $value_pVar;
            array_unshift($pathArray_pVar, $pathValue_pVar);
            array_unshift($defs_pVar, $defValue_pVar);
            $this->applyStats_gFunc($childs_pVar, $pathArray_pVar, $stats_pVar, $defs_pVar);
        }

        return;
    }

    private function addStats_gFunc($stats1_pVar = array(), $stats2_pVar = array())
    {
        foreach($stats2_pVar as $k_pVar=>$v_pVar) {
            if(!isset($stats1_pVar[$k_pVar])) {
                $stats1_pVar[$k_pVar] = $v_pVar;
            }
            else {
                foreach($stats2_pVar[$k_pVar] as $kk_pVar=>$vv_pVar) {
                    if(!isset($stats1_pVar[$k_pVar][$kk_pVar])) {
                        $stats1_pVar[$k_pVar][$kk_pVar] = 0;
                    }
                    if($kk_pVar == 'aktualnost') {
                        if($stats1_pVar[$k_pVar][$kk_pVar] && $vv_pVar) {
                            $stats1_pVar[$k_pVar][$kk_pVar] = min($vv_pVar, $stats1_pVar[$k_pVar][$kk_pVar]);
                        }
                        elseif($vv_pVar) {
                            $stats1_pVar[$k_pVar][$kk_pVar] = $vv_pVar;
                        }
                    }
                    else {
                        $stats1_pVar[$k_pVar][$kk_pVar] += $vv_pVar;
                    }
                }
            }
        }
        return($stats1_pVar);
    }

    private function statsCountTotal_gFunc($stats_pVar)
    {
        $stats_pVar['total'] = $this->getEmptyCount_gFunc();
        foreach($stats_pVar as $k_pVar=>$v_pVar) {
            if($k_pVar == 'total') {
                continue;
            }
            foreach($v_pVar as $kk_pVar=>$vv_pVar) {
                if(!isset($stats_pVar['total'][$kk_pVar])) {
                    $stats_pVar['total'][$kk_pVar] = 0;
                }

                /*
                if($kk_pVar == 'aktualnost') {
                    if($vv_pVar && $stats_pVar['total'][$kk_pVar]) {
                        $stats_pVar['total'][$kk_pVar] = min($vv_pVar, $stats_pVar['total'][$kk_pVar]);
                    }
                    elseif($vv_pVar) {
                        $stats_pVar['total'][$kk_pVar] = $vv_pVar;
                    }
                }
                else {
                    $stats_pVar['total'][$kk_pVar] += $vv_pVar;
                }*/

                if(in_array($kk_pVar, array('aktualnost', 'obtiaznost', 'nekorektnost'))) {
                    if(isset($stats_pVar['active']) && isset($stats_pVar['active'][$kk_pVar])) {
                        $stats_pVar['total'][$kk_pVar] = $stats_pVar['active'][$kk_pVar];
                    }
                    else {
                        $stats_pVar['total'][$kk_pVar] = 0;
                    }
                }
                else {
                    $stats_pVar['total'][$kk_pVar] += $vv_pVar;
                }
            }
        }
        return($stats_pVar);
    }

    private function getEmptyCount_gFunc()
    {
        return(array('n_questions'=>0, 'n_questions_sk'=>0, 'n_questions_en'=>0, 'n_questions_cz'=>0, 'obtiaznost'=>0, 'nekorektnost'=>0, 'aktualnost'=>0));
    }

    private function setFilter_gFunc(&$childs_pVar, $defNames_pVar, $level_pVar = 0, $filter_pVar = array())
    {
        $tmp_pVar = array_values($defNames_pVar);
        $tmp_name_pVar = $tmp_pVar[$level_pVar];
        $level_pVar++;

        foreach($childs_pVar as $k_pVar=>$child_pVar) {
            $filter_pVar[$tmp_name_pVar] = $child_pVar['enum_field_value'];
            $childs_pVar[$k_pVar]['filter'] = $filter_pVar;
            if(isset($child_pVar['childs']) && is_array($child_pVar['childs'])) {
                $this->setFilter_gFunc($childs_pVar[$k_pVar]['childs'], $defNames_pVar, $level_pVar, $filter_pVar);
            }
        }
    }
}

class questions_tree extends questions_tree_gClass { }

