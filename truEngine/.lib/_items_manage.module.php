<?php
if(isset($_SERVER['SCRIPT_FILENAME']) && strtolower($_SERVER['SCRIPT_FILENAME']) == strtolower(str_replace("\\",'/',__FILE__))) { header("HTTP/1.0 404 Not Found");  return(false); }
if(isset($GLOBALS['file__items_manage_pVar'])) return(true);
$GLOBALS['file__items_manage_pVar']=true;

if(!modules_gClass::isModuleRegistred_gFunc('db'))
{
    return(false);
}
if(!modules_gClass::isModuleRegistred_gFunc('items'))
{
    return(false);
}

if(!modules_gClass::isModuleRegistred_gFunc('forms')) {
	return(false);
}


class items_manage_additem_gClass extends form_gClass
{
	function __construct($action_pVar = 'get')
	{
		parent::__construct($action_pVar);
	}

	protected function initForm_gFunc()
	{
		// musim ziskat data
		$data_pVar = array();
		if(isset($this->params) && count($this->params)) {
			$data_pVar = $this->params;
		}
		$this->form_init_pVar = true;
		items_gClass::initFormRef_gFunc('', $this, $data_pVar);
	}


	protected function getData()
	{
		$data_pVar = $this->getFormData_gFunc();
		if($data_pVar['error_code'] !== self::RESULT_OK_pVar) {
			return($data_pVar);
		}

		// ulozim item
		items_gClass::editItemByForm_gFunc('', $this);
		$itemId_pVar = $this->getVar_gFunc('item_id');

		$systemName_pVar = $this->getFieldValue_gFunc('_name');

		// aktualizujem tabulky
		db_items_gClass::createBaseStruct_gFunc($systemName_pVar);
		db_items_gClass::applyLanguagesToTables_gFunc($systemName_pVar);

		$data_pVar = $this->getFormData_gFunc();
		return($data_pVar);
	}
}

class items_manage_additem extends items_manage_additem_gClass
{

}


return(true);
