<?xml version="1.0" encoding="utf-8" ?>
<truEngine-document xmlns:te="http://www.truengine.sk">

<te:source name="addliteratura" varname="form" item_id="@{request:item_id}" status="active" default_status="active">
	<te:source-param name="submit_button_title_add"><te:string id="Pridať literatúru" /></te:source-param>
	<te:source-param name="submit_button_title_update"><te:string id="Aktualizovať literatúru" /></te:source-param>
</te:source>

<?php
	$items_pVar = main_gClass::getSessionData_gFunc('selected_items', array());
	if(isset($items_pVar['literatura']) && count($items_pVar['literatura']) && intval(@{request:item_id})) {
		?>
		<div class="msg_warning" id="lit_msg">
		<te:string id="Pozor! Odoslaním formulára budú aktualizovaná všetka označená literatúra!" /> (<?php echo implode(', ', array_keys($items_pVar['literatura'])); ?>)<br />
		<a href="#" onclick="msel_check('literatura', 0, 'clear'); $('lit_msg').style.display='none'; return(false);"><te:string id="Zrušiť označenie" /></a>
		</div>
		<?php
	}
?>
<te:include name="/sk/components/form" form="@@{form}" />
	<te:if test="@{form:hidden_fields:literatura_item_id:value}">
		<te:content-set name="title" value="@L{Editovať literatúru}" />
	<te:else />
		<te:content-set name="title" value="@L{Nová literatúra}" />
	</te:if>

	<te:if test="@{form:error_code} == 'ok'">
		<ul>
			<li><a href="/literatura/editovat-literaturu?item_id=@{form:vars:item_id}"><te:string id="Zobraziť literatúru" /></a></li>
			<li style="padding-top:30px;"><a href="/literatura/"><te:string id="Zoznam literatúry" /></a></li>
			<li style="padding-top:30px;"><a href="/literatura/nova"><te:string id="Nová literatúra" /></a></li>
		</ul>
	</te:if>

</truEngine-document>
