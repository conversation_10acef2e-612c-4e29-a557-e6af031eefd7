<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {

        Schema::table('kega_items_test_answers__data', function (Blueprint $table) {
            $table->index('status' , 'status_index');
        });

        Schema::table('kega_items_test_questions__data', function (Blueprint $table) {
            $table->index(['modul', 'program', 'predmet', 'kategoria', 'podkategoria'], 'categories_index');
        });

        Schema::table('kega_items_test_questions__data', function (Blueprint $table) {
            $table->index('sk_keywords', 'sk_keywords_index');
        });
        Schema::table('kega_items_test_questions__data', function (Blueprint $table) {
            $table->index('en_keywords', 'en_keywords_index');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('kega_items_test_answers__data', function (Blueprint $table) {
            $table->dropIndex('status_index');
        });

        Schema::table('kega_items_test_questions__data', function (Blueprint $table) {
            $table->dropIndex('categories_index');
        });

        Schema::table('kega_items_test_questions__data', function (Blueprint $table) {
            $table->dropIndex('sk_keywords_index');
        });

        Schema::table('kega_items_test_questions__data', function (Blueprint $table) {
            $table->dropIndex('en_keywords_index');
        });
    }
};
