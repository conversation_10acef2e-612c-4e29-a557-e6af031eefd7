<?php


class registeruser_gClass extends additem_gClass
{
    private $isic_failed_pVar;

    protected function initParams_gFunc()
    {
        $this->setParam_gFunc('formtype','register_user');
        $this->setParam_gFunc('itemtype', 'users');
        $this->isic_failed_pVar = false;
    }

    protected function initForm_gFunc($multiedit_pVar = false, $initFormRef_pVar = true)
    {
        parent::initForm_gFunc();
        $this->setFieldPrefix_gFunc('users_');

        $roles_pVar = main_gClass::getConfigVar_gFunc('registration_roles', 'access3_rights');
        if($roles_pVar !== null) {
            $roles_pVar = explode(',', $roles_pVar);
            if(count($roles_pVar)) {
                $options_pVar = $this->getFieldOptions_gFunc('rights', 'user_role');
                $newOptions_pVar = array();
                foreach($roles_pVar as $v_pVar) {
                    if(isset($options_pVar[$v_pVar])) {
                        $newOptions_pVar[$v_pVar] = $options_pVar[$v_pVar];
                    }
                }

                $this->setFieldOptions_gFunc('rights', 'user_role', $newOptions_pVar);
            }
        }
        $this->setFieldPrefix_gFunc('');
    }

    protected function validateField_gFunc($fieldsetName_pVar, $fieldName_pVar, $applyPrefix_pVar = true)
    {
        if($fieldName_pVar === 'users_email') {
            $this->setCurrentValue_pVar($fieldsetName_pVar, $fieldName_pVar, $this->getFieldValue_gFunc('users_login'));
        }

        $result_pVar = parent::validateField_gFunc($fieldsetName_pVar, $fieldName_pVar);

        if($result_pVar === true) {
            if($fieldName_pVar === 'users_login' || $fieldName_pVar === 'users_nick' || $fieldName_pVar === 'users_email') {
                // zistim ci uz existuje takyto login alebo nick alebo email
                if(!session_session_gClass::checkForUniqueLoginName_gFunc(
                    $this->getFieldValue_gFunc($fieldName_pVar),
                    $this->getVar_gFunc('item_id')
                )) {
                    // error
                    $this->setError_gFunc(string_gClass::get('str__session_login_exists_sVar'), $fieldName_pVar);
                    return(false);
                }
            }

            if($fieldName_pVar === 'users_login') {
                $login_pVar = $this->getFieldValue_gFunc($fieldName_pVar);
                if (!filter_var($login_pVar, FILTER_VALIDATE_EMAIL)) {
                    // error
                    $this->setError_gFunc(string_gClass::get('str__session_invalid_email_sVar'), $fieldName_pVar);
                    return(false);
                }
            }

            if($fieldName_pVar === 'users_password' || $fieldName_pVar === 'users_password_2') {
                $pass =  $this->getFieldValue_gFunc($fieldName_pVar);
                if(strlen($pass) < 8) {
                    $this->setError_gFunc(string_gClass::get('str__session_password_too_short'), $fieldName_pVar);
                    return(false);
                }

                $pass =  $this->getFieldValue_gFunc('users_password');
                $pass2 =  $this->getFieldValue_gFunc('users_password_2');
                if($pass !== $pass2) {
                    $this->setError_gFunc(string_gClass::get('str__session_passwords_not_same'), $fieldName_pVar);
                    return(false);
                }
            }


            if($fieldName_pVar === 'users_isic') {
                // isic musi byt unikatny vramci registrovanych pouzivatelov
                $isic_pVar = $this->getFieldValue_gFunc($fieldName_pVar);
                $isic_pVar = str_replace(' ', '', $isic_pVar);
                if($isic_pVar !== '0000000000') {
                    $sql_pVar = 'SELECT count(*) FROM `%titems_users__data` WHERE `isic` = %s AND `status`<>\'deleted\' AND status<>\'request\' AND `user_role` <>\'tester\'';
                    $n_pVar = db_public_gClass::getField_gFunc($sql_pVar, __FILE__, __LINE__, $isic_pVar);
                    if($n_pVar === false || $n_pVar) {
                        $this->setError_gFunc(string_gClass::get('str__session_isic_exists_sVar'), $fieldName_pVar);
                        return(false);
                    }
                }
                else {
                    $this->isic_failed_pVar = true;
                }
            }
        }
        return($result_pVar);
    }

    protected function saveData_gFunc()
    {
        $this->setFieldPrefix_gFunc('users_');

        $nick = $this->getFieldValue_gFunc('login');
        $titul_pred = $this->getFieldValue_gFunc('titul_pred');
        $first_name = $this->getFieldValue_gFunc('first_name');
        $last_name = $this->getFieldValue_gFunc('last_name');
        $titul_za = $this->getFieldValue_gFunc('titul_za');

        if(!empty($first_name) && !empty($last_name)) {
            $nick = trim($titul_pred . ' ' . $first_name . ' ' . $last_name . ', ' . $titul_za, ', ');
        }


        //23.3.2014 -- Kanitra -- zmenena hodnota status z 'request' na 'active' a doplnene hodnoty isic_force_ok,isic_ok a email_check_status
        //DOVOD: nefunkcny server na overenie
        $data_pVar = [
            'status'=>'active',
            'email_check_status'=>'checked',
            'isic_force_ok'=>'yes',
            'isic_ok'=>'yes',
            'email' => $this->getFieldValue_gFunc('login'),
            'nick' => $nick,
        ];

        session_gClass::giveMeFullAccess_gFunc();

        $isic_pVar = $this->getFieldValue_gFunc('isic');
        if(!empty($isic_pVar)) {
            $isic_pVar = str_replace(' ', '', $isic_pVar);
            $data_pVar['isic'] = $isic_pVar;
        }
        $this->setFieldPrefix_gFunc('');


        items_gClass::editItemByForm_gFunc('users', $this, $data_pVar);

        session_gClass::revokeMeFullAccess_gFunc();

        $this->setFieldPrefix_gFunc('users_');
        $email_pVar = $this->getFieldValue_gFunc('email');
        $user_id_pVar = $this->getVar_gFunc('item_id');
        $isic_pVar = $this->getVar_gFunc('isic');
        $user_role_pVar = $this->getFieldValue_gFunc('user_role');
        $role_options_pVar = $this->getFieldOptions_gFunc('rights', 'user_role');
        $login_pVar = $this->getFieldValue_gFunc('login');
        $this->setFieldPrefix_gFunc('');

        // ulozim do DB
        $data_pVar = array(
            'user_id'=>$user_id_pVar,
            'request_time'=>date('Y-m-d H:i:s'),
            'request_type'=>'request'
        );
        db_public_gClass::insertData_gFunc('%tusers_requestlog', '%d,%s,%s', $data_pVar, __FILE__, __LINE__);

        // poslem overovaci mail...
        $time_limit = date('Y-m-d H:i:s', time() + 3600);
        $hash_pVar = sha1($user_id_pVar . $email_pVar . time());
        $subject_pVar = 'Žiadosť o registráciu';

        $template_pVar = '/.emails/registration';
        $template_pVar = main_gClass::addLngPrefixToDocName_gFunc($template_pVar);

        $vars_pVar = array('mail'=>array());
        $vars_pVar['mail']['email'] = $email_pVar;
        $vars_pVar['mail']['login'] = $login_pVar;
        $vars_pVar['mail']['url'] = main_gClass::getServerVar_gFunc('SERVER_NAME') . main_gClass::getConfigVar_gFunc('web_dir_gVar', 'runtime_pVar');
        $vars_pVar['mail']['url_registration'] = 'http://' . main_gClass::getServerVar_gFunc('SERVER_NAME') . main_gClass::makeUrl_gFunc(main_gClass::addLngPrefixToDocName_gFunc('/log-in/registracia-krok-1'));
        $vars_pVar['mail']['time'] = $time_limit;

        $vars_pVar['mail']['link'] = 'http://' . main_gClass::getServerVar_gFunc('SERVER_NAME') . main_gClass::makeUrl_gFunc(main_gClass::addLngPrefixToDocName_gFunc('/log-in/potvrdenie-registracie'));
        if(strpos($vars_pVar['mail']['link'], '?') !== false) {
            $vars_pVar['mail']['link'] .= '&';
        }
        else {
            $vars_pVar['mail']['link'] .= '?';
        }
        $vars_pVar['mail']['link'] .= 'email=' . $email_pVar . '&user_id=' . $user_id_pVar . '&hash=' . $hash_pVar;

        $templateContent_pVar = callStack_gClass::getDocContent_gFunc($template_pVar, $vars_pVar);
        email_gClass::mailHtml_gFunc($email_pVar, main_gClass::getConfigVar_gFunc('email', 'contacts'), $subject_pVar, $templateContent_pVar);

        // ulozim do DB
        $data_pVar = array(
            'email'=>$email_pVar,
            'hash'=>$hash_pVar,
            'user_id'=>$user_id_pVar,
            'request_time'=>date('Y-m-d H:i:s'),
            'destroy_time'=>$time_limit
        );
        //23.3.2014 Kanitra -- zakomentovane kvoli nefungujucemu mailovemu serveru
        //db_public_gClass::insertData_gFunc('%taccess__check_mail', '%s,%s,%d,%s,%s', $data_pVar, __FILE__, __LINE__);

        /**	nechcem hned upozornovat na nezadany isic, aby sa radcej sustredili na potvrdzovaci mail
         **/

        if(modules_gClass::isModuleRegistred_gFunc('cdouk')) {
            $cdouk_pVar = new cdouk_gClass();
            //23.3.2014 Kanitra -- zakomentovanie kvoli nefungujucemu overovaciemu serveru
            //$cdouk_pVar->getData_gFunc($isic_pVar, false, true, true); // updatnem profil z CDOUK, a overim ISIC
        }
        /*
                $roles_pVar = main_gClass::getConfigVar_gFunc('registration_roles', 'access3_rights');
                $roles2_pVar = main_gClass::getConfigVar_gFunc('registration_autoaccept', 'access3_rights');
                $autoaccept_pVar = false;
                if($roles_pVar !== null && $roles2_pVar !== null) {
                    $roles_pVar = explode(',', $roles_pVar);
                    $roles2_pVar = explode(',', $roles2_pVar);
                    if(count($roles_pVar) && count($roles2_pVar)) {
                        if(in_array($user_role_pVar, $roles_pVar) && in_array($user_role_pVar, $roles2_pVar)) {
                            // autoaccept...
                            $autoaccept_pVar = true;
                        }
                    }
                }
        */

        /**	nechcem hned upozornovat na inu rolu, aby sa radcej sustredili na potvrdzovaci mail
         **/

        $str_pVar = '&email=true';

        $this->setVar_gFunc('status', $str_pVar);
    }
}

class registeruser extends registeruser_gClass {}
