<?php 

if(!isStamp_gFunc('items', 'vytvorenie tabuliek items___')) {
	$sql_pVar = "
		CREATE TABLE IF NOT EXISTS `%titems___data` (
		  `item_id` int(10) unsigned NOT NULL auto_increment,
		  `status` enum('active', 'deleted') NOT NULL default 'active',
		  `name` varchar(255) NOT NULL default '',
		  `languages` set('sk','cz','en','de') NOT NULL default 'sk',
		  `tree` enum('yes','no') NOT NULL default 'no' COMMENT 'ci sa ma generovat strom alebo nie',
		  `tree_defs` enum('yes','no') NOT NULL default 'no' COMMENT 'ci sa ma generovat tabulky stromov alebo nie',
		  `forms` text COMMENT 'zoznam formularov oddelenych ciarkou',
		  `modules` text COMMENT 'zoznam modulov, ktore sa maju nahrat koli handlerom (oddelene ciarkou)',
		  `garant` enum('yes','no') NOT NULL default 'no' COMMENT 'ak je yes, tak su pristupne funkcie garantovania dat.',
		  PRIMARY KEY  (`item_id`)
		) ENGINE=InnoDB;
	";

	if(!db_public_gClass::execute_gFunc($sql_pVar, __FILE__, __LINE__)) {
		return(false);
	}

	$sql_pVar = "
		CREATE TABLE IF NOT EXISTS `%titems___fields` (
		  `field_id` int(10) unsigned NOT NULL auto_increment,
		  `name` varchar(255) NOT NULL default '' COMMENT 'indexovy nazov pola',
		  `tag` varchar(100) NOT NULL default '',
		  `type` enum('enum','set','int','varchar','text','float','date','time','datetime','imagelist','filelist','ximagelist','xfilelist') NOT NULL default 'int',
		  `len` int(10) unsigned default NULL COMMENT 'velkost v DB napr. varchar(255)',
		  `not_null` enum('yes','no') NOT NULL default 'no' COMMENT 'ci je priznak NOT NULL',
		  `default_value` varchar(255) default NULL COMMENT 'defaultna hodnota pola',
		  `pattern` varchar(255) default NULL COMMENT 'regularny vyraz, ktoremu musi zodpovdat vstup',
		  `min_value` varchar(100) default NULL COMMENT 'minimalna hodnota pri ciselnych poliach, minimalny pocet znakov pri retazcoch',
		  `max_value` varchar(100) default NULL COMMENT 'maximalna hodnota pri ciselnych poliach, maximalny pocet znakov pri retazcoch',
		  `url_order` int(10) unsigned default NULL COMMENT 'ak sa z tohto pola vytvara url, je tu pozicia v url od 1 do x',
		  `fieldset` int(10) unsigned default NULL COMMENT 'do ktoreho fieldsetu je zaradene toto pole. Ak je NULL nie je zaradene v ziadnom fieldsete, a je zobrazene mimo fieldsetov.',
		  `tab` int(10) unsigned default NULL COMMENT 'do ktoreho tabu je zaradene toto pole. Ak je NULL, nie je zaradene v ziadnom tabe a je zobrazene mimo tabov.',
		  `field_order` tinyint(3) unsigned default NULL COMMENT 'poradie pola vramci fieldsetu, alebo vramci formulara alebo tabu, ak nie su pouzite fieldsety',
		  `comment` varchar(255) COMMENT 'sql komentar',
		  PRIMARY KEY  (`field_id`),
		  UNIQUE KEY `tag` (`tag`)
		) ENGINE=InnoDB;
	";
	if(!db_public_gClass::execute_gFunc($sql_pVar, __FILE__, __LINE__)) {
		return(false);
	}

	$sql_pVar = "
		CREATE TABLE IF NOT EXISTS `%titems___fieldsets` (
		  `fieldset_id` int(10) unsigned NOT NULL auto_increment,
		  `fieldset_name` varchar(255) NOT NULL default '' COMMENT 'indexovy nazov fieldsetu',
		  `fieldset_legend` varchar(255) default NULL COMMENT 'legend popis, ak je NULL, legend tag nebude pouzity',
		  `fieldset_order` tinyint(4) default NULL COMMENT 'poradie fieldsetu vramci tabu alebo formulara (ak nie su pouzite taby)',
		  PRIMARY KEY  (`fieldset_id`)
		) ENGINE=InnoDB;
	";
	if(!db_public_gClass::execute_gFunc($sql_pVar, __FILE__, __LINE__)) {
		return(false);
	}

	$sql_pVar = "
		CREATE TABLE IF NOT EXISTS `%titems___tabs` (
		  `tab_id` int(10) unsigned NOT NULL auto_increment,
		  `tab_name` varchar(255) NOT NULL default '' COMMENT 'indexovy nazov tabu',
		  `tab_legend` varchar(255) NOT NULL default '' COMMENT 'nadpis tabu',
		  `tab_order` tinyint(4) default NULL COMMENT 'poradie tabu vramci formulara',
		  PRIMARY KEY  (`tab_id`)
		) ENGINE=InnoDB;
	";
	if(!db_public_gClass::execute_gFunc($sql_pVar, __FILE__, __LINE__)) {
		return(false);
	}

	$sql_pVar = "
		CREATE TABLE IF NOT EXISTS `%titems___forms` (
		  `form_id` int(10) unsigned NOT NULL auto_increment,
		  `form_name` varchar(255) NOT NULL default '' COMMENT 'indexovy nazov formularu',
		  PRIMARY KEY  (`form_id`)
		) ENGINE=InnoDB;
	";
	if(!db_public_gClass::execute_gFunc($sql_pVar, __FILE__, __LINE__)) {
		return(false);
	}

	$sql_pVar = "
		CREATE TABLE IF NOT EXISTS `%titems___form_rules` (
		  `form_rule_id` int(10) unsigned NOT NULL auto_increment,
		  `form_id` int(10) NOT NULL COMMENT 'id formularu, pre ktore plati toto pravidlo',
		  `field_name` varchar(255) default NULL COMMENT 'Nazov fieldu, ktoreho sa tyka toto pravidlo',
		  `field_access` enum('static', 'edit') COMMENT 'static, alebo edit. Ak chcem aby sa pole vobec nezobrazilo, zmazem pravidlo',
		  PRIMARY KEY  (`form_rule_id`)
		) ENGINE=InnoDB;
	";
	if(!db_public_gClass::execute_gFunc($sql_pVar, __FILE__, __LINE__)) {
		return(false);
	}

	$sql_pVar = "
		CREATE TABLE IF NOT EXISTS `%titems___values` (
		  `enum_id` int(10) unsigned NOT NULL auto_increment,
		  `enum_field_id` int(11) NOT NULL default '0' COMMENT 'id fieldu z tabulky _fields',
		  `enum_value_order` int(11) NOT NULL default '0' COMMENT 'radenie',
		  `enum_field_value` varchar(255) NOT NULL default '' COMMENT 'hodnota ktora sa zapise do tabulky _data',
		  `enum_field_name_item` varchar(255) NOT NULL default '' COMMENT 'nazov - jednotne cislo',
		  `enum_field_name_group` varchar(255) default '' COMMENT 'nazov - mnozne cislo',
		  `url_name` varchar(255) default NULL COMMENT 'url hodnota',
		  PRIMARY KEY  (`enum_id`)
		) ENGINE=InnoDB;
	";

	if(!db_public_gClass::execute_gFunc($sql_pVar, __FILE__, __LINE__)) {
		return(false);
	}

	stamp_gFunc('items', 'vytvorenie tabuliek items___');
}

if(!isStamp_gFunc('items', 'prvotna inicializacia tabuliek items___')) {

	$sql_pVar = "insert  into `%titems___data`(`status`,`name`,`languages`,`tree`,`tree_defs`,`forms`)
					values ('active','','sk','no','no','add_item,update_item')";
	if(!db_public_gClass::execute_gFunc($sql_pVar, __FILE__, __LINE__)) {
		return(false);
	}

	$sql_pVar = "insert  into `%titems___fields`(`field_id`,`name`,`tag`,`type`,`len`,`not_null`,`default_value`,`pattern`,`min_value`,`max_value`,`fieldset`,`field_order`,`comment`)
				values (1,'Názov','name','varchar',255,'yes',NULL,'/[a-z0-9_]{1,255}/i',1,255,1,1,'Label vo formulari');";
	if(!db_public_gClass::execute_gFunc($sql_pVar, __FILE__, __LINE__)) {
		return(false);
	}
	$sql_pVar = "insert  into `%titems___fields`(`field_id`,`name`,`tag`,`type`,`len`,`not_null`,`default_value`,`pattern`,`min_value`,`max_value`,`fieldset`,`field_order`,`comment`)
				values (2,'Jazyky','languages','set',null,'yes','sk','/[a-z]{2}/',2,2,1,2,'Zoznam jazykov pre itemy');";
	if(!db_public_gClass::execute_gFunc($sql_pVar, __FILE__, __LINE__)) {
		return(false);
	}
	$sql_pVar = "insert  into `%titems___fields`(`field_id`,`name`,`tag`,`type`,`len`,`not_null`,`default_value`,`pattern`,`min_value`,`max_value`,`fieldset`,`field_order`,`comment`)
				values (3,'Strom','tree','enum',NULL,'yes','no','/yes|no/',2,3,1,3,'ci sa ma generovat strom');";
	if(!db_public_gClass::execute_gFunc($sql_pVar, __FILE__, __LINE__)) {
		return(false);
	}
	$sql_pVar = "insert  into `%titems___fields`(`field_id`,`name`,`tag`,`type`,`len`,`not_null`,`default_value`,`pattern`,`min_value`,`max_value`,`fieldset`,`field_order`,`comment`)
				values (4,'Status','status','enum',NULL,'yes','active','/active/',6,6,1,4,'vzdy aktivny');";
	if(!db_public_gClass::execute_gFunc($sql_pVar, __FILE__, __LINE__)) {
		return(false);
	}
	$sql_pVar = "insert  into `%titems___fields`(`field_id`,`name`,`tag`,`type`,`len`,`not_null`,`default_value`,`pattern`,`min_value`,`max_value`,`fieldset`,`field_order`,`comment`)
				values (5,'Formuláre','forms','text',NULL,'yes','add_item,update_item','/[a-z_,]?/i',NULL,NULL,1,5,'zoznam dostupnych formularov');";
	if(!db_public_gClass::execute_gFunc($sql_pVar, __FILE__, __LINE__)) {
		return(false);
	}
	$sql_pVar = "insert  into `%titems___fields`(`field_id`,`name`,`tag`,`type`,`len`,`not_null`,`default_value`,`pattern`,`min_value`,`max_value`,`fieldset`,`field_order`,`comment`)
				values (6,'Stromové štruktúry','tree_defs','enum',NULL,'yes','no','/yes|no/',2,3,1,3,'ci sa maju generovat stromove struktury');";
	if(!db_public_gClass::execute_gFunc($sql_pVar, __FILE__, __LINE__)) {
		return(false);
	}
	$sql_pVar = "insert  into `%titems___fields`(`field_id`,`name`,`tag`,`type`,`len`,`not_null`,`default_value`,`pattern`,`min_value`,`max_value`,`fieldset`,`field_order`,`comment`)
				values (7,'Moduly','modules','text',NULL,'no',NULL,'',NULL,NULL,1,3,'Zoznam modulov, ktore sa maju nahrat.');";
	if(!db_public_gClass::execute_gFunc($sql_pVar, __FILE__, __LINE__)) {
		return(false);
	}
	$sql_pVar = "insert  into `%titems___fields`(`field_id`,`name`,`tag`,`type`,`len`,`not_null`,`default_value`,`pattern`,`min_value`,`max_value`,`fieldset`,`field_order`,`comment`)
				values (8,'Garant','garant','enum',NULL,'no','no','',NULL,NULL,1,4,'ak je yes, tak su pristupne funkcie garantovania dat.');";
	if(!db_public_gClass::execute_gFunc($sql_pVar, __FILE__, __LINE__)) {
		return(false);
	}
	$sql_pVar = "insert  into `%titems___fieldsets`(`fieldset_id`,`fieldset_name`,`fieldset_legend`,`fieldset_order`)
				values (1,'main','Všeobecné nastavenia',1);";
	if(!db_public_gClass::execute_gFunc($sql_pVar, __FILE__, __LINE__)) {
		return(false);
	}
	$sql_pVar = "insert  into `%titems___values`(`enum_id`,`enum_field_id`,`enum_field_value`,`enum_field_name_item`,`enum_field_name_group`,`url_name`)
				values (1,2,'sk','sk','sk','sk')
					  ,(5,2,'en','en','en','en')
    				  ,(6,2,'cz','cz','cz','cz')
					  ,(7,2,'de','de','de','de')
	;";
	if(!db_public_gClass::execute_gFunc($sql_pVar, __FILE__, __LINE__)) {
		return(false);
	}
	$sql_pVar = "insert  into `%titems___values`(`enum_id`,`enum_field_id`,`enum_field_value`,`enum_field_name_item`,`enum_field_name_group`,`url_name`)
					values (2,3,'yes','yes','yes','yes');";
	if(!db_public_gClass::execute_gFunc($sql_pVar, __FILE__, __LINE__)) {
		return(false);
	}
	$sql_pVar = "insert  into `%titems___values`(`enum_id`,`enum_field_id`,`enum_field_value`,`enum_field_name_item`,`enum_field_name_group`,`url_name`)
					values (3,3,'no','no','no','no');";
	if(!db_public_gClass::execute_gFunc($sql_pVar, __FILE__, __LINE__)) {
		return(false);
	}
	$sql_pVar = "insert  into `%titems___values`(`enum_id`,`enum_field_id`,`enum_field_value`,`enum_field_name_item`,`enum_field_name_group`,`url_name`)
					values (4,4,'active','active','active','active');";
	if(!db_public_gClass::execute_gFunc($sql_pVar, __FILE__, __LINE__)) {
		return(false);
	}
	$sql_pVar = "insert  into `%titems___values`(`enum_id`,`enum_field_id`,`enum_field_value`,`enum_field_name_item`,`enum_field_name_group`,`url_name`)
					values (8,6,'yes','yes','yes','yes');";
	if(!db_public_gClass::execute_gFunc($sql_pVar, __FILE__, __LINE__)) {
		return(false);
	}
	$sql_pVar = "insert  into `%titems___values`(`enum_id`,`enum_field_id`,`enum_field_value`,`enum_field_name_item`,`enum_field_name_group`,`url_name`)
					values (9,6,'no','no','no','no');";
	if(!db_public_gClass::execute_gFunc($sql_pVar, __FILE__, __LINE__)) {
		return(false);
	}
	
	$sql_pVar = "insert  into `%titems___values`(`enum_id`,`enum_field_id`,`enum_field_value`,`enum_field_name_item`,`enum_field_name_group`,`url_name`)
					values (10,8,'yes','yes','yes','yes');";
	if(!db_public_gClass::execute_gFunc($sql_pVar, __FILE__, __LINE__)) {
		return(false);
	}
	$sql_pVar = "insert  into `%titems___values`(`enum_id`,`enum_field_id`,`enum_field_value`,`enum_field_name_item`,`enum_field_name_group`,`url_name`)
					values (11,8,'no','no','no','no');";
	if(!db_public_gClass::execute_gFunc($sql_pVar, __FILE__, __LINE__)) {
		return(false);
	}
	
	db_items_gClass::applyLanguagesToTables_gFunc('');

	stamp_gFunc('items', 'prvotna inicializacia tabuliek items___');
}



return(true);
