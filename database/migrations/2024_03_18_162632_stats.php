<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('kega_items_test_questions__data', function (Blueprint $table) {
            $table->dateTime('time_last_stats')
                ->nullable()->default(null)
                ->index('idx_time_last_stats');
        });
        Schema::table('kega_items_test_answers__data', function (Blueprint $table) {
            $table->integer('obtiaznost')
                ->nullable()->default(null)
                ->index('idx_obtiaznost');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('kega_items_test_questions__data', function (Blueprint $table) {
            $table->dropColumn('time_last_stats');
        });
        Schema::table('kega_items_test_answers__data', function (Blueprint $table) {
            $table->dropColumn('obtiaznost');
        });
    }
};
