<?php

class test_question_last_comments_gClass extends table_gClass
{
    protected function initTable_gFunc()
    {
        $creator_id_pVar = 0;
        $owner_id_pVar = 0;
        if(isset($this->params['creator_id'])) {
            $creator_id_pVar = intval($this->params['creator_id']);
        }
        if(isset($this->params['owner_id'])) {
            $owner_id_pVar = intval($this->params['owner_id']);
        }

        if(isset($this->params['delete_id'])) {
            $comment_pVar = items_gClass::getItemComment_gFunc('test_questions', $this->params['delete_id']);
            if(is_array($comment_pVar)) {
                $user_id_pVar = session_gClass::getUserDetail_gFunc('user_id');
                $deleteAccess_pVar = false;
                if($comment_pVar['user_id'] == $user_id_pVar) {
                    if(session_gClass::userHasRightsAccess_gFunc(s_test_delete_owner_comment)) {
                        $deleteAccess_pVar = true;
                    }
                }
                if(!$deleteAccess_pVar && session_gClass::userHasRightsAccess_gFunc(s_test_delete_comment)) {
                    $deleteAccess_pVar = true;
                }

                if($deleteAccess_pVar) {;
                    items_gClass::deleteItemComment_gFunc('test_questions', $this->params['delete_id']);
                }
            }
            unset($this->params['delete_id']);
        }

        $order_by_pVar = false;
        if(isset($this->params['_order_by'])) {
            $order_by_pVar = $this->params['_order_by'];
        }

        if(session_gClass::userHasRightsAccessAction_gFunc(s_test_show_comments)) {
            $comments_pVar = items_gClass::getItemComments_gFunc('test_questions', 0, $creator_id_pVar, $owner_id_pVar, $order_by_pVar);
            $comments_pVar['_format_handlers'] = array('nick'=>array('test_question_last_comments','formatNick_gFunc'));
            $this->setData_gFunc($comments_pVar);
        }
        else {
            $this->setData_gFunc(array());
        }

        if(isset($this->params['columns'])) {
            $this->setColumnsFromString_gFunc($this->params['columns']);
        }
    }

    function formatNick_gFunc($value_pVar, $row_pVar)
    {
        $user_foto_pVar = session_gClass::getUserDetailStatic_gFunc($row_pVar['user_id']['value'], 'foto_thumb');
        $user_name_pVar = session_gClass::getUserDetailStatic_gFunc($row_pVar['user_id']['value'], 'real_name');
        $user_role_pVar = session_gClass::getUserDetailStatic_gFunc($row_pVar['user_id']['value'], 'user_role');
        $value_pVar = $user_name_pVar .'<br /><span style="color:#666666;">'.$user_role_pVar.'</span><br /><img src="/?doc='.$user_foto_pVar.'" />';
        return($value_pVar);
    }
}

class test_question_last_comments extends test_question_last_comments_gClass { }

