<?xml version="1.0" encoding="utf-8" ?>
<truEngine-document xmlns:te="http://www.truengine.sk">

<?php
if(!isset(@{select}) || !is_array(@{select})) {
	@{select} = array();
}
if(!isset(@{select:status})) {
	@{select:status} = false;
}
?>

<?php @{n} = 0; ?>
<te:for each="@{zoznam_literatury} as @{k_lit}=>@{literatura}">
	<hr te:if="@{n}" te:print="yes" />
	<div class="literaturalist" te:if="substr(@{k_lit},0,1) != '_'">
		<table cellpadding="0" cellspacing="0" style="width:100%">
			<tr>
				<td valign="top" style="width:130px;">
					<te:if test="is_array(@{literatura:image}) &amp;&amp; isset(@{literatura:image:0})">
						<a te:print="no" href="@{literatura:image:0:src}" rel="milkbox" target="_blank"><img src="@{literatura:image:0:th1_:src}" style="width:80px;" /></a>
						<img te:print="yes" src="@{literatura:image:0:th1_:src}" style="width:80px;" />
					</te:if>
				</td>
				<td valign="top">
					<div class="nav" te:print="no">
						<div te:if="@{select:status}" te:print="no">
							<br /><input type="checkbox" id="msel_literatura_@{literatura:item_id}" onclick="msel_check('literatura', @{literatura:item_id}, this.checked)" />
							<div style="clear:both;"></div>
						</div>
					</div>

					<table cellpadding="0" cellspacing="0">
						<tr>
							<td class="label"><te:string id="Názov" />:</td>
							<td>@{literatura:name}</td>
						</tr>
						<tr>
							<td class="label"><te:string id="Autor" />:</td>
							<td>@{literatura:autor}</td>
						</tr>
						<tr>
							<td class="label"><te:string id="Vydavateľstvo/rok" />:</td>
							<td>@{literatura:vydavatelstvo} / @{literatura:year}</td>
						</tr>
						<tr>
							<td class="label"><te:string id="Online" />:</td>
							<td>@{literatura:link}</td>
						</tr>
						<tr te:if="isset(@{literatura:strany})">
							<td class="label"><te:string id="Strany" />:</td>
							<td><?php @{literatura:strany} = string_gClass::interval(@{literatura:strany}); ?>@{literatura:strany}</td>
						</tr>
						<tr te:if="isset(@{literatura:pages})">
							<td class="label"><te:string id="Strany" />:</td>
							<td><?php @{literatura:pages} = string_gClass::interval(@{literatura:pages}); ?>@{literatura:pages}</td>
						</tr>

						<tr te:print="no">
							<td></td>
							<td>
								<a te:access="s_test_literatura_edit" href="/literatura/editovat-literaturu?item_id=@{literatura:item_id}"><te:string id="Editovať" /></a>
								<a href="/otazky/otazky?filter=literatura_md5%3DLIKE(%25@{literatura:qmd5}%25)"><te:string id="Otázky" /></a>
							</td>
						</tr>

					</table>

				</td>
			</tr>
		</table>

	</div>
	<?php @{n}++; ?>
</te:for>

</truEngine-document>
