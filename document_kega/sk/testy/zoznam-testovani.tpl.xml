<?xml version="1.0" encoding="utf-8" ?>
<truEngine-document xmlns:te="http://www.truengine.sk" te:access="s_test_list_instances">

<te:content-set name="title" value="@L{Zoznam testovaní}" />

<te:include name="/sk/components/filter" filter="@@{filter}" item_type="test_user_instances" doc="/@{doc}" />

<te:source name="test_instances" varname="instances" data_format="table" columns="time_first:@L{Čas spustenia},name:@L{Meno},score:@L{Počet bodov}" pager="20,@{request:page}" source_template_id="@{request:test_id}" filter="@{filter:settings}" _order_by="@{filter:sort}" />

<?php @{_order_by_fields} = array('name'=>'Meno', 'score'=>'Počet bodov', 'time_first'=>'Čas spustenia'); ?>
<te:include name="/sk/components/pager" pager="@{pager}" doc="@{doc}" params="@{filter:params}" _order_by="@{filter:sort}" order_fields="@{_order_by_fields}" extra_params="&amp;test_id=@{request:test_id}" />

<te:include name="/sk/components/table" table="@@{instances}" _order_by="@{request:zoradit}" />

<te:include name="/sk/components/pager" pager="@{pager}" doc="@{doc}" params="@{filter:params}" _order_by="@{filter:sort}" order_fields="@{_order_by_fields}" extra_params="&amp;test_id=@{request:test_id}" />

<te:content-set name="pdf_icon" value="@{REQUEST_URI}" />

</truEngine-document>
