<?php
if(isset($_SERVER['SCRIPT_FILENAME']) && strtolower($_SERVER['SCRIPT_FILENAME']) == strtolower(str_replace("\\",'/',__FILE__))) { header("HTTP/1.0 404 Not Found");  return(false); }
if(isset($GLOBALS['file__export_pdf_pVar'])) return(true);
$GLOBALS['file__export_pdf_pVar']=true;

if(!modules_gClass::isModuleRegistred_gFunc('export'))
{
    return(false);
}

if(!modules_gClass::isModuleRegistred_gFunc('pdf'))
{
    return(false);
}

class export_pdf_gClass extends export_gClass
{
	private $html_pVar;

	function __construct($settings_pVar)
	{
		parent::__construct($settings_pVar, false);
		$this->right_pVar = s_system_export_data_pdf;
		$this->html_pVar = '';
	}

	protected function export_open_gFunc()
	{
		$this->html_pVar .= 'data: ' . $this->settings_pVar['data_type'] . '<br />';
		$this->html_pVar .= 'timestamp: ' . $this->settings_pVar['timestamp'] . '<br />';
		$this->html_pVar .= 'language: ' . $this->settings_pVar['languages'] . '<br />';
		$this->html_pVar .= 'comment: ' . $this->comment_pVar . '<br />';
		$this->html_pVar .= '<hr />';
		return(true);
	}

	protected function export_close_gFunc()
	{
		$pdf_pVar = pdf_gClass::newPdf_gFunc();
        	/// vycistim HTML
        	$this->html_pVar = preg_replace('/<script[^>]*>[^<]*<\/script>/im', '', $this->html_pVar);
        	$this->html_pVar = str_replace("\t", ' ', $this->html_pVar);
        	$this->html_pVar = str_replace("\n", ' ', $this->html_pVar);
        	$this->html_pVar = str_replace("\r", ' ', $this->html_pVar);
        	while(strpos($this->html_pVar, '  ') !== false) {
	        	$this->html_pVar = str_replace('  ', ' ', $this->html_pVar);
        	}
        	$this->html_pVar = str_replace('> ', '>', $this->html_pVar);
        	$this->html_pVar = str_replace(' <', '<', $this->html_pVar);

			$this->html_pVar = str_replace('class="label_1"', 'style="width:50px;"', $this->html_pVar);
			$this->html_pVar = str_replace('class="label"', 'style="width:80px;"', $this->html_pVar);

        	$pdf_pVar->writeHTML_gFunc($this->html_pVar);

        	$pdf_pVar->output_gFunc('export.pdf');
		return(true);
	}

	protected function export_data_gFunc()
	{
		$data_pVar = $this->getData_gFunc();
		//echo '<pre>'; print_r($data_pVar); echo '</pre>';exit;
		$fields_pVar = implode(',', array_keys($data_pVar['_fields']));

		$this->writePdf_gFunc($data_pVar);
		return(true);
	}

	private function writePdf_gFunc(&$data_pVar, $nTabs_pVar = 0, $addPrefix_pVar = '')
	{
		if($nTabs_pVar === 0) {
			$prefixValue_pVar = 0;
		}
		elseif($nTabs_pVar === 1) {
			$prefixValue_pVar = ord('A') - 1;
		}
		elseif($nTabs_pVar === 2) {
			$prefixValue_pVar = '-';
		}
		else {
			$prefixValue_pVar = '*';
		}

		foreach($data_pVar as $k_pVar=>$v_pVar) {
			if(substr($k_pVar, 0, 1) === '_') {
				continue;
			}

			if($nTabs_pVar === 0 || $nTabs_pVar === 1) {
				$prefixValue_pVar++;
			}
			$prefix_tab_pVar = str_repeat(TAB, $nTabs_pVar);
			if($nTabs_pVar === 0) {
				$prefix_pVar = sprintf('%d. ' ,$prefixValue_pVar);
			}
			elseif($nTabs_pVar === 1) {
				$prefix_pVar = chr($prefixValue_pVar) . ') ';
			}
			else {
				$prefix_pVar = $prefixValue_pVar . ' ';
			}

			if($nTabs_pVar === 0) {
				$this->html_pVar .= '<br />';
			}

			$nFields_pVar = count($v_pVar);
			foreach($v_pVar as $vv_pVar) {
				if(is_array($vv_pVar)) {
					$nFields_pVar--;
				}
			}
			$i_pVar = 0;
			$str_pVar = '';
			foreach($v_pVar as $kk_pVar=>$vv_pVar) {
				$str_pVar .= $prefix_tab_pVar;
				if(!$i_pVar) {
					$str_pVar .= $prefix_pVar;
				}
				else {
					$str_pVar .= str_repeat(' ', strlen($prefix_pVar));
				}
				if($nFields_pVar != 1) {
					//$str_pVar .= '(' . $kk_pVar . ') ';
				}
				if(!is_array($vv_pVar) || isset($vv_pVar['_TE:file_content'])) {
					if(is_array($vv_pVar)) {
						$this->html_pVar .= '<br />' . $str_pVar . '<img src="' . $vv_pVar['data'][0]['th1_']['src'] . '" />';
					}
					else {
						$vv_pVar = trim($vv_pVar);
						if(empty($vv_pVar)) {
							//$vv_pVar = '-';
							continue;
						}
						$str_pVar .= $vv_pVar;
						$this->html_pVar .= '<br />' . $str_pVar;
					}
				}
				else {
					$this->writePdf_gFunc($vv_pVar, $nTabs_pVar + 1, str_repeat(' ', strlen($prefix_pVar)));
				}
				$str_pVar = '';
				$i_pVar++;
			}


			if($nTabs_pVar === 0) {
//				$this->result_pVar->echo_gFunc(NL);
			}
		}
	}

}


return(true);
