@props([
    'name',
    'value',
    'type',
])

@php $value = (is_array($value) || $value === null) ? $value : array_map('trim', explode(',', $value)) @endphp
<select class="form-control" name="{{ $name }}[]" id="{{ $name }}" multiple="multiple">
    @if(!empty($value))
        @foreach($value as $val)
            <option value="{{ $val }}" selected>{{ $val }}</option>
        @endforeach
    @endif
</select>


<script type="text/javascript">
    initSelect2('#{{ $name }}', {
        'formTag': '{{ $form->getFormTag() }}',
        'fieldTag': '{{ $type }}',
        'field': '{{ $name }}',
        'formAction': 'get-tags'
    }, {
        tags: true,
        tokenSeparators: [','],
        minimumInputLength: 3
    });
</script>



