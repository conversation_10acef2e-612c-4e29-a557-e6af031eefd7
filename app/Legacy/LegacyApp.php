<?php

namespace App\Legacy;

$GLOBALS['scipt_start_time'] = microtime(true);
$GLOBALS['master_ini_dir'] = '../';
require_once('../truEngine/.lib/__loader.module.php');

class LegacyApp
{
    static private $headers = [];
    static private $httpStatus = 200;
    static private $oldJsEnabled = true;

    static private $isPdfExport = false;

    static private $legacyAppObject = null;
    static function bootstrap()
    {
        define('NL', "\n");
        define('LF', "\n");
        define('CR', "\r");
        define('TAB', "\t");
        define('PHP_OPEN', '<' . '?' . 'php ');
        define('PHP_CLOSE', ' ?' . '>');


        $app = self::getLegacyAppObject();
        $app->init();

        \session_gClass::getSessionObject_gFunc();

        /// todo: povolit middleware VerifyCsrfToken
    }

    static function getLegacyAppObject()
    {
        if (self::$legacyAppObject == null) {
            self::$legacyAppObject = \main_gClass::getObject_gFunc();
        }
        return self::$legacyAppObject;
    }

    static function setHeader($header, $value)
    {
        self::$headers[$header] = $value;
    }

    static function setPdfExport()
    {
        self::$isPdfExport = true;
    }

    static function isPdfExport()
    {
        return self::$isPdfExport;
    }

    static function setHttpStatus($status)
    {
        self::$httpStatus = $status;
    }

    static function getHeaders()
    {
        return self::$headers;
    }

    static function getHttpStatus()
    {
        return self::$httpStatus;
    }

    /**
     * @param $componentClassName
     * @param $props
     * @return mixed
     *
     * do viewu komponenty je potrebne poslat attributes
     * return view('components.homepage-text',
     * ['attributes' => $this->attributes]);
     *
     * a parametre standardne do konstruktora:
     * public string $edit;
     * public function __construct(string $edit = '0')
     *
     */
    static function renderLaravelComponent($componentClassName, $props = [])
    {
        $c = new $componentClassName(...$props);
        $c->withAttributes($props);
        return $c->render();
    }

    static function renderLivewireComponent($componentName, $props = [], $slots = [], $vars = [])
    {
        if(!isset($props['formClass']) && isset($props['formclass'])) {
            $props['formClass'] = $props['formclass'];
        }
        unset($props['formclass']);
        if(isset($props['config']) && is_string($props['config'])) {
            $props['config'] = json_decode(str_replace('\'', '"', $props['config']), true);
        }
        return app('livewire')->mount($componentName, $props, 'key-' . uniqid(), $slots, $vars);
    }

    static function oldJsEnabled()
    {
        return self::$oldJsEnabled;
    }

    static function disableOldJs()
    {
        self::$oldJsEnabled = false;
    }

    static function str($str)
    {
        return \string_gClass::getString_gFunc($str, \main_gClass::getLanguage_gFunc());
    }

    static function includeLegacyTemplate($templateName, $params = [])
    {
        ob_start();
        $docName_pVar = $templateName;
        if($docName_pVar[0] !== '/') {
            // vytvorim absolutnu cestu
            $path_pVar = callStack_gClass::getDocPath_gFunc();
            $path_pVar .= $docName_pVar;
            $docName_pVar = $path_pVar;
        }

        \callStack_gClass::includePrepare_gFunc();


       //  $includeVars_pVar = array(\'doc\'=>vars::$vars[\'doc\'], \'REQUEST_URI\'=>vars::$vars[\'REQUEST_URI\']);';
        $includeVars_pVar = [];
        foreach ($params as $k_pVar=>$v_pVar) {
            $includeVars_pVar[$k_pVar] = $v_pVar;
        }


        $includeVars_pVar['requested_doc'] = self::getRequestedDoc();
        $includeVars_pVar['doc'] = self::getRequestedDoc();

        \callStack_gClass::includeEnd_gFunc($docName_pVar, $includeVars_pVar);
        return ob_get_clean();
    }

    static function getRequestedDoc()
    {
        return request()->input('doc');
    }


    static function authSession()
    {
        $sessionId_pVar = request()->session()->getId();
        $userId = auth()->user()->item_id;
        \log_gClass::write_gFunc('SESSION_USER_ID', $userId);
        \log_gClass::write_gFunc('LOGGED_IN', $userId, false, true);
        \Illuminate\Support\Facades\DB::table('kega_users_onlineusers')->where('session_str_id', $sessionId_pVar)->delete();
        \db_session_gClass::createSessionOnlineusers_gFunc($userId, $sessionId_pVar, false);
    }
}


