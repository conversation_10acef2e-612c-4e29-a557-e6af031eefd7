<?php
namespace App\Opus\Stats;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;

class CountStats
{
    static function countProcessTime()
    {
        return sprintf("%0.03f", ((microtime(true) - LARAVEL_START)));
    }

    static function count()
    {
        $processedCount = 0;
        $output = '';
        $database = [];

//        DB::table('kega_tests_running__questions')
//            ->whereNotNull('processed')
//            ->update([
//                'processed' => null,
//            ]);
//        dd(__LINE__);

        $tests = DB::table('kega_tests_running')
            ->whereNull('processed')
            ->where(function ($query) {
                $query->where('ignored', '<>', 1)
                    ->orWhereNull('ignored');
            })
            ->where('status', '=', 'closed')
            ->select('id', 'user_id', 'time_create', 'time_first', 'official')
            ->limit(100)
            ->get();

        foreach($tests as $test) {
            $questions = DB::table('kega_tests_running__questions')
                ->where('running_test_id', '=', $test->id)
                ->whereNull('processed')
                ->where(function ($query) {
                    $query->where('ignored', '<>', 1)
                        ->orWhereNull('ignored');
                })
                ->get();
            foreach ($questions as $question) {
                if($question->ignored) {
                    continue;
                }
                if($question->processed) {
                    continue;
                }
                $answerResults = explode('/', $question->answer_results);
                if(isset($answerResults[0])) {
                    $answerResults[0] = explode(',', $answerResults[0]);
                }
                if(isset($answerResults[1])) {
                    $answerResults[1] = explode(',', $answerResults[1]);
                }
                $dbAnswersIds = explode(',', $question->db_answer_ids);

                if($question->answer_results === null
                    || count($answerResults) != 2
                    || !is_array($answerResults[0])
                    || !is_array($answerResults[1])
                    || count($answerResults[0]) != count($answerResults[1])
                    || count($answerResults[0]) != count($dbAnswersIds)
                    || !$test->user_id
                ) {
                    DB::table('kega_tests_running__questions')
                        ->where('running_test_id', '=', $question->running_test_id)
                        ->where('db_question_id', '=', $question->db_question_id)
                        ->update([
                            'processed' => 0,
                            'ignored' => 1
                        ]);
                    continue;
                }

                $doneQuestions = [];
                $date = date('Y-m-d', strtotime($test->time_create));
                if(!empty($test->time_first)) {
                    $date = date('Y-m-d', strtotime($test->time_first));
                }
                $year = date('Y', strtotime($date));

                foreach($dbAnswersIds as $k=>$dbAnswersId) {
                    foreach ([$test->user_id, null] as $userId) {
                        foreach ([$date, null] as $tDate) {
                            foreach ([$year, null] as $tYear) {
                                if(!$tYear && $tDate) {
                                    continue;
                                }

                                // nelogujem datumove statistiky pre starsie ako 1 rok
                                if ($tDate && strtotime($tDate) < time() - 60 * 60 * 24 * 365) {
                                    ////continue;
                                }

                                // nelogujem user statistiky pre starsie ako 1 rok
                                if ($date && strtotime($date) < time() - 60 * 60 * 24 * 365) {
                                    if ($userId) {
                                        /////continue;
                                    }
                                }


                                $key = $question->db_question_id . '-' . $dbAnswersId . '-' . $userId . '-' . $tYear . '-' . $tDate;
                                $ok = $answerResults[1][$k] == 'S' ? 1 : 0;
                                $fail = $answerResults[1][$k] == 'N' ? 1 : 0;
                                $ok_test = ($test->official === 'yes' && $answerResults[1][$k] == 'S') ? 1 : 0;
                                $fail_test = ($test->official === 'yes' && $answerResults[1][$k] == 'N') ? 1 : 0;
                                if (isset($database[$key])) {
                                    $database[$key]['count_total']++;
                                    $database[$key]['count_ok_total'] += $ok;
                                    $database[$key]['count_fail_total'] += $fail;
                                    $database[$key]['score_total'] = ($database[$key]['count_ok_total'] + $database[$key]['count_fail_total']) ?
                                        (($database[$key]['count_ok_total'] / ($database[$key]['count_ok_total'] + $database[$key]['count_fail_total'])) * 100) : 0;

                                    $database[$key]['count_test'] += $test->official === 'yes' ? 1 : 0;
                                    $database[$key]['count_ok_test'] += $ok_test;
                                    $database[$key]['count_fail_test'] += $fail_test;
                                    $database[$key]['score_test'] = ($database[$key]['count_ok_test'] + $database[$key]['count_fail_test']) ? (($database[$key]['count_ok_test'] / ($database[$key]['count_ok_test'] + $database[$key]['count_fail_test'])) * 100) : 0;
                                } else {
                                    $database[$key] = [
                                        'question_id' => $question->db_question_id,
                                        'answer_id' => $dbAnswersId,
                                        'user_id' => $userId,
                                        'year' => $tYear,
                                        'date' => $tDate,
                                        'count_total' => 1,
                                        'count_ok_total' => $ok,
                                        'count_fail_total' => $fail,
                                        'score_total' => ($ok + $fail) ? (($ok / ($ok + $fail)) * 100) : 0,
                                        'count_test' => $test->official === 'yes' ? 1 : 0,
                                        'count_ok_test' => $ok_test,
                                        'count_fail_test' => $fail_test,
                                        'score_test' => ($ok_test + $fail_test) ? (($ok_test / ($ok_test + $fail_test)) * 100) : 0,
                                    ];
                                }
                            }
                        }
                    }
                    $doneQuestions[] = $question->db_question_id;
                }
                DB::table('kega_tests_running__questions')
                    ->where('running_test_id', '=', $question->running_test_id)
                    ->where('db_question_id', '=', $question->db_question_id)
                    ->update([
                        'processed' => 1,
                        'ignored' => 0
                    ]);
            }
            DB::table('kega_tests_running')
                ->where('id', '=', $test->id)
                ->update([
                    'processed' => 1,
                    'ignored' => 0
                ]);
            $processedCount++;
        }

        foreach($database as $data) {
            $i = DB::table('kega_stats')
                ->where('question_id', '=', $data['question_id'])
                ->where('answer_id', '=', $data['answer_id'])
                ->where('user_id', '=', $data['user_id'])
                ->where('year', '=', $data['year'])
                ->where('date', '=', $data['date'])
                ->first();
            if($i) {
                DB::table('kega_stats')
                    ->where('question_id', '=', $data['question_id'])
                    ->where('answer_id', '=', $data['answer_id'])
                    ->where('user_id', '=', $data['user_id'])
                    ->where('year', '=', $data['year'])
                    ->where('date', '=', $data['date'])
                    ->update([
                        'count_total' => $i->count_total + $data['count_total'],
                        'count_ok_total' => $i->count_ok_total + $data['count_ok_total'],
                        'count_fail_total' => $i->count_fail_total + $data['count_fail_total'],
                        'score_total' => ($i->count_total + $data['count_total']) ? ((($i->count_ok_total + $data['count_ok_total']) / ($i->count_total + $data['count_total'])) *100) : 0,

                        'count_test' => $i->count_test + $data['count_test'],
                        'count_ok_test' => $i->count_ok_test + $data['count_ok_test'],
                        'count_fail_test' => $i->count_fail_test + $data['count_fail_test'],
                        'score_test' => ($i->count_test + $data['count_test']) ? ((($i->count_ok_test + $data['count_ok_test']) / ($i->count_test + $data['count_test'])) *100) : 0,
                    ]);
            }
            else {
                DB::table('kega_stats')
                    ->insert([
                        'question_id' => $data['question_id'],
                        'answer_id' => $data['answer_id'],
                        'user_id' => $data['user_id'],
                        'year' => $data['year'],
                        'date' => $data['date'],
                        'count_total' => $data['count_total'],
                        'count_ok_total' => $data['count_ok_total'],
                        'count_fail_total' => $data['count_fail_total'],
                        'score_total' => ($data['count_ok_total'] + $data['count_fail_total']) ? (($data['count_ok_total'] / ($data['count_ok_total'] + $data['count_fail_total'])) *100) : 0,
                        'count_test' => $data['count_test'],
                        'count_ok_test' => $data['count_ok_test'],
                        'count_fail_test' => $data['count_fail_test'],
                        'score_test' => ($data['count_ok_test'] + $data['count_fail_test']) ? (($data['count_ok_test'] / ($data['count_ok_test'] + $data['count_fail_test'])) *100) : 0,
                    ]);
            }
        }

        $toProcess = DB::table('kega_tests_running')
            ->whereNull('processed')
            ->where(function ($query) {
                $query->where('ignored', '<>', 1)
                    ->orWhereNull('ignored');
            })
            ->where('status', '=', 'closed')
            ->count();

        if($processedCount) {
            $output .= date('Y-m-d H:i:s')
                . ' - (' . self::countProcessTime() . ' s)'
                . ' - ' . count($tests) . ' tests processed.'
                . ' ' . count($database) . ' records updated.'
                . ' ' . $toProcess . ' tests to process.' . PHP_EOL;

            $log = Storage::disk('logs')->path('opus-count-stats.log');
            file_put_contents($log, $output, FILE_APPEND);
        }

        return $processedCount;
    }

    static function countQuestionStats()
    {
        $processedCount = 0;

        $question_ids = DB::table('kega_items_test_questions__data')
            ->orderBy('time_last_stats', 'asc')
            ->where(function ($query) {
                $query->where('time_last_stats', '<', date('Y-m-d H:i:s', strtotime('-1 day')))
                    ->orWhereNull('time_last_stats');
            })
            ->select('item_id')
            ->limit(1000)
            ->pluck('item_id');

        foreach ($question_ids as $question_id) {
            $processedCount++;
            self::recountQuestionStats($question_id);

            DB::table('kega_items_test_questions__data')
                ->where('item_id', '=', $question_id)
                ->update([
                    'time_last_stats' => date('Y-m-d H:i:s')
                ]);
        }


        if($processedCount) {
            $toProcess = DB::table('kega_items_test_questions__data')
                ->where(function ($query) {
                    $query->where('time_last_stats', '<', date('Y-m-d H:i:s', strtotime('-1 day')))
                        ->orWhereNull('time_last_stats');
                })
                ->count();

            $output = date('Y-m-d H:i:s')
                . ' - (' . self::countProcessTime() . ' s)'
                . ' - ' . $processedCount . ' questions processed.'
                . ' ' . $toProcess . ' questions to process.' . PHP_EOL;

            $log = Storage::disk('logs')->path('opus-count-stats.log');
            file_put_contents($log, $output, FILE_APPEND);
        }

        return $processedCount;
    }



    private static function recountQuestionStats($question_id)
    {
        $stats = DB::table('kega_stats')
            ->where('question_id', '=', $question_id)
            ->whereNull('user_id')
            ->whereNull('year')
            ->whereNull('date')
            ->distinct('answer_id')
            ->get();
        $answer_ids = [];
        foreach ($stats as $stat) {
            $answer_ids[] = $stat->answer_id;
        }

        $answer_ids = DB::table('kega_items_test_answers__data')
            ->where('test_question', '=', $question_id)
            ->whereIn('item_id', $answer_ids)
            ->pluck('item_id')->toArray();

        $questionData = [
            'spravne' => 0,
            'nespravne' => 0,
            'spravne_test' => 0,
            'nespravne_test' => 0,
            'obtiaznost' => 0.0
        ];
        foreach ($stats as $stat) {
            if(!in_array($stat->answer_id, $answer_ids)) {
                continue;
            }

            $answerData = [
                'spravne' => $stat->count_ok_total,
                'nespravne' => $stat->count_fail_total,
                'spravne_test' => $stat->count_ok_test,
                'nespravne_test' => $stat->count_fail_test,
            ];
            $answerData['obtiaznost'] = ($answerData['spravne'] + $answerData['nespravne']) ? (round($answerData['nespravne'] / ($answerData['spravne'] + $answerData['nespravne']), 2) * 100) : 0;

            DB::table('kega_items_test_answers__data')
                ->where('item_id', '=', $stat->answer_id)
                ->update($answerData);

            $questionData['spravne'] += $stat->count_ok_total;
            $questionData['nespravne'] += $stat->count_fail_total;
            $questionData['spravne_test'] += $stat->count_ok_test;
            $questionData['nespravne_test'] += $stat->count_fail_test;
        }
        $questionData['obtiaznost'] = ($questionData['spravne'] + $questionData['nespravne']) ? (round($questionData['nespravne'] / ($questionData['spravne'] + $questionData['nespravne']), 2) * 100) : 0;

        DB::table('kega_items_test_questions__data')
            ->where('item_id', '=', $question_id)
            ->update($questionData);
    }

}
