/* IE9 Resets and Normalization */
article,
aside,
details,
figcaption,
figure,
footer,
header,
hgroup,
main,
nav,
section,
summary {
	display: block;
}

audio,
canvas,
progress,
video {
	display: inline-block;
}

[hidden],
template {
	display: none;
}

abbr[title] {
	border-bottom: 1px dotted;
}

img {
	border: 0;
}

svg:not(:root) {
	overflow: hidden;
}

figure {
	margin: 1em 40px;
}

button {
	overflow: visible;
}

input[type="checkbox"],
input[type="radio"] {
	box-sizing: border-box;
	padding: 0;
}

legend {
	border: 0;
	padding: 0;
}

textarea {
	overflow: auto;
}