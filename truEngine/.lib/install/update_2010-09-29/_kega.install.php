<?php
if(!isStamp_gFunc('kega', 'kega update 2010-09-29')) {
	$sql_pVar = 'ALTER TABLE `%ttest_hall_of_fame`     ADD COLUMN `total_score` INT(11) NULL AFTER `archive_date`';
	if(!db_public_gClass::execute_gFunc($sql_pVar, __FILE__, __LINE__)) {
		return(false);
	}
	stamp_gFunc('kega', 'kega update 2010-09-29');
}

if(!isStamp_gFunc('kega', 'kega update-2010-10-01')) {
	$sql_pVar = 'update %titems_test_questions__fields set sk_field_info = \'::Ak tvoríte otázku z novej knihy, ktorú opus ešte nepozna, odporúčame Vam ju najprv vytvorit= http://www.sapienti.ae/sk/literatura/nova\' where tag = \'literatura\'';
	if(!db_public_gClass::execute_gFunc($sql_pVar, __FILE__, __LINE__)) {
		return(false);
	}
	$sql_pVar = 'update %titems_test_answers__fields set sk_default_value =\'nespravne\',cz_default_value =\'nespravne\',en_default_value =\'nespravne\' where tag=\'spravnost\'';
	if(!db_public_gClass::execute_gFunc($sql_pVar, __FILE__, __LINE__)) {
		return(false);
	}

	stamp_gFunc('kega', 'kega update-2010-10-01');
}

if(!isStamp_gFunc('kega', 'kega update-2010-10-07')) {
	$sql_pVar = 'update %titems_test_questions__fields set sk_field_info = \'::Vysvetlenie je nepovinný doplnok, ktorý slúži ako komentár/poznámka pre študentov, ktorá sa počas testovania nezobrazuje,<br />ale pri listovaní otázkami ponúka širšie objasnenie otázky prípadne odhaľuje správnu informáciu alebo odhaľuje chyták v otázke,<br />aby sa predišlo opakovaným diskusiám k danej otázke.\' where tag = \'vysvetlenie\'';
	if(!db_public_gClass::execute_gFunc($sql_pVar, __FILE__, __LINE__)) {
		return(false);
	}
	$sql_pVar = 'update %titems_test_answers__fields set sk_field_info = \'::Vysvetlenie je nepovinný doplnok, ktorý slúži ako komentár/poznámka pre študentov, ktorá sa počas testovania nezobrazuje,<br />ale pri listovaní otázkami ponúka širšie objasnenie odpovede prípadne odhaľuje správnu informáciu alebo odhaľuje chyták v odpovedi,<br />aby sa predišlo opakovaným diskusiám k danej otázke.\' where tag = \'odpoved_vysvetlenie\'';
	if(!db_public_gClass::execute_gFunc($sql_pVar, __FILE__, __LINE__)) {
		return(false);
	}
	stamp_gFunc('kega', 'kega update-2010-10-07');
}


if(!isStamp_gFunc('kega', 'kega update-2010-10-12')) {
	$items_pVar = new install_item_gClass('test_templates', false);
	$items_pVar->addField_gFunc('vyhodnotenie', 'excelentne', 'xvarchar', 'Kritérium pre excelentné absolvovanie', array('len'=>255, 'sk_field_info'=>'::Podľa pravidiel prednostu test garantuje potrebné minimum vedomostí pre absolvovanie celej skúšky a prípadná ústna skúška rozhodne o známke A-E, ale už nie Fx'));
	$items_pVar->setFormRule_gFunc('edit_item', 'excelentne', 'edit');
	$items_pVar->setFormRule_gFunc('add_item', 'excelentne', 'edit');
	$items_pVar->apply_gFunc();

	stamp_gFunc('kega', 'kega update-2010-10-12');
}

if(!isStamp_gFunc('kega', 'kega update-2010-10-12 2')) {
	$sql_pVar = 'ALTER TABLE `%ttests_running`     CHANGE `status` `status` ENUM(\'waiting\',\'running\',\'closed\',\'deleted\') DEFAULT \'waiting\' NOT NULL';
	if(!db_public_gClass::execute_gFunc($sql_pVar, __FILE__, __LINE__)) {
		return(false);
	}

	stamp_gFunc('kega', 'kega update-2010-10-12 2');
}


return(true);
