<?php

class additem_gClass extends form_gClass
{
    protected $itemType_pVar;
    protected $formType_pVar;
    protected $item_id_pVar;
    protected $forcedData_pVar;

    function __construct($action_pVar = 'get')
    {
        parent::__construct($action_pVar);
        $this->itemType_pVar = false;
        $this->formType_pVar = false;
        $this->forcedData_pVar  = array();
    }

    protected function initForm_gFunc($multiedit_pVar = false, $initFormRef_pVar = true)
    {
        parent::initForm_gFunc($multiedit_pVar);
        // musim ziskat data
        $data_pVar = array();
        if(isset($this->params) && count($this->params)) {
            $data_pVar = $this->params;
        }
        $this->form_init_pVar = true;

        if(!isset($data_pVar['itemtype'])) {
            return(false);
        }
        $this->itemType_pVar = $data_pVar['itemtype'];
        unset($data_pVar['itemtype']);

        $setDefaultStatus_pVar = false;
        if(isset($data_pVar['default_status'])) {
            if(!isset($data_pVar['item_id']) || !$data_pVar['item_id']) {
                $setDefaultStatus_pVar = $data_pVar['default_status'];
            }
            unset($data_pVar['default_status']);
        }

        // todo - skontrolovat ci existuje taky typ itemov.

        $item_id_pVar = 0;
        $isItemId_pVar = false;
        if(isset($data_pVar['item_id']) && $data_pVar['item_id']) {
            $isItemId_pVar = true;
            $item_id_pVar = $data_pVar['item_id'];
        }

        if($this->isData_gFunc()) {
            if(main_gClass::getInputInteger_gFunc($this->itemType_pVar . '_item_id', main_gClass::SRC_REQUEST_pVar)) {
                $isItemId_pVar = true;
            }
        }

        if($isItemId_pVar) {
            if(isset($data_pVar['formtype-edit'])) {
                $this->formType_pVar = $data_pVar['formtype-edit'];
            }
            elseif(isset($data_pVar['formtype'])) {
                $this->formType_pVar = $data_pVar['formtype'];
            }
            else {
                $this->formType_pVar = 'edit_item';
            }

            if($this->getMultieditLevel_gFunc() !== false) {
                $this->addHiddenField_gFunc($this->itemType_pVar . '_active_formpart', 1, '/1|0/');
            }
        }
        else {
            if(isset($data_pVar['formtype-add'])) {
                $this->formType_pVar = $data_pVar['formtype-add'];
            }
            elseif(isset($data_pVar['formtype'])) {
                $this->formType_pVar = $data_pVar['formtype'];
            }
            else {
                $this->formType_pVar = 'add_item';
            }
            if($this->getMultieditLevel_gFunc() !== false) {
                if($this->getMultieditLevel_gFunc() === 1) { // prva polozka musi byt vzdy aktivna...
                    $this->addHiddenField_gFunc($this->itemType_pVar . '_active_formpart', 1, '/1|0/');
                }
                else {
                    $this->addHiddenField_gFunc($this->itemType_pVar . '_active_formpart', 0, '/1|0/');
                }
            }
        }

        $this->setVar_gFunc('itemtype', $this->itemType_pVar, false);

        unset($data_pVar['formtype']);
        unset($data_pVar['formtype-edit']);
        unset($data_pVar['formtype-add']);

        if($initFormRef_pVar) {
            items_gClass::initFormRef_gFunc($this->itemType_pVar, $this, $data_pVar, false, $this->formType_pVar);
        }
        else {
            $this->addHiddenField_gFunc($this->itemType_pVar . '_' . 'item_id', $item_id_pVar, '/[0-9]+/');
            $this->item_id_pVar = $this->getFieldValue_gFunc($this->itemType_pVar . '_' . 'item_id');
            if($isItemId_pVar) {
                if(isset($data_pVar['submit_button_title_update'])) {
                    $this->setVar_gFunc('submit_button_title', $data_pVar['submit_button_title_update'], false);
                }
                else {
                    $this->setVar_gFunc('submit_button_title', string_gClass::get('str_forms_submit_button_title_pVar', false));
                }
            }
            else {
                if(isset($data_pVar['submit_button_title_add'])) {
                    $this->setVar_gFunc('submit_button_title', $data_pVar['submit_button_title_add'], false);
                }
                else {
                    $this->setVar_gFunc('submit_button_title', string_gClass::get('str_forms_submit_button_title_pVar'), false);
                }
            }
        }
        if($setDefaultStatus_pVar !== false) {
            if($this->setFieldDefaultValue_gFunc($this->itemType_pVar . '_status', $setDefaultStatus_pVar) === false) {
                $this->setHiddenFieldDefaultValue_gFunc($this->itemType_pVar . '_status', $setDefaultStatus_pVar);
            }
        }

        $this->setFieldsetPrefix_gFunc('');
        $this->setFieldPrefix_gFunc('');

        return(true);
    }

    protected function getData()
    {
        $data_pVar = $this->getFormData_gFunc();
        if($data_pVar['error_code'] !== self::RESULT_OK_pVar) {
            return($data_pVar);
        }

        // ulozim item
        if($this->saveData_gFunc() === false) {
            $this->setError_gFunc('Chyba pri ukladaní dát.');
            $this->setVar_gFunc('save_data_error', 1, false);
        }
        else {
            $this->setVar_gFunc('save_data_error', 0, false);
        }

        $data_pVar = $this->getFormData_gFunc();

        return($data_pVar);
    }

    protected function saveData_gFunc()
    {
        items_gClass::editItemByForm_gFunc($this->itemType_pVar, $this, $this->forcedData_pVar);
    }
}

class additem extends additem_gClass {}

