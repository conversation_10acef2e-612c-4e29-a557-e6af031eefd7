<html>
<head>
<title>Package com-tecnick-tcpdf Element Index</title>
<link rel="stylesheet" type="text/css" href="media/style.css">
</head>
<body>

<table border="0" cellspacing="0" cellpadding="0" height="48" width="100%">
  <tr>
    <td class="header_top">com-tecnick-tcpdf</td>
  </tr>
  <tr><td class="header_line"><img src="media/empty.png" width="1" height="1" border="0" alt=""  /></td></tr>
  <tr>
    <td class="header_menu">
        
                                    
                              		  [ <a href="classtrees_com-tecnick-tcpdf.html" class="menu">class tree: com-tecnick-tcpdf</a> ]
		  [ <a href="elementindex_com-tecnick-tcpdf.html" class="menu">index: com-tecnick-tcpdf</a> ]
		  	    [ <a href="elementindex.html" class="menu">all elements</a> ]
    </td>
  </tr>
  <tr><td class="header_line"><img src="media/empty.png" width="1" height="1" border="0" alt=""  /></td></tr>
</table>

<table width="100%" border="0" cellpadding="0" cellspacing="0">
  <tr valign="top">
    <td width="200" class="menu">
      <b>Packages:</b><br />
              <a href="li_com-tecnick-tcpdf.html">com-tecnick-tcpdf</a><br />
            <br /><br />
                        <b>Files:</b><br />
      	  <div class="package">
			<a href="com-tecnick-tcpdf/_barcodes.php.html">		barcodes.php
		</a><br>
			<a href="com-tecnick-tcpdf/_htmlcolors.php.html">		htmlcolors.php
		</a><br>
			<a href="com-tecnick-tcpdf/_tcpdf.php.html">		tcpdf.php
		</a><br>
			<a href="com-tecnick-tcpdf/_config---tcpdf_config.php.html">		tcpdf_config.php
		</a><br>
			<a href="com-tecnick-tcpdf/_unicode_data.php.html">		unicode_data.php
		</a><br>
	  </div><br />
      
      
            <b>Classes:</b><br />
        <div class="package">
		    		<a href="com-tecnick-tcpdf/TCPDF.html">TCPDF</a><br />
	    		<a href="com-tecnick-tcpdf/TCPDFBarcode.html">TCPDFBarcode</a><br />
	  </div>
                </td>
    <td>
      <table cellpadding="10" cellspacing="0" width="100%" border="0"><tr><td valign="top">

<a name="top"></a>
<h1>Element index for package com-tecnick-tcpdf</h1>
	[ <a href="elementindex_com-tecnick-tcpdf.html#a">a</a> ]
	[ <a href="elementindex_com-tecnick-tcpdf.html#b">b</a> ]
	[ <a href="elementindex_com-tecnick-tcpdf.html#c">c</a> ]
	[ <a href="elementindex_com-tecnick-tcpdf.html#d">d</a> ]
	[ <a href="elementindex_com-tecnick-tcpdf.html#e">e</a> ]
	[ <a href="elementindex_com-tecnick-tcpdf.html#f">f</a> ]
	[ <a href="elementindex_com-tecnick-tcpdf.html#g">g</a> ]
	[ <a href="elementindex_com-tecnick-tcpdf.html#h">h</a> ]
	[ <a href="elementindex_com-tecnick-tcpdf.html#i">i</a> ]
	[ <a href="elementindex_com-tecnick-tcpdf.html#j">j</a> ]
	[ <a href="elementindex_com-tecnick-tcpdf.html#k">k</a> ]
	[ <a href="elementindex_com-tecnick-tcpdf.html#l">l</a> ]
	[ <a href="elementindex_com-tecnick-tcpdf.html#m">m</a> ]
	[ <a href="elementindex_com-tecnick-tcpdf.html#n">n</a> ]
	[ <a href="elementindex_com-tecnick-tcpdf.html#o">o</a> ]
	[ <a href="elementindex_com-tecnick-tcpdf.html#p">p</a> ]
	[ <a href="elementindex_com-tecnick-tcpdf.html#r">r</a> ]
	[ <a href="elementindex_com-tecnick-tcpdf.html#s">s</a> ]
	[ <a href="elementindex_com-tecnick-tcpdf.html#t">t</a> ]
	[ <a href="elementindex_com-tecnick-tcpdf.html#u">u</a> ]
	[ <a href="elementindex_com-tecnick-tcpdf.html#v">v</a> ]
	[ <a href="elementindex_com-tecnick-tcpdf.html#w">w</a> ]
	[ <a href="elementindex_com-tecnick-tcpdf.html#x">x</a> ]
	[ <a href="elementindex_com-tecnick-tcpdf.html#y">y</a> ]
	[ <a href="elementindex_com-tecnick-tcpdf.html#z">z</a> ]
	[ <a href="elementindex_com-tecnick-tcpdf.html#_">_</a> ]

  <hr />
	<a name="_"></a>
	<div>
		<h2>_</h2>
		<dl>
							<dt><b>_addfield</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#method_addfield">TCPDF::_addfield()</a></dd>
							<dt><b>_beginpage</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#method_beginpage">TCPDF::_beginpage()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Initialize a new page.</dd>
							<dt><b>_datastring</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#method_datastring">TCPDF::_datastring()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Format a date string for meta information</dd>
							<dt><b>_destroy</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#method_destroy">TCPDF::_destroy()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Unset all class variables except the following critical variables: internal_encoding, state, bufferlen, buffer and diskcache.</dd>
							<dt><b>_dochecks</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#method_dochecks">TCPDF::_dochecks()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Check for locale-related bug</dd>
							<dt><b>_dolinethrough</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#method_dolinethrough">TCPDF::_dolinethrough()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Line through text.</dd>
							<dt><b>_dounderline</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#method_dounderline">TCPDF::_dounderline()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Underline text.</dd>
							<dt><b>_enddoc</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#method_enddoc">TCPDF::_enddoc()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Output end of document (EOF).</dd>
							<dt><b>_endpage</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#method_endpage">TCPDF::_endpage()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Mark end of page.</dd>
							<dt><b>_escape</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#method_escape">TCPDF::_escape()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Add &quot;\&quot; before &quot;\&quot;, &quot;(&quot; and &quot;)&quot;</dd>
							<dt><b>_escapetext</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#method_escapetext">TCPDF::_escapetext()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Format a text string</dd>
							<dt><b>_freadint</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#method_freadint">TCPDF::_freadint()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Read a 4-byte integer from file.</dd>
							<dt><b>_generateencryptionkey</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#method_generateencryptionkey">TCPDF::_generateencryptionkey()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Compute encryption key</dd>
							<dt><b>_getfontpath</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#method_getfontpath">TCPDF::_getfontpath()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Return fonts path</dd>
							<dt><b>_JScolor</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#method_JScolor">TCPDF::_JScolor()</a></dd>
							<dt><b>_md5_16</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#method_md5_16">TCPDF::_md5_16()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Encrypts a string using MD5 and returns it's value as a binary string.</dd>
							<dt><b>_newobj</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#method_newobj">TCPDF::_newobj()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Begin a new object.</dd>
							<dt><b>_objectkey</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#method_objectkey">TCPDF::_objectkey()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Compute encryption key depending on object number where the encrypted data is stored</dd>
							<dt><b>_out</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#method_out">TCPDF::_out()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Output a string to the document.</dd>
							<dt><b>_outarc</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#method_outarc">TCPDF::_outarc()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Output an arc</dd>
							<dt><b>_outCurve</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#method_outCurve">TCPDF::_outCurve()</a></dd>
							<dt><b>_outLine</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#method_outLine">TCPDF::_outLine()</a></dd>
							<dt><b>_outPoint</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#method_outPoint">TCPDF::_outPoint()</a></dd>
							<dt><b>_outRect</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#method_outRect">TCPDF::_outRect()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Draws a rectangle.</dd>
							<dt><b>_Ovalue</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#method_Ovalue">TCPDF::_Ovalue()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Compute O value (used for RC4 encryption)</dd>
							<dt><b>_parsejpeg</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#method_parsejpeg">TCPDF::_parsejpeg()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Extract info from a JPEG file without using the GD library.</dd>
							<dt><b>_parsepng</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#method_parsepng">TCPDF::_parsepng()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Extract info from a PNG file without using the GD library.</dd>
							<dt><b>_putannots</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#method_putannots">TCPDF::_putannots()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Output Page Annotations.</dd>
							<dt><b>_putbookmarks</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#method_putbookmarks">TCPDF::_putbookmarks()</a></dd>
							<dt><b>_putcatalog</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#method_putcatalog">TCPDF::_putcatalog()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Output Catalog.</dd>
							<dt><b>_putcertification</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#method_putcertification">TCPDF::_putcertification()</a></dd>
							<dt><b>_putcidfont0</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#method_putcidfont0">TCPDF::_putcidfont0()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Output CID-0 fonts.</dd>
							<dt><b>_putEmbeddedFiles</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#method_putEmbeddedFiles">TCPDF::_putEmbeddedFiles()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Embedd the attached files.</dd>
							<dt><b>_putencryption</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#method_putencryption">TCPDF::_putencryption()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Put encryption on PDF document.</dd>
							<dt><b>_putextgstates</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#method_putextgstates">TCPDF::_putextgstates()</a></dd>
							<dt><b>_putfonts</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#method_putfonts">TCPDF::_putfonts()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Output fonts.</dd>
							<dt><b>_putfontwidths</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#method_putfontwidths">TCPDF::_putfontwidths()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Outputs font widths</dd>
							<dt><b>_putheader</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#method_putheader">TCPDF::_putheader()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Output PDF header.</dd>
							<dt><b>_putimages</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#method_putimages">TCPDF::_putimages()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Output images.</dd>
							<dt><b>_putinfo</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#method_putinfo">TCPDF::_putinfo()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Adds some Metadata information</dd>
							<dt><b>_putjavascript</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#method_putjavascript">TCPDF::_putjavascript()</a></dd>
							<dt><b>_putocg</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#method_putocg">TCPDF::_putocg()</a></dd>
							<dt><b>_putpages</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#method_putpages">TCPDF::_putpages()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Output pages.</dd>
							<dt><b>_putresourcedict</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#method_putresourcedict">TCPDF::_putresourcedict()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Output Resources Dictionary.</dd>
							<dt><b>_putresources</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#method_putresources">TCPDF::_putresources()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Output Resources.</dd>
							<dt><b>_putshaders</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#method_putshaders">TCPDF::_putshaders()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Output shaders.</dd>
							<dt><b>_putspotcolors</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#method_putspotcolors">TCPDF::_putspotcolors()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Output Spot Colors Resources.</dd>
							<dt><b>_putstream</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#method_putstream">TCPDF::_putstream()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Output a stream.</dd>
							<dt><b>_puttrailer</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#method_puttrailer">TCPDF::_puttrailer()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Output trailer.</dd>
							<dt><b>_puttruetypeunicode</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#method_puttruetypeunicode">TCPDF::_puttruetypeunicode()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Adds unicode fonts.<br /></dd>
							<dt><b>_putuserrights</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#method_putuserrights">TCPDF::_putuserrights()</a></dd>
							<dt><b>_putviewerpreferences</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#method_putviewerpreferences">TCPDF::_putviewerpreferences()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Output viewer preferences.</dd>
							<dt><b>_putxobjectdict</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#method_putxobjectdict">TCPDF::_putxobjectdict()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Output object dictionary for images.</dd>
							<dt><b>_RC4</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#method_RC4">TCPDF::_RC4()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Returns the input text exrypted using RC4 algorithm and the specified key.</dd>
							<dt><b>_textstring</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#method_textstring">TCPDF::_textstring()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Format a text string for meta information</dd>
							<dt><b>_toJPEG</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#method_toJPEG">TCPDF::_toJPEG()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Convert the loaded php image to a JPEG and then return a structure for the PDF creator.</dd>
							<dt><b>_Uvalue</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#method_Uvalue">TCPDF::_Uvalue()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Compute U value (used for RC4 encryption)</dd>
							<dt><b>__construct</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#method__construct">TCPDF::__construct()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;This is the class constructor.</dd>
							<dt><b>__construct</b></dt>
				<dd>in file barcodes.php, method <a href="com-tecnick-tcpdf/TCPDFBarcode.html#method__construct">TCPDFBarcode::__construct()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;This is the class constructor.</dd>
							<dt><b>__destruct</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#method__destruct">TCPDF::__destruct()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Default destructor.</dd>
					</dl>
	</div>
	<a href="elementindex_com-tecnick-tcpdf.html#top">top</a><br>
  <hr />
	<a name="a"></a>
	<div>
		<h2>a</h2>
		<dl>
							<dt><b>$AliasNbPages</b></dt>
				<dd>in file tcpdf.php, variable <a href="com-tecnick-tcpdf/TCPDF.html#var$AliasNbPages">TCPDF::$AliasNbPages</a></dd>
							<dt><b>$AliasNumPage</b></dt>
				<dd>in file tcpdf.php, variable <a href="com-tecnick-tcpdf/TCPDF.html#var$AliasNumPage">TCPDF::$AliasNumPage</a></dd>
							<dt><b>$author</b></dt>
				<dd>in file tcpdf.php, variable <a href="com-tecnick-tcpdf/TCPDF.html#var$author">TCPDF::$author</a></dd>
							<dt><b>$AutoPageBreak</b></dt>
				<dd>in file tcpdf.php, variable <a href="com-tecnick-tcpdf/TCPDF.html#var$AutoPageBreak">TCPDF::$AutoPageBreak</a></dd>
							<dt><b>AcceptPageBreak</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodAcceptPageBreak">TCPDF::AcceptPageBreak()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Whenever a page break condition is met, the method is called, and the break is issued or not depending on the returned value.</dd>
							<dt><b>addExtGState</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodaddExtGState">TCPDF::addExtGState()</a></dd>
							<dt><b>AddFont</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodAddFont">TCPDF::AddFont()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Imports a TrueType, Type1, core, or CID0 font and makes it available.</dd>
							<dt><b>addHtmlLink</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodaddHtmlLink">TCPDF::addHtmlLink()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Output anchor link.</dd>
							<dt><b>addHTMLVertSpace</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodaddHTMLVertSpace">TCPDF::addHTMLVertSpace()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Add vertical spaces if needed.</dd>
							<dt><b>AddLink</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodAddLink">TCPDF::AddLink()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Creates a new internal link and returns its identifier. An internal link is a clickable area which directs to another place within the document.<br />  The identifier can then be passed to Cell(), Write(), Image() or Link(). The destination is defined with SetLink().</dd>
							<dt><b>AddPage</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodAddPage">TCPDF::AddPage()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Adds a new page to the document. If a page is already present, the Footer() method is called first to output the footer (if enabled). Then the page is added, the current position set to the top-left corner according to the left and top margins (or top-right if in RTL mode), and Header() is called to display the header (if enabled).</dd>
							<dt><b>AddSpotColor</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodAddSpotColor">TCPDF::AddSpotColor()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Defines a new spot color.</dd>
							<dt><b>addTOC</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodaddTOC">TCPDF::addTOC()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Output a Table of Content Index (TOC).</dd>
							<dt><b>AliasNbPages</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodAliasNbPages">TCPDF::AliasNbPages()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Defines an alias for the total number of pages.</dd>
							<dt><b>AliasNumPage</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodAliasNumPage">TCPDF::AliasNumPage()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Defines an alias for the page number.</dd>
							<dt><b>Annotation</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodAnnotation">TCPDF::Annotation()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Puts a markup annotation on a rectangular area of the page.</dd>
							<dt><b>arrUTF8ToUTF16BE</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodarrUTF8ToUTF16BE">TCPDF::arrUTF8ToUTF16BE()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Converts array of UTF-8 characters to UTF16-BE string.<br /></dd>
					</dl>
	</div>
	<a href="elementindex_com-tecnick-tcpdf.html#top">top</a><br>
  <hr />
	<a name="b"></a>
	<div>
		<h2>b</h2>
		<dl>
							<dt><b>$barcode</b></dt>
				<dd>in file tcpdf.php, variable <a href="com-tecnick-tcpdf/TCPDF.html#var$barcode">TCPDF::$barcode</a></dd>
							<dt><b>$barcode_array</b></dt>
				<dd>in file barcodes.php, variable <a href="com-tecnick-tcpdf/TCPDFBarcode.html#var$barcode_array">TCPDFBarcode::$barcode_array</a></dd>
							<dt><b>$bgcolor</b></dt>
				<dd>in file tcpdf.php, variable <a href="com-tecnick-tcpdf/TCPDF.html#var$bgcolor">TCPDF::$bgcolor</a></dd>
							<dt><b>$bMargin</b></dt>
				<dd>in file tcpdf.php, variable <a href="com-tecnick-tcpdf/TCPDF.html#var$bMargin">TCPDF::$bMargin</a></dd>
							<dt><b>$booklet</b></dt>
				<dd>in file tcpdf.php, variable <a href="com-tecnick-tcpdf/TCPDF.html#var$booklet">TCPDF::$booklet</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Booklet mode for double-sided pages</dd>
							<dt><b>$buffer</b></dt>
				<dd>in file tcpdf.php, variable <a href="com-tecnick-tcpdf/TCPDF.html#var$buffer">TCPDF::$buffer</a></dd>
							<dt><b>$bufferlen</b></dt>
				<dd>in file tcpdf.php, variable <a href="com-tecnick-tcpdf/TCPDF.html#var$bufferlen">TCPDF::$bufferlen</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Lenght of the buffer in bytes.</dd>
							<dt><b>barcodes.php</b></dt>
				<dd>procedural page <a href="com-tecnick-tcpdf/_barcodes.php.html">barcodes.php</a></dd>
							<dt><b>barcode_c128</b></dt>
				<dd>in file barcodes.php, method <a href="com-tecnick-tcpdf/TCPDFBarcode.html#methodbarcode_c128">TCPDFBarcode::barcode_c128()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;C128 barcodes.</dd>
							<dt><b>barcode_codabar</b></dt>
				<dd>in file barcodes.php, method <a href="com-tecnick-tcpdf/TCPDFBarcode.html#methodbarcode_codabar">TCPDFBarcode::barcode_codabar()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;CODABAR barcodes.</dd>
							<dt><b>barcode_code11</b></dt>
				<dd>in file barcodes.php, method <a href="com-tecnick-tcpdf/TCPDFBarcode.html#methodbarcode_code11">TCPDFBarcode::barcode_code11()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;CODE11 barcodes.</dd>
							<dt><b>barcode_code39</b></dt>
				<dd>in file barcodes.php, method <a href="com-tecnick-tcpdf/TCPDFBarcode.html#methodbarcode_code39">TCPDFBarcode::barcode_code39()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;CODE 39 - ANSI MH10.8M-1983 - USD-3 - 3 of 9.</dd>
							<dt><b>barcode_code93</b></dt>
				<dd>in file barcodes.php, method <a href="com-tecnick-tcpdf/TCPDFBarcode.html#methodbarcode_code93">TCPDFBarcode::barcode_code93()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;CODE 93 - USS-93</dd>
							<dt><b>barcode_eanext</b></dt>
				<dd>in file barcodes.php, method <a href="com-tecnick-tcpdf/TCPDFBarcode.html#methodbarcode_eanext">TCPDFBarcode::barcode_eanext()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;UPC-Based Extentions</dd>
							<dt><b>barcode_eanupc</b></dt>
				<dd>in file barcodes.php, method <a href="com-tecnick-tcpdf/TCPDFBarcode.html#methodbarcode_eanupc">TCPDFBarcode::barcode_eanupc()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;EAN13 and UPC-A barcodes.</dd>
							<dt><b>barcode_i25</b></dt>
				<dd>in file barcodes.php, method <a href="com-tecnick-tcpdf/TCPDFBarcode.html#methodbarcode_i25">TCPDFBarcode::barcode_i25()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Interleaved 2 of 5 barcodes.</dd>
							<dt><b>barcode_imb</b></dt>
				<dd>in file barcodes.php, method <a href="com-tecnick-tcpdf/TCPDFBarcode.html#methodbarcode_imb">TCPDFBarcode::barcode_imb()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;IMB - Intelligent Mail Barcode - Onecode - USPS-B-3200  (requires PHP bcmath extension)  Intelligent Mail barcode is a 65-bar code for use on mail in the United States.</dd>
							<dt><b>barcode_msi</b></dt>
				<dd>in file barcodes.php, method <a href="com-tecnick-tcpdf/TCPDFBarcode.html#methodbarcode_msi">TCPDFBarcode::barcode_msi()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;MSI.</dd>
							<dt><b>barcode_pharmacode</b></dt>
				<dd>in file barcodes.php, method <a href="com-tecnick-tcpdf/TCPDFBarcode.html#methodbarcode_pharmacode">TCPDFBarcode::barcode_pharmacode()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Pharmacode</dd>
							<dt><b>barcode_pharmacode2t</b></dt>
				<dd>in file barcodes.php, method <a href="com-tecnick-tcpdf/TCPDFBarcode.html#methodbarcode_pharmacode2t">TCPDFBarcode::barcode_pharmacode2t()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Pharmacode two-track</dd>
							<dt><b>barcode_postnet</b></dt>
				<dd>in file barcodes.php, method <a href="com-tecnick-tcpdf/TCPDFBarcode.html#methodbarcode_postnet">TCPDFBarcode::barcode_postnet()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;POSTNET and PLANET barcodes.</dd>
							<dt><b>barcode_rms4cc</b></dt>
				<dd>in file barcodes.php, method <a href="com-tecnick-tcpdf/TCPDFBarcode.html#methodbarcode_rms4cc">TCPDFBarcode::barcode_rms4cc()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;RMS4CC - CBC - KIX  RMS4CC (Royal Mail 4-state Customer Code) - CBC (Customer Bar Code) - KIX (Klant index - Customer index)  RM4SCC is the name of the barcode symbology used by the Royal Mail for its Cleanmail service.</dd>
							<dt><b>barcode_s25</b></dt>
				<dd>in file barcodes.php, method <a href="com-tecnick-tcpdf/TCPDFBarcode.html#methodbarcode_s25">TCPDFBarcode::barcode_s25()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Standard 2 of 5 barcodes.</dd>
							<dt><b>binseq_to_array</b></dt>
				<dd>in file barcodes.php, method <a href="com-tecnick-tcpdf/TCPDFBarcode.html#methodbinseq_to_array">TCPDFBarcode::binseq_to_array()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Convert binary barcode sequence to TCPDF barcode array</dd>
							<dt><b>Bookmark</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodBookmark">TCPDF::Bookmark()</a></dd>
							<dt><b>Button</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodButton">TCPDF::Button()</a></dd>
					</dl>
	</div>
	<a href="elementindex_com-tecnick-tcpdf.html#top">top</a><br>
  <hr />
	<a name="c"></a>
	<div>
		<h2>c</h2>
		<dl>
							<dt><b>$cache_file_lenght</b></dt>
				<dd>in file tcpdf.php, variable <a href="com-tecnick-tcpdf/TCPDF.html#var$cache_file_lenght">TCPDF::$cache_file_lenght</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Array used to store the lenghts of cache files</dd>
							<dt><b>$cache_maxsize_UTF8StringToArray</b></dt>
				<dd>in file tcpdf.php, variable <a href="com-tecnick-tcpdf/TCPDF.html#var$cache_maxsize_UTF8StringToArray">TCPDF::$cache_maxsize_UTF8StringToArray</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Maximum size of cache array used for UTF8StringToArray() method.</dd>
							<dt><b>$cache_size_UTF8StringToArray</b></dt>
				<dd>in file tcpdf.php, variable <a href="com-tecnick-tcpdf/TCPDF.html#var$cache_size_UTF8StringToArray">TCPDF::$cache_size_UTF8StringToArray</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Current size of cache array used for UTF8StringToArray() method.</dd>
							<dt><b>$cache_UTF8StringToArray</b></dt>
				<dd>in file tcpdf.php, variable <a href="com-tecnick-tcpdf/TCPDF.html#var$cache_UTF8StringToArray">TCPDF::$cache_UTF8StringToArray</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Cache array for UTF8StringToArray() method.</dd>
							<dt><b>$cell_height_ratio</b></dt>
				<dd>in file tcpdf.php, variable <a href="com-tecnick-tcpdf/TCPDF.html#var$cell_height_ratio">TCPDF::$cell_height_ratio</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Default cell height ratio.</dd>
							<dt><b>$cMargin</b></dt>
				<dd>in file tcpdf.php, variable <a href="com-tecnick-tcpdf/TCPDF.html#var$cMargin">TCPDF::$cMargin</a></dd>
							<dt><b>$ColorFlag</b></dt>
				<dd>in file tcpdf.php, variable <a href="com-tecnick-tcpdf/TCPDF.html#var$ColorFlag">TCPDF::$ColorFlag</a></dd>
							<dt><b>$compress</b></dt>
				<dd>in file tcpdf.php, variable <a href="com-tecnick-tcpdf/TCPDF.html#var$compress">TCPDF::$compress</a></dd>
							<dt><b>$CoreFonts</b></dt>
				<dd>in file tcpdf.php, variable <a href="com-tecnick-tcpdf/TCPDF.html#var$CoreFonts">TCPDF::$CoreFonts</a></dd>
							<dt><b>$creator</b></dt>
				<dd>in file tcpdf.php, variable <a href="com-tecnick-tcpdf/TCPDF.html#var$creator">TCPDF::$creator</a></dd>
							<dt><b>$CurOrientation</b></dt>
				<dd>in file tcpdf.php, variable <a href="com-tecnick-tcpdf/TCPDF.html#var$CurOrientation">TCPDF::$CurOrientation</a></dd>
							<dt><b>$CurrentFont</b></dt>
				<dd>in file tcpdf.php, variable <a href="com-tecnick-tcpdf/TCPDF.html#var$CurrentFont">TCPDF::$CurrentFont</a></dd>
							<dt><b>$currpagegroup</b></dt>
				<dd>in file tcpdf.php, variable <a href="com-tecnick-tcpdf/TCPDF.html#var$currpagegroup">TCPDF::$currpagegroup</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Contains the alias of the current page group</dd>
							<dt><b>$customlistindent</b></dt>
				<dd>in file tcpdf.php, variable <a href="com-tecnick-tcpdf/TCPDF.html#var$customlistindent">TCPDF::$customlistindent</a></dd>
							<dt><b>Cell</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodCell">TCPDF::Cell()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Prints a cell (rectangular area) with optional borders, background color and character string. The upper-left corner of the cell corresponds to the current position. The text can be aligned or centered. After the call, the current position moves to the right or to the next line. It is possible to put a link on the text.<br />  If automatic page breaking is enabled and the cell goes beyond the limit, a page break is done before outputting.</dd>
							<dt><b>CheckBox</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodCheckBox">TCPDF::CheckBox()</a></dd>
							<dt><b>checkPageBreak</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodcheckPageBreak">TCPDF::checkPageBreak()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Add page if needed.</dd>
							<dt><b>checksum_code39</b></dt>
				<dd>in file barcodes.php, method <a href="com-tecnick-tcpdf/TCPDFBarcode.html#methodchecksum_code39">TCPDFBarcode::checksum_code39()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Calculate CODE 39 checksum (modulo 43).</dd>
							<dt><b>checksum_code93</b></dt>
				<dd>in file barcodes.php, method <a href="com-tecnick-tcpdf/TCPDFBarcode.html#methodchecksum_code93">TCPDFBarcode::checksum_code93()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Calculate CODE 93 checksum (modulo 47).</dd>
							<dt><b>checksum_s25</b></dt>
				<dd>in file barcodes.php, method <a href="com-tecnick-tcpdf/TCPDFBarcode.html#methodchecksum_s25">TCPDFBarcode::checksum_s25()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Checksum for standard 2 of 5 barcodes.</dd>
							<dt><b>Circle</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodCircle">TCPDF::Circle()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Draws a circle.</dd>
							<dt><b>Clip</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodClip">TCPDF::Clip()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Set a rectangular clipping area.</dd>
							<dt><b>Close</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodClose">TCPDF::Close()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Terminates the PDF document.</dd>
							<dt><b>closeHTMLTagHandler</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodcloseHTMLTagHandler">TCPDF::closeHTMLTagHandler()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Process closing tags.</dd>
							<dt><b>ComboBox</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodComboBox">TCPDF::ComboBox()</a></dd>
							<dt><b>commitTransaction</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodcommitTransaction">TCPDF::commitTransaction()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Delete the copy of the current TCPDF object used for undo operation.</dd>
							<dt><b>convertHTMLColorToDec</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodconvertHTMLColorToDec">TCPDF::convertHTMLColorToDec()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Returns an associative array (keys: R,G,B) from an html color name or a six-digit or three-digit hexadecimal color representation (i.e. #3FE5AA or #7FF).</dd>
							<dt><b>CoonsPatchMesh</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodCoonsPatchMesh">TCPDF::CoonsPatchMesh()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Paints a coons patch mesh.</dd>
							<dt><b>Curve</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodCurve">TCPDF::Curve()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Draws a Bezier curve.</dd>
					</dl>
	</div>
	<a href="elementindex_com-tecnick-tcpdf.html#top">top</a><br>
  <hr />
	<a name="d"></a>
	<div>
		<h2>d</h2>
		<dl>
							<dt><b>$default_monospaced_font</b></dt>
				<dd>in file tcpdf.php, variable <a href="com-tecnick-tcpdf/TCPDF.html#var$default_monospaced_font">TCPDF::$default_monospaced_font</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Default monospaced font</dd>
							<dt><b>$default_table_columns</b></dt>
				<dd>in file tcpdf.php, variable <a href="com-tecnick-tcpdf/TCPDF.html#var$default_table_columns">TCPDF::$default_table_columns</a></dd>
							<dt><b>$diffs</b></dt>
				<dd>in file tcpdf.php, variable <a href="com-tecnick-tcpdf/TCPDF.html#var$diffs">TCPDF::$diffs</a></dd>
							<dt><b>$diskcache</b></dt>
				<dd>in file tcpdf.php, variable <a href="com-tecnick-tcpdf/TCPDF.html#var$diskcache">TCPDF::$diskcache</a><br>&nbsp;&nbsp;&nbsp;&nbsp;If true enables disk caching.</dd>
							<dt><b>$dpi</b></dt>
				<dd>in file tcpdf.php, variable <a href="com-tecnick-tcpdf/TCPDF.html#var$dpi">TCPDF::$dpi</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Dot Per Inch Document Resolution (do not change)</dd>
							<dt><b>$DrawColor</b></dt>
				<dd>in file tcpdf.php, variable <a href="com-tecnick-tcpdf/TCPDF.html#var$DrawColor">TCPDF::$DrawColor</a></dd>
							<dt><b>dec_to_hex</b></dt>
				<dd>in file barcodes.php, method <a href="com-tecnick-tcpdf/TCPDFBarcode.html#methoddec_to_hex">TCPDFBarcode::dec_to_hex()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Convert large integer number to hexadecimal representation.</dd>
							<dt><b>deletePage</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methoddeletePage">TCPDF::deletePage()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Remove the specified page.</dd>
					</dl>
	</div>
	<a href="elementindex_com-tecnick-tcpdf.html#top">top</a><br>
  <hr />
	<a name="e"></a>
	<div>
		<h2>e</h2>
		<dl>
							<dt><b>$embeddedfiles</b></dt>
				<dd>in file tcpdf.php, variable <a href="com-tecnick-tcpdf/TCPDF.html#var$embeddedfiles">TCPDF::$embeddedfiles</a></dd>
							<dt><b>$encoding</b></dt>
				<dd>in file tcpdf.php, variable <a href="com-tecnick-tcpdf/TCPDF.html#var$encoding">TCPDF::$encoding</a></dd>
							<dt><b>$encrypted</b></dt>
				<dd>in file tcpdf.php, variable <a href="com-tecnick-tcpdf/TCPDF.html#var$encrypted">TCPDF::$encrypted</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Indicates whether document is protected</dd>
							<dt><b>$encryption_key</b></dt>
				<dd>in file tcpdf.php, variable <a href="com-tecnick-tcpdf/TCPDF.html#var$encryption_key">TCPDF::$encryption_key</a><br>&nbsp;&nbsp;&nbsp;&nbsp;RC4 encryption key</dd>
							<dt><b>$enc_obj_id</b></dt>
				<dd>in file tcpdf.php, variable <a href="com-tecnick-tcpdf/TCPDF.html#var$enc_obj_id">TCPDF::$enc_obj_id</a><br>&nbsp;&nbsp;&nbsp;&nbsp;encryption object id</dd>
							<dt><b>$endlinex</b></dt>
				<dd>in file tcpdf.php, variable <a href="com-tecnick-tcpdf/TCPDF.html#var$endlinex">TCPDF::$endlinex</a><br>&nbsp;&nbsp;&nbsp;&nbsp;End position of the latest inserted line</dd>
							<dt><b>$epsmarker</b></dt>
				<dd>in file tcpdf.php, variable <a href="com-tecnick-tcpdf/TCPDF.html#var$epsmarker">TCPDF::$epsmarker</a><br>&nbsp;&nbsp;&nbsp;&nbsp;String used to mark the beginning and end of EPS image blocks</dd>
							<dt><b>$extgstates</b></dt>
				<dd>in file tcpdf.php, variable <a href="com-tecnick-tcpdf/TCPDF.html#var$extgstates">TCPDF::$extgstates</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Array of transparency objects and parameters.</dd>
							<dt><b>Ellipse</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodEllipse">TCPDF::Ellipse()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Draws an ellipse.</dd>
							<dt><b>empty_string</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodempty_string">TCPDF::empty_string()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Determine whether a string is empty.</dd>
							<dt><b>encode_code39_ext</b></dt>
				<dd>in file barcodes.php, method <a href="com-tecnick-tcpdf/TCPDFBarcode.html#methodencode_code39_ext">TCPDFBarcode::encode_code39_ext()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Encode a string to be used for CODE 39 Extended mode.</dd>
							<dt><b>endPage</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodendPage">TCPDF::endPage()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Terminate the current page</dd>
							<dt><b>Error</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodError">TCPDF::Error()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;This method is automatically called in case of fatal error; it simply outputs the message and halts the execution. An inherited class may override it to customize the error handling but should always halt the script, or the resulting document would probably be invalid.</dd>
					</dl>
	</div>
	<a href="elementindex_com-tecnick-tcpdf.html#top">top</a><br>
  <hr />
	<a name="f"></a>
	<div>
		<h2>f</h2>
		<dl>
							<dt><b>$feps</b></dt>
				<dd>in file tcpdf.php, variable <a href="com-tecnick-tcpdf/TCPDF.html#var$feps">TCPDF::$feps</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Epsilon value used for float calculations</dd>
							<dt><b>$fgcolor</b></dt>
				<dd>in file tcpdf.php, variable <a href="com-tecnick-tcpdf/TCPDF.html#var$fgcolor">TCPDF::$fgcolor</a></dd>
							<dt><b>$fhPt</b></dt>
				<dd>in file tcpdf.php, variable <a href="com-tecnick-tcpdf/TCPDF.html#var$fhPt">TCPDF::$fhPt</a></dd>
							<dt><b>$FillColor</b></dt>
				<dd>in file tcpdf.php, variable <a href="com-tecnick-tcpdf/TCPDF.html#var$FillColor">TCPDF::$FillColor</a></dd>
							<dt><b>$FontAscent</b></dt>
				<dd>in file tcpdf.php, variable <a href="com-tecnick-tcpdf/TCPDF.html#var$FontAscent">TCPDF::$FontAscent</a></dd>
							<dt><b>$FontDescent</b></dt>
				<dd>in file tcpdf.php, variable <a href="com-tecnick-tcpdf/TCPDF.html#var$FontDescent">TCPDF::$FontDescent</a></dd>
							<dt><b>$FontFamily</b></dt>
				<dd>in file tcpdf.php, variable <a href="com-tecnick-tcpdf/TCPDF.html#var$FontFamily">TCPDF::$FontFamily</a></dd>
							<dt><b>$FontFiles</b></dt>
				<dd>in file tcpdf.php, variable <a href="com-tecnick-tcpdf/TCPDF.html#var$FontFiles">TCPDF::$FontFiles</a></dd>
							<dt><b>$fontkeys</b></dt>
				<dd>in file tcpdf.php, variable <a href="com-tecnick-tcpdf/TCPDF.html#var$fontkeys">TCPDF::$fontkeys</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Store the font keys.</dd>
							<dt><b>$fontlist</b></dt>
				<dd>in file tcpdf.php, variable <a href="com-tecnick-tcpdf/TCPDF.html#var$fontlist">TCPDF::$fontlist</a></dd>
							<dt><b>$fonts</b></dt>
				<dd>in file tcpdf.php, variable <a href="com-tecnick-tcpdf/TCPDF.html#var$fonts">TCPDF::$fonts</a></dd>
							<dt><b>$FontSize</b></dt>
				<dd>in file tcpdf.php, variable <a href="com-tecnick-tcpdf/TCPDF.html#var$FontSize">TCPDF::$FontSize</a></dd>
							<dt><b>$FontSizePt</b></dt>
				<dd>in file tcpdf.php, variable <a href="com-tecnick-tcpdf/TCPDF.html#var$FontSizePt">TCPDF::$FontSizePt</a></dd>
							<dt><b>$FontStyle</b></dt>
				<dd>in file tcpdf.php, variable <a href="com-tecnick-tcpdf/TCPDF.html#var$FontStyle">TCPDF::$FontStyle</a></dd>
							<dt><b>$footerlen</b></dt>
				<dd>in file tcpdf.php, variable <a href="com-tecnick-tcpdf/TCPDF.html#var$footerlen">TCPDF::$footerlen</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Array used to store footer lenght of each page.</dd>
							<dt><b>$footerpos</b></dt>
				<dd>in file tcpdf.php, variable <a href="com-tecnick-tcpdf/TCPDF.html#var$footerpos">TCPDF::$footerpos</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Array used to store footer positions of each page.</dd>
							<dt><b>$footer_font</b></dt>
				<dd>in file tcpdf.php, variable <a href="com-tecnick-tcpdf/TCPDF.html#var$footer_font">TCPDF::$footer_font</a></dd>
							<dt><b>$footer_margin</b></dt>
				<dd>in file tcpdf.php, variable <a href="com-tecnick-tcpdf/TCPDF.html#var$footer_margin">TCPDF::$footer_margin</a></dd>
							<dt><b>$fwPt</b></dt>
				<dd>in file tcpdf.php, variable <a href="com-tecnick-tcpdf/TCPDF.html#var$fwPt">TCPDF::$fwPt</a></dd>
							<dt><b>Footer</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodFooter">TCPDF::Footer()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;This method is used to render the page footer.</dd>
							<dt><b>formatPageNumber</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodformatPageNumber">TCPDF::formatPageNumber()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Format the page numbers.</dd>
							<dt><b>formatTOCPageNumber</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodformatTOCPageNumber">TCPDF::formatTOCPageNumber()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Format the page numbers on the Table Of Content.</dd>
					</dl>
	</div>
	<a href="elementindex_com-tecnick-tcpdf.html#top">top</a><br>
  <hr />
	<a name="g"></a>
	<div>
		<h2>g</h2>
		<dl>
							<dt><b>$gradients</b></dt>
				<dd>in file tcpdf.php, variable <a href="com-tecnick-tcpdf/TCPDF.html#var$gradients">TCPDF::$gradients</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Array for storing gradient information.</dd>
							<dt><b>GetAbsX</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodGetAbsX">TCPDF::GetAbsX()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Returns the absolute X value of current position.</dd>
							<dt><b>getAliasNbPages</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodgetAliasNbPages">TCPDF::getAliasNbPages()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Returns the string alias used for the total number of pages.</dd>
							<dt><b>getAliasNumPage</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodgetAliasNumPage">TCPDF::getAliasNumPage()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Returns the string alias used for the page number.</dd>
							<dt><b>GetArrStringWidth</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodGetArrStringWidth">TCPDF::GetArrStringWidth()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Returns the string length of an array of chars in user unit. A font must be selected.<br /></dd>
							<dt><b>getBarcode</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodgetBarcode">TCPDF::getBarcode()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Get current barcode.</dd>
							<dt><b>getBarcodeArray</b></dt>
				<dd>in file barcodes.php, method <a href="com-tecnick-tcpdf/TCPDFBarcode.html#methodgetBarcodeArray">TCPDFBarcode::getBarcodeArray()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Return an array representations of barcode.</dd>
							<dt><b>getBorderMode</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodgetBorderMode">TCPDF::getBorderMode()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Get the border mode accounting for multicell position (opens bottom side of multicell crossing pages)</dd>
							<dt><b>getBreakMargin</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodgetBreakMargin">TCPDF::getBreakMargin()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Returns the page break margin.</dd>
							<dt><b>getBuffer</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodgetBuffer">TCPDF::getBuffer()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Get buffer content.</dd>
							<dt><b>getCellCode</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodgetCellCode">TCPDF::getCellCode()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Returns the PDF string code to print a cell (rectangular area) with optional borders, background color and character string. The upper-left corner of the cell corresponds to the current position. The text can be aligned or centered. After the call, the current position moves to the right or to the next line. It is possible to put a link on the text.<br />  If automatic page breaking is enabled and the cell goes beyond the limit, a page break is done before outputting.</dd>
							<dt><b>getCellHeightRatio</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodgetCellHeightRatio">TCPDF::getCellHeightRatio()</a></dd>
							<dt><b>GetCharWidth</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodGetCharWidth">TCPDF::GetCharWidth()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Returns the length of the char in user unit for the current font.<br /></dd>
							<dt><b>getFontBuffer</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodgetFontBuffer">TCPDF::getFontBuffer()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Get font buffer content.</dd>
							<dt><b>getFontFamily</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodgetFontFamily">TCPDF::getFontFamily()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Returns the current font family name.</dd>
							<dt><b>getFontSize</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodgetFontSize">TCPDF::getFontSize()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Returns the current font size.</dd>
							<dt><b>getFontSizePt</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodgetFontSizePt">TCPDF::getFontSizePt()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Returns the current font size in points unit.</dd>
							<dt><b>getFontsList</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodgetFontsList">TCPDF::getFontsList()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Fill the list of available fonts ($this-&gt;fontlist).</dd>
							<dt><b>getFontStyle</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodgetFontStyle">TCPDF::getFontStyle()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Returns the current font style.</dd>
							<dt><b>getFooterFont</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodgetFooterFont">TCPDF::getFooterFont()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Get Footer font.</dd>
							<dt><b>getFooterMargin</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodgetFooterMargin">TCPDF::getFooterMargin()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Returns footer margin in user units.</dd>
							<dt><b>getGDgamma</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodgetGDgamma">TCPDF::getGDgamma()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Correct the gamma value to be used with GD library</dd>
							<dt><b>getGraphicVars</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodgetGraphicVars">TCPDF::getGraphicVars()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Returns current graphic variables as array.</dd>
							<dt><b>getGroupPageNo</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodgetGroupPageNo">TCPDF::getGroupPageNo()</a></dd>
							<dt><b>getGroupPageNoFormatted</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodgetGroupPageNoFormatted">TCPDF::getGroupPageNoFormatted()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Returns the current group page number formatted as a string.</dd>
							<dt><b>getHeaderData</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodgetHeaderData">TCPDF::getHeaderData()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Returns header data:</dd>
							<dt><b>getHeaderFont</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodgetHeaderFont">TCPDF::getHeaderFont()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Get header font.</dd>
							<dt><b>getHeaderMargin</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodgetHeaderMargin">TCPDF::getHeaderMargin()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Returns header margin in user units.</dd>
							<dt><b>getHtmlDomArray</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodgetHtmlDomArray">TCPDF::getHtmlDomArray()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Returns the HTML DOM array.</dd>
							<dt><b>getHTMLUnitToUnits</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodgetHTMLUnitToUnits">TCPDF::getHTMLUnitToUnits()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;convert html string containing value and unit of measure to user's units or points.</dd>
							<dt><b>getImageBuffer</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodgetImageBuffer">TCPDF::getImageBuffer()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Get page buffer content.</dd>
							<dt><b>getImageRBX</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodgetImageRBX">TCPDF::getImageRBX()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Return the right-bottom (or left-bottom for RTL) corner X coordinate of last inserted image</dd>
							<dt><b>getImageRBY</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodgetImageRBY">TCPDF::getImageRBY()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Return the right-bottom (or left-bottom for RTL) corner Y coordinate of last inserted image</dd>
							<dt><b>getImageScale</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodgetImageScale">TCPDF::getImageScale()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Returns the adjusting factor to convert pixels to user units.</dd>
							<dt><b>getLastH</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodgetLastH">TCPDF::getLastH()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Get the last cell height.</dd>
							<dt><b>GetLineWidth</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodGetLineWidth">TCPDF::GetLineWidth()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Returns the current the line width.</dd>
							<dt><b>getMargins</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodgetMargins">TCPDF::getMargins()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Returns an array containing current margins:</dd>
							<dt><b>GetNumChars</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodGetNumChars">TCPDF::GetNumChars()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Returns the numbero of characters in a string.</dd>
							<dt><b>getNumLines</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodgetNumLines">TCPDF::getNumLines()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;This method returns the estimated number of lines required to print the text.</dd>
							<dt><b>getNumPages</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodgetNumPages">TCPDF::getNumPages()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Get the total number of insered pages.</dd>
							<dt><b>getObjFilename</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodgetObjFilename">TCPDF::getObjFilename()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Returns a temporary filename for caching object on filesystem.</dd>
							<dt><b>getOriginalMargins</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodgetOriginalMargins">TCPDF::getOriginalMargins()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Returns an array containing original margins:</dd>
							<dt><b>getPage</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodgetPage">TCPDF::getPage()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Get current document page number.</dd>
							<dt><b>getPageBuffer</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodgetPageBuffer">TCPDF::getPageBuffer()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Get page buffer content.</dd>
							<dt><b>getPageDimensions</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodgetPageDimensions">TCPDF::getPageDimensions()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Returns an array of page dimensions:</dd>
							<dt><b>getPageGroupAlias</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodgetPageGroupAlias">TCPDF::getPageGroupAlias()</a></dd>
							<dt><b>getPageHeight</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodgetPageHeight">TCPDF::getPageHeight()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Returns the page height in units.</dd>
							<dt><b>getPageNumGroupAlias</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodgetPageNumGroupAlias">TCPDF::getPageNumGroupAlias()</a></dd>
							<dt><b>getPageWidth</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodgetPageWidth">TCPDF::getPageWidth()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Returns the page width in units.</dd>
							<dt><b>getPDFData</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodgetPDFData">TCPDF::getPDFData()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Returns the PDF data.</dd>
							<dt><b>getRemainingWidth</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodgetRemainingWidth">TCPDF::getRemainingWidth()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Returns the remaining width between the current position and margins.</dd>
							<dt><b>getRTL</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodgetRTL">TCPDF::getRTL()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Return the RTL status</dd>
							<dt><b>getScaleFactor</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodgetScaleFactor">TCPDF::getScaleFactor()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Returns the scale factor (number of points in user unit).</dd>
							<dt><b>GetStringWidth</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodGetStringWidth">TCPDF::GetStringWidth()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Returns the length of a string in user unit. A font must be selected.<br /></dd>
							<dt><b>GetX</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodGetX">TCPDF::GetX()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Returns the relative X value of current position.</dd>
							<dt><b>GetY</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodGetY">TCPDF::GetY()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Returns the ordinate of the current position.</dd>
							<dt><b>Gradient</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodGradient">TCPDF::Gradient()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Output gradient.</dd>
					</dl>
	</div>
	<a href="elementindex_com-tecnick-tcpdf.html#top">top</a><br>
  <hr />
	<a name="h"></a>
	<div>
		<h2>h</h2>
		<dl>
							<dt><b>$h</b></dt>
				<dd>in file tcpdf.php, variable <a href="com-tecnick-tcpdf/TCPDF.html#var$h">TCPDF::$h</a></dd>
							<dt><b>$header_font</b></dt>
				<dd>in file tcpdf.php, variable <a href="com-tecnick-tcpdf/TCPDF.html#var$header_font">TCPDF::$header_font</a></dd>
							<dt><b>$header_logo</b></dt>
				<dd>in file tcpdf.php, variable <a href="com-tecnick-tcpdf/TCPDF.html#var$header_logo">TCPDF::$header_logo</a></dd>
							<dt><b>$header_logo_width</b></dt>
				<dd>in file tcpdf.php, variable <a href="com-tecnick-tcpdf/TCPDF.html#var$header_logo_width">TCPDF::$header_logo_width</a></dd>
							<dt><b>$header_margin</b></dt>
				<dd>in file tcpdf.php, variable <a href="com-tecnick-tcpdf/TCPDF.html#var$header_margin">TCPDF::$header_margin</a></dd>
							<dt><b>$header_string</b></dt>
				<dd>in file tcpdf.php, variable <a href="com-tecnick-tcpdf/TCPDF.html#var$header_string">TCPDF::$header_string</a></dd>
							<dt><b>$header_title</b></dt>
				<dd>in file tcpdf.php, variable <a href="com-tecnick-tcpdf/TCPDF.html#var$header_title">TCPDF::$header_title</a></dd>
							<dt><b>$hPt</b></dt>
				<dd>in file tcpdf.php, variable <a href="com-tecnick-tcpdf/TCPDF.html#var$hPt">TCPDF::$hPt</a></dd>
							<dt><b>$HREF</b></dt>
				<dd>in file tcpdf.php, variable <a href="com-tecnick-tcpdf/TCPDF.html#var$HREF">TCPDF::$HREF</a></dd>
							<dt><b>$htmlLinkColorArray</b></dt>
				<dd>in file tcpdf.php, variable <a href="com-tecnick-tcpdf/TCPDF.html#var$htmlLinkColorArray">TCPDF::$htmlLinkColorArray</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Default color for html links</dd>
							<dt><b>$htmlLinkFontStyle</b></dt>
				<dd>in file tcpdf.php, variable <a href="com-tecnick-tcpdf/TCPDF.html#var$htmlLinkFontStyle">TCPDF::$htmlLinkFontStyle</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Default font style to add to html links</dd>
							<dt><b>$htmlvspace</b></dt>
				<dd>in file tcpdf.php, variable <a href="com-tecnick-tcpdf/TCPDF.html#var$htmlvspace">TCPDF::$htmlvspace</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Count the latest inserted vertical spaces on HTML</dd>
							<dt><b>Header</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodHeader">TCPDF::Header()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;This method is used to render the page header.</dd>
							<dt><b>HEAD_MAGNIFICATION</b></dt>
				<dd>in file tcpdf_config.php, constant <a href="com-tecnick-tcpdf/_config---tcpdf_config.php.html#defineHEAD_MAGNIFICATION">HEAD_MAGNIFICATION</a><br>&nbsp;&nbsp;&nbsp;&nbsp;magnification factor for titles</dd>
							<dt><b>hex_to_dec</b></dt>
				<dd>in file barcodes.php, method <a href="com-tecnick-tcpdf/TCPDFBarcode.html#methodhex_to_dec">TCPDFBarcode::hex_to_dec()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Convert large hexadecimal number to decimal representation (string).</dd>
							<dt><b>htmlcolors.php</b></dt>
				<dd>procedural page <a href="com-tecnick-tcpdf/_htmlcolors.php.html">htmlcolors.php</a></dd>
					</dl>
	</div>
	<a href="elementindex_com-tecnick-tcpdf.html#top">top</a><br>
  <hr />
	<a name="i"></a>
	<div>
		<h2>i</h2>
		<dl>
							<dt><b>$imagekeys</b></dt>
				<dd>in file tcpdf.php, variable <a href="com-tecnick-tcpdf/TCPDF.html#var$imagekeys">TCPDF::$imagekeys</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Store the image keys.</dd>
							<dt><b>$images</b></dt>
				<dd>in file tcpdf.php, variable <a href="com-tecnick-tcpdf/TCPDF.html#var$images">TCPDF::$images</a></dd>
							<dt><b>$imgscale</b></dt>
				<dd>in file tcpdf.php, variable <a href="com-tecnick-tcpdf/TCPDF.html#var$imgscale">TCPDF::$imgscale</a></dd>
							<dt><b>$img_rb_x</b></dt>
				<dd>in file tcpdf.php, variable <a href="com-tecnick-tcpdf/TCPDF.html#var$img_rb_x">TCPDF::$img_rb_x</a></dd>
							<dt><b>$img_rb_y</b></dt>
				<dd>in file tcpdf.php, variable <a href="com-tecnick-tcpdf/TCPDF.html#var$img_rb_y">TCPDF::$img_rb_y</a></dd>
							<dt><b>$InFooter</b></dt>
				<dd>in file tcpdf.php, variable <a href="com-tecnick-tcpdf/TCPDF.html#var$InFooter">TCPDF::$InFooter</a></dd>
							<dt><b>$internal_encoding</b></dt>
				<dd>in file tcpdf.php, variable <a href="com-tecnick-tcpdf/TCPDF.html#var$internal_encoding">TCPDF::$internal_encoding</a></dd>
							<dt><b>$intmrk</b></dt>
				<dd>in file tcpdf.php, variable <a href="com-tecnick-tcpdf/TCPDF.html#var$intmrk">TCPDF::$intmrk</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Array used to store positions inside the pages buffer.</dd>
							<dt><b>$isunicode</b></dt>
				<dd>in file tcpdf.php, variable <a href="com-tecnick-tcpdf/TCPDF.html#var$isunicode">TCPDF::$isunicode</a></dd>
							<dt><b>Image</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodImage">TCPDF::Image()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Puts an image in the page.</dd>
							<dt><b>ImageEps</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodImageEps">TCPDF::ImageEps()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Embed vector-based Adobe Illustrator (AI) or AI-compatible EPS files.</dd>
							<dt><b>ImagePngAlpha</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodImagePngAlpha">TCPDF::ImagePngAlpha()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Extract info from a PNG image with alpha channel using the GD library.</dd>
							<dt><b>imb_crc11fcs</b></dt>
				<dd>in file barcodes.php, method <a href="com-tecnick-tcpdf/TCPDFBarcode.html#methodimb_crc11fcs">TCPDFBarcode::imb_crc11fcs()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Intelligent Mail Barcode calculation of Frame Check Sequence</dd>
							<dt><b>imb_reverse_us</b></dt>
				<dd>in file barcodes.php, method <a href="com-tecnick-tcpdf/TCPDFBarcode.html#methodimb_reverse_us">TCPDFBarcode::imb_reverse_us()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Reverse unsigned short value</dd>
							<dt><b>imb_tables</b></dt>
				<dd>in file barcodes.php, method <a href="com-tecnick-tcpdf/TCPDFBarcode.html#methodimb_tables">TCPDFBarcode::imb_tables()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;generate Nof13 tables used for Intelligent Mail Barcode</dd>
							<dt><b>IncludeJS</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodIncludeJS">TCPDF::IncludeJS()</a></dd>
							<dt><b>intToRoman</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodintToRoman">TCPDF::intToRoman()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Returns the Roman representation of an integer number</dd>
					</dl>
	</div>
	<a href="elementindex_com-tecnick-tcpdf.html#top">top</a><br>
  <hr />
	<a name="j"></a>
	<div>
		<h2>j</h2>
		<dl>
							<dt><b>$javascript</b></dt>
				<dd>in file tcpdf.php, variable <a href="com-tecnick-tcpdf/TCPDF.html#var$javascript">TCPDF::$javascript</a><br>&nbsp;&nbsp;&nbsp;&nbsp;javascript code</dd>
							<dt><b>$jpeg_quality</b></dt>
				<dd>in file tcpdf.php, variable <a href="com-tecnick-tcpdf/TCPDF.html#var$jpeg_quality">TCPDF::$jpeg_quality</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Set the default JPEG compression quality (1-100)</dd>
					</dl>
	</div>
	<a href="elementindex_com-tecnick-tcpdf.html#top">top</a><br>
  <hr />
	<a name="k"></a>
	<div>
		<h2>k</h2>
		<dl>
							<dt><b>$k</b></dt>
				<dd>in file tcpdf.php, variable <a href="com-tecnick-tcpdf/TCPDF.html#var$k">TCPDF::$k</a></dd>
							<dt><b>$keywords</b></dt>
				<dd>in file tcpdf.php, variable <a href="com-tecnick-tcpdf/TCPDF.html#var$keywords">TCPDF::$keywords</a></dd>
							<dt><b>K_BLANK_IMAGE</b></dt>
				<dd>in file tcpdf_config.php, constant <a href="com-tecnick-tcpdf/_config---tcpdf_config.php.html#defineK_BLANK_IMAGE">K_BLANK_IMAGE</a><br>&nbsp;&nbsp;&nbsp;&nbsp;blank image</dd>
							<dt><b>K_CELL_HEIGHT_RATIO</b></dt>
				<dd>in file tcpdf_config.php, constant <a href="com-tecnick-tcpdf/_config---tcpdf_config.php.html#defineK_CELL_HEIGHT_RATIO">K_CELL_HEIGHT_RATIO</a><br>&nbsp;&nbsp;&nbsp;&nbsp;height of cell repect font height</dd>
							<dt><b>K_LRE</b></dt>
				<dd>in file unicode_data.php, constant <a href="com-tecnick-tcpdf/_unicode_data.php.html#defineK_LRE">K_LRE</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Left-to-Right Embedding</dd>
							<dt><b>K_LRM</b></dt>
				<dd>in file unicode_data.php, constant <a href="com-tecnick-tcpdf/_unicode_data.php.html#defineK_LRM">K_LRM</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Left-to-Right Mark</dd>
							<dt><b>K_LRO</b></dt>
				<dd>in file unicode_data.php, constant <a href="com-tecnick-tcpdf/_unicode_data.php.html#defineK_LRO">K_LRO</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Left-to-Right Override</dd>
							<dt><b>K_PATH_CACHE</b></dt>
				<dd>in file tcpdf_config.php, constant <a href="com-tecnick-tcpdf/_config---tcpdf_config.php.html#defineK_PATH_CACHE">K_PATH_CACHE</a><br>&nbsp;&nbsp;&nbsp;&nbsp;cache directory for temporary files (full path)</dd>
							<dt><b>K_PATH_FONTS</b></dt>
				<dd>in file tcpdf_config.php, constant <a href="com-tecnick-tcpdf/_config---tcpdf_config.php.html#defineK_PATH_FONTS">K_PATH_FONTS</a><br>&nbsp;&nbsp;&nbsp;&nbsp;path for PDF fonts</dd>
							<dt><b>K_PATH_IMAGES</b></dt>
				<dd>in file tcpdf_config.php, constant <a href="com-tecnick-tcpdf/_config---tcpdf_config.php.html#defineK_PATH_IMAGES">K_PATH_IMAGES</a><br>&nbsp;&nbsp;&nbsp;&nbsp;images directory</dd>
							<dt><b>K_PATH_MAIN</b></dt>
				<dd>in file tcpdf_config.php, constant <a href="com-tecnick-tcpdf/_config---tcpdf_config.php.html#defineK_PATH_MAIN">K_PATH_MAIN</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Installation path (/var/www/tcpdf/).</dd>
							<dt><b>K_PATH_URL</b></dt>
				<dd>in file tcpdf_config.php, constant <a href="com-tecnick-tcpdf/_config---tcpdf_config.php.html#defineK_PATH_URL">K_PATH_URL</a><br>&nbsp;&nbsp;&nbsp;&nbsp;URL path to tcpdf installation folder (http://localhost/tcpdf/).</dd>
							<dt><b>K_PATH_URL_CACHE</b></dt>
				<dd>in file tcpdf_config.php, constant <a href="com-tecnick-tcpdf/_config---tcpdf_config.php.html#defineK_PATH_URL_CACHE">K_PATH_URL_CACHE</a><br>&nbsp;&nbsp;&nbsp;&nbsp;cache directory for temporary files (url path)</dd>
							<dt><b>K_PDF</b></dt>
				<dd>in file unicode_data.php, constant <a href="com-tecnick-tcpdf/_unicode_data.php.html#defineK_PDF">K_PDF</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Pop Directional Format</dd>
							<dt><b>K_RE_PATTERN_ARABIC</b></dt>
				<dd>in file unicode_data.php, constant <a href="com-tecnick-tcpdf/_unicode_data.php.html#defineK_RE_PATTERN_ARABIC">K_RE_PATTERN_ARABIC</a></dd>
							<dt><b>K_RE_PATTERN_RTL</b></dt>
				<dd>in file unicode_data.php, constant <a href="com-tecnick-tcpdf/_unicode_data.php.html#defineK_RE_PATTERN_RTL">K_RE_PATTERN_RTL</a></dd>
							<dt><b>K_RLE</b></dt>
				<dd>in file unicode_data.php, constant <a href="com-tecnick-tcpdf/_unicode_data.php.html#defineK_RLE">K_RLE</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Right-to-Left Embedding</dd>
							<dt><b>K_RLM</b></dt>
				<dd>in file unicode_data.php, constant <a href="com-tecnick-tcpdf/_unicode_data.php.html#defineK_RLM">K_RLM</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Right-to-Left Mark</dd>
							<dt><b>K_RLO</b></dt>
				<dd>in file unicode_data.php, constant <a href="com-tecnick-tcpdf/_unicode_data.php.html#defineK_RLO">K_RLO</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Right-to-Left Override</dd>
							<dt><b>K_SMALL_RATIO</b></dt>
				<dd>in file tcpdf_config.php, constant <a href="com-tecnick-tcpdf/_config---tcpdf_config.php.html#defineK_SMALL_RATIO">K_SMALL_RATIO</a><br>&nbsp;&nbsp;&nbsp;&nbsp;reduction factor for small font</dd>
							<dt><b>K_TITLE_MAGNIFICATION</b></dt>
				<dd>in file tcpdf_config.php, constant <a href="com-tecnick-tcpdf/_config---tcpdf_config.php.html#defineK_TITLE_MAGNIFICATION">K_TITLE_MAGNIFICATION</a><br>&nbsp;&nbsp;&nbsp;&nbsp;title magnification respect main font size</dd>
					</dl>
	</div>
	<a href="elementindex_com-tecnick-tcpdf.html#top">top</a><br>
  <hr />
	<a name="l"></a>
	<div>
		<h2>l</h2>
		<dl>
							<dt><b>$l</b></dt>
				<dd>in file tcpdf.php, variable <a href="com-tecnick-tcpdf/TCPDF.html#var$l">TCPDF::$l</a></dd>
							<dt><b>$lasth</b></dt>
				<dd>in file tcpdf.php, variable <a href="com-tecnick-tcpdf/TCPDF.html#var$lasth">TCPDF::$lasth</a></dd>
							<dt><b>$last_rc4_key</b></dt>
				<dd>in file tcpdf.php, variable <a href="com-tecnick-tcpdf/TCPDF.html#var$last_rc4_key">TCPDF::$last_rc4_key</a><br>&nbsp;&nbsp;&nbsp;&nbsp;last RC4 key encrypted (cached for optimisation)</dd>
							<dt><b>$last_rc4_key_c</b></dt>
				<dd>in file tcpdf.php, variable <a href="com-tecnick-tcpdf/TCPDF.html#var$last_rc4_key_c">TCPDF::$last_rc4_key_c</a><br>&nbsp;&nbsp;&nbsp;&nbsp;last RC4 computed key</dd>
							<dt><b>$LayoutMode</b></dt>
				<dd>in file tcpdf.php, variable <a href="com-tecnick-tcpdf/TCPDF.html#var$LayoutMode">TCPDF::$LayoutMode</a></dd>
							<dt><b>$linestyleCap</b></dt>
				<dd>in file tcpdf.php, variable <a href="com-tecnick-tcpdf/TCPDF.html#var$linestyleCap">TCPDF::$linestyleCap</a><br>&nbsp;&nbsp;&nbsp;&nbsp;PDF string for last line width</dd>
							<dt><b>$linestyleDash</b></dt>
				<dd>in file tcpdf.php, variable <a href="com-tecnick-tcpdf/TCPDF.html#var$linestyleDash">TCPDF::$linestyleDash</a><br>&nbsp;&nbsp;&nbsp;&nbsp;PDF string for last line width</dd>
							<dt><b>$linestyleJoin</b></dt>
				<dd>in file tcpdf.php, variable <a href="com-tecnick-tcpdf/TCPDF.html#var$linestyleJoin">TCPDF::$linestyleJoin</a><br>&nbsp;&nbsp;&nbsp;&nbsp;PDF string for last line width</dd>
							<dt><b>$linestyleWidth</b></dt>
				<dd>in file tcpdf.php, variable <a href="com-tecnick-tcpdf/TCPDF.html#var$linestyleWidth">TCPDF::$linestyleWidth</a><br>&nbsp;&nbsp;&nbsp;&nbsp;PDF string for last line width</dd>
							<dt><b>$linethrough</b></dt>
				<dd>in file tcpdf.php, variable <a href="com-tecnick-tcpdf/TCPDF.html#var$linethrough">TCPDF::$linethrough</a><br>&nbsp;&nbsp;&nbsp;&nbsp;line trough state</dd>
							<dt><b>$LineWidth</b></dt>
				<dd>in file tcpdf.php, variable <a href="com-tecnick-tcpdf/TCPDF.html#var$LineWidth">TCPDF::$LineWidth</a></dd>
							<dt><b>$links</b></dt>
				<dd>in file tcpdf.php, variable <a href="com-tecnick-tcpdf/TCPDF.html#var$links">TCPDF::$links</a></dd>
							<dt><b>$lispacer</b></dt>
				<dd>in file tcpdf.php, variable <a href="com-tecnick-tcpdf/TCPDF.html#var$lispacer">TCPDF::$lispacer</a></dd>
							<dt><b>$listcount</b></dt>
				<dd>in file tcpdf.php, variable <a href="com-tecnick-tcpdf/TCPDF.html#var$listcount">TCPDF::$listcount</a></dd>
							<dt><b>$listindent</b></dt>
				<dd>in file tcpdf.php, variable <a href="com-tecnick-tcpdf/TCPDF.html#var$listindent">TCPDF::$listindent</a></dd>
							<dt><b>$listnum</b></dt>
				<dd>in file tcpdf.php, variable <a href="com-tecnick-tcpdf/TCPDF.html#var$listnum">TCPDF::$listnum</a></dd>
							<dt><b>$listordered</b></dt>
				<dd>in file tcpdf.php, variable <a href="com-tecnick-tcpdf/TCPDF.html#var$listordered">TCPDF::$listordered</a></dd>
							<dt><b>$lisymbol</b></dt>
				<dd>in file tcpdf.php, variable <a href="com-tecnick-tcpdf/TCPDF.html#var$lisymbol">TCPDF::$lisymbol</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Symbol used for HTML unordered list items</dd>
							<dt><b>$lMargin</b></dt>
				<dd>in file tcpdf.php, variable <a href="com-tecnick-tcpdf/TCPDF.html#var$lMargin">TCPDF::$lMargin</a></dd>
							<dt><b>lastPage</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodlastPage">TCPDF::lastPage()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Reset pointer to the last document page.</dd>
							<dt><b>Line</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodLine">TCPDF::Line()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Draws a line between two points.</dd>
							<dt><b>LinearGradient</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodLinearGradient">TCPDF::LinearGradient()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Paints a linear colour gradient.</dd>
							<dt><b>Link</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodLink">TCPDF::Link()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Puts a link on a rectangular area of the page.</dd>
							<dt><b>ListBox</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodListBox">TCPDF::ListBox()</a></dd>
							<dt><b>Ln</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodLn">TCPDF::Ln()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Performs a line break.</dd>
					</dl>
	</div>
	<a href="elementindex_com-tecnick-tcpdf.html#top">top</a><br>
  <hr />
	<a name="m"></a>
	<div>
		<h2>m</h2>
		<dl>
							<dt><b>MirrorH</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodMirrorH">TCPDF::MirrorH()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Horizontal Mirroring.</dd>
							<dt><b>MirrorL</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodMirrorL">TCPDF::MirrorL()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Reflection against a straight line through point (x, y) with the gradient angle (angle).</dd>
							<dt><b>MirrorP</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodMirrorP">TCPDF::MirrorP()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Point reflection mirroring.</dd>
							<dt><b>MirrorV</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodMirrorV">TCPDF::MirrorV()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Verical Mirroring.</dd>
							<dt><b>movePage</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodmovePage">TCPDF::movePage()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Move a page to a previous position.</dd>
							<dt><b>MultiCell</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodMultiCell">TCPDF::MultiCell()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;This method allows printing text with line breaks.</dd>
					</dl>
	</div>
	<a href="elementindex_com-tecnick-tcpdf.html#top">top</a><br>
  <hr />
	<a name="n"></a>
	<div>
		<h2>n</h2>
		<dl>
							<dt><b>$n</b></dt>
				<dd>in file tcpdf.php, variable <a href="com-tecnick-tcpdf/TCPDF.html#var$n">TCPDF::$n</a></dd>
							<dt><b>$newline</b></dt>
				<dd>in file tcpdf.php, variable <a href="com-tecnick-tcpdf/TCPDF.html#var$newline">TCPDF::$newline</a><br>&nbsp;&nbsp;&nbsp;&nbsp;True if a newline is created.</dd>
							<dt><b>$newpagegroup</b></dt>
				<dd>in file tcpdf.php, variable <a href="com-tecnick-tcpdf/TCPDF.html#var$newpagegroup">TCPDF::$newpagegroup</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Array of page numbers were a new page group was started</dd>
							<dt><b>$numfonts</b></dt>
				<dd>in file tcpdf.php, variable <a href="com-tecnick-tcpdf/TCPDF.html#var$numfonts">TCPDF::$numfonts</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Counts the number of fonts.</dd>
							<dt><b>$numimages</b></dt>
				<dd>in file tcpdf.php, variable <a href="com-tecnick-tcpdf/TCPDF.html#var$numimages">TCPDF::$numimages</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Counts the number of pages.</dd>
							<dt><b>$numpages</b></dt>
				<dd>in file tcpdf.php, variable <a href="com-tecnick-tcpdf/TCPDF.html#var$numpages">TCPDF::$numpages</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Counts the number of pages.</dd>
							<dt><b>$n_js</b></dt>
				<dd>in file tcpdf.php, variable <a href="com-tecnick-tcpdf/TCPDF.html#var$n_js">TCPDF::$n_js</a><br>&nbsp;&nbsp;&nbsp;&nbsp;javascript counter</dd>
							<dt><b>$n_ocg_print</b></dt>
				<dd>in file tcpdf.php, variable <a href="com-tecnick-tcpdf/TCPDF.html#var$n_ocg_print">TCPDF::$n_ocg_print</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Print visibility.</dd>
							<dt><b>$n_ocg_view</b></dt>
				<dd>in file tcpdf.php, variable <a href="com-tecnick-tcpdf/TCPDF.html#var$n_ocg_view">TCPDF::$n_ocg_view</a><br>&nbsp;&nbsp;&nbsp;&nbsp;View visibility.</dd>
					</dl>
	</div>
	<a href="elementindex_com-tecnick-tcpdf.html#top">top</a><br>
  <hr />
	<a name="o"></a>
	<div>
		<h2>o</h2>
		<dl>
							<dt><b>$objcopy</b></dt>
				<dd>in file tcpdf.php, variable <a href="com-tecnick-tcpdf/TCPDF.html#var$objcopy">TCPDF::$objcopy</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Used to store a cloned copy of the current class object</dd>
							<dt><b>$offsets</b></dt>
				<dd>in file tcpdf.php, variable <a href="com-tecnick-tcpdf/TCPDF.html#var$offsets">TCPDF::$offsets</a></dd>
							<dt><b>$oldcMargin</b></dt>
				<dd>in file tcpdf.php, variable <a href="com-tecnick-tcpdf/TCPDF.html#var$oldcMargin">TCPDF::$oldcMargin</a></dd>
							<dt><b>$opencell</b></dt>
				<dd>in file tcpdf.php, variable <a href="com-tecnick-tcpdf/TCPDF.html#var$opencell">TCPDF::$opencell</a></dd>
							<dt><b>$openMarkedContent</b></dt>
				<dd>in file tcpdf.php, variable <a href="com-tecnick-tcpdf/TCPDF.html#var$openMarkedContent">TCPDF::$openMarkedContent</a><br>&nbsp;&nbsp;&nbsp;&nbsp;True if marked-content sequence is open</dd>
							<dt><b>$original_lMargin</b></dt>
				<dd>in file tcpdf.php, variable <a href="com-tecnick-tcpdf/TCPDF.html#var$original_lMargin">TCPDF::$original_lMargin</a></dd>
							<dt><b>$original_rMargin</b></dt>
				<dd>in file tcpdf.php, variable <a href="com-tecnick-tcpdf/TCPDF.html#var$original_rMargin">TCPDF::$original_rMargin</a></dd>
							<dt><b>$OutlineRoot</b></dt>
				<dd>in file tcpdf.php, variable <a href="com-tecnick-tcpdf/TCPDF.html#var$OutlineRoot">TCPDF::$OutlineRoot</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Outline root for bookmark</dd>
							<dt><b>$outlines</b></dt>
				<dd>in file tcpdf.php, variable <a href="com-tecnick-tcpdf/TCPDF.html#var$outlines">TCPDF::$outlines</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Outlines for bookmark</dd>
							<dt><b>$Ovalue</b></dt>
				<dd>in file tcpdf.php, variable <a href="com-tecnick-tcpdf/TCPDF.html#var$Ovalue">TCPDF::$Ovalue</a><br>&nbsp;&nbsp;&nbsp;&nbsp;O entry in pdf document</dd>
							<dt><b>objclone</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodobjclone">TCPDF::objclone()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Creates a copy of a class object</dd>
							<dt><b>Open</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodOpen">TCPDF::Open()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;This method begins the generation of the PDF document.</dd>
							<dt><b>openHTMLTagHandler</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodopenHTMLTagHandler">TCPDF::openHTMLTagHandler()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Process opening tags.</dd>
							<dt><b>Output</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodOutput">TCPDF::Output()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Send the document to a given destination: string, local file or browser.</dd>
					</dl>
	</div>
	<a href="elementindex_com-tecnick-tcpdf.html#top">top</a><br>
  <hr />
	<a name="p"></a>
	<div>
		<h2>p</h2>
		<dl>
							<dt><b>$padding</b></dt>
				<dd>in file tcpdf.php, variable <a href="com-tecnick-tcpdf/TCPDF.html#var$padding">TCPDF::$padding</a><br>&nbsp;&nbsp;&nbsp;&nbsp;RC4 padding</dd>
							<dt><b>$page</b></dt>
				<dd>in file tcpdf.php, variable <a href="com-tecnick-tcpdf/TCPDF.html#var$page">TCPDF::$page</a></dd>
							<dt><b>$PageAnnots</b></dt>
				<dd>in file tcpdf.php, variable <a href="com-tecnick-tcpdf/TCPDF.html#var$PageAnnots">TCPDF::$PageAnnots</a></dd>
							<dt><b>$PageBreakTrigger</b></dt>
				<dd>in file tcpdf.php, variable <a href="com-tecnick-tcpdf/TCPDF.html#var$PageBreakTrigger">TCPDF::$PageBreakTrigger</a></dd>
							<dt><b>$pagedim</b></dt>
				<dd>in file tcpdf.php, variable <a href="com-tecnick-tcpdf/TCPDF.html#var$pagedim">TCPDF::$pagedim</a></dd>
							<dt><b>$pagegroups</b></dt>
				<dd>in file tcpdf.php, variable <a href="com-tecnick-tcpdf/TCPDF.html#var$pagegroups">TCPDF::$pagegroups</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Contains the number of pages of the groups</dd>
							<dt><b>$pagelen</b></dt>
				<dd>in file tcpdf.php, variable <a href="com-tecnick-tcpdf/TCPDF.html#var$pagelen">TCPDF::$pagelen</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Array containing page lenghts in bytes.</dd>
							<dt><b>$PageMode</b></dt>
				<dd>in file tcpdf.php, variable <a href="com-tecnick-tcpdf/TCPDF.html#var$PageMode">TCPDF::$PageMode</a><br>&nbsp;&nbsp;&nbsp;&nbsp;A name object specifying how the document should be displayed when opened.</dd>
							<dt><b>$pageopen</b></dt>
				<dd>in file tcpdf.php, variable <a href="com-tecnick-tcpdf/TCPDF.html#var$pageopen">TCPDF::$pageopen</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Store the fage status (true when opened, false when closed).</dd>
							<dt><b>$pages</b></dt>
				<dd>in file tcpdf.php, variable <a href="com-tecnick-tcpdf/TCPDF.html#var$pages">TCPDF::$pages</a></dd>
							<dt><b>$PDFVersion</b></dt>
				<dd>in file tcpdf.php, variable <a href="com-tecnick-tcpdf/TCPDF.html#var$PDFVersion">TCPDF::$PDFVersion</a></dd>
							<dt><b>$premode</b></dt>
				<dd>in file tcpdf.php, variable <a href="com-tecnick-tcpdf/TCPDF.html#var$premode">TCPDF::$premode</a></dd>
							<dt><b>$print_footer</b></dt>
				<dd>in file tcpdf.php, variable <a href="com-tecnick-tcpdf/TCPDF.html#var$print_footer">TCPDF::$print_footer</a></dd>
							<dt><b>$print_header</b></dt>
				<dd>in file tcpdf.php, variable <a href="com-tecnick-tcpdf/TCPDF.html#var$print_header">TCPDF::$print_header</a></dd>
							<dt><b>$Pvalue</b></dt>
				<dd>in file tcpdf.php, variable <a href="com-tecnick-tcpdf/TCPDF.html#var$Pvalue">TCPDF::$Pvalue</a><br>&nbsp;&nbsp;&nbsp;&nbsp;P entry in pdf document</dd>
							<dt><b>PageNo</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodPageNo">TCPDF::PageNo()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Returns the current page number.</dd>
							<dt><b>PageNoFormatted</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodPageNoFormatted">TCPDF::PageNoFormatted()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Returns the current page number formatted as a string.</dd>
							<dt><b>PDF_AUTHOR</b></dt>
				<dd>in file tcpdf_config.php, constant <a href="com-tecnick-tcpdf/_config---tcpdf_config.php.html#definePDF_AUTHOR">PDF_AUTHOR</a><br>&nbsp;&nbsp;&nbsp;&nbsp;document author</dd>
							<dt><b>PDF_CREATOR</b></dt>
				<dd>in file tcpdf_config.php, constant <a href="com-tecnick-tcpdf/_config---tcpdf_config.php.html#definePDF_CREATOR">PDF_CREATOR</a><br>&nbsp;&nbsp;&nbsp;&nbsp;document creator</dd>
							<dt><b>PDF_FONT_MONOSPACED</b></dt>
				<dd>in file tcpdf_config.php, constant <a href="com-tecnick-tcpdf/_config---tcpdf_config.php.html#definePDF_FONT_MONOSPACED">PDF_FONT_MONOSPACED</a><br>&nbsp;&nbsp;&nbsp;&nbsp;default monospaced font name</dd>
							<dt><b>PDF_FONT_NAME_DATA</b></dt>
				<dd>in file tcpdf_config.php, constant <a href="com-tecnick-tcpdf/_config---tcpdf_config.php.html#definePDF_FONT_NAME_DATA">PDF_FONT_NAME_DATA</a><br>&nbsp;&nbsp;&nbsp;&nbsp;default data font name</dd>
							<dt><b>PDF_FONT_NAME_MAIN</b></dt>
				<dd>in file tcpdf_config.php, constant <a href="com-tecnick-tcpdf/_config---tcpdf_config.php.html#definePDF_FONT_NAME_MAIN">PDF_FONT_NAME_MAIN</a><br>&nbsp;&nbsp;&nbsp;&nbsp;default main font name</dd>
							<dt><b>PDF_FONT_SIZE_DATA</b></dt>
				<dd>in file tcpdf_config.php, constant <a href="com-tecnick-tcpdf/_config---tcpdf_config.php.html#definePDF_FONT_SIZE_DATA">PDF_FONT_SIZE_DATA</a><br>&nbsp;&nbsp;&nbsp;&nbsp;default data font size</dd>
							<dt><b>PDF_FONT_SIZE_MAIN</b></dt>
				<dd>in file tcpdf_config.php, constant <a href="com-tecnick-tcpdf/_config---tcpdf_config.php.html#definePDF_FONT_SIZE_MAIN">PDF_FONT_SIZE_MAIN</a><br>&nbsp;&nbsp;&nbsp;&nbsp;default main font size</dd>
							<dt><b>PDF_HEADER_LOGO</b></dt>
				<dd>in file tcpdf_config.php, constant <a href="com-tecnick-tcpdf/_config---tcpdf_config.php.html#definePDF_HEADER_LOGO">PDF_HEADER_LOGO</a><br>&nbsp;&nbsp;&nbsp;&nbsp;image logo</dd>
							<dt><b>PDF_HEADER_LOGO_WIDTH</b></dt>
				<dd>in file tcpdf_config.php, constant <a href="com-tecnick-tcpdf/_config---tcpdf_config.php.html#definePDF_HEADER_LOGO_WIDTH">PDF_HEADER_LOGO_WIDTH</a><br>&nbsp;&nbsp;&nbsp;&nbsp;header logo image width [mm]</dd>
							<dt><b>PDF_HEADER_STRING</b></dt>
				<dd>in file tcpdf_config.php, constant <a href="com-tecnick-tcpdf/_config---tcpdf_config.php.html#definePDF_HEADER_STRING">PDF_HEADER_STRING</a><br>&nbsp;&nbsp;&nbsp;&nbsp;header description string</dd>
							<dt><b>PDF_HEADER_TITLE</b></dt>
				<dd>in file tcpdf_config.php, constant <a href="com-tecnick-tcpdf/_config---tcpdf_config.php.html#definePDF_HEADER_TITLE">PDF_HEADER_TITLE</a><br>&nbsp;&nbsp;&nbsp;&nbsp;header title</dd>
							<dt><b>PDF_IMAGE_SCALE_RATIO</b></dt>
				<dd>in file tcpdf_config.php, constant <a href="com-tecnick-tcpdf/_config---tcpdf_config.php.html#definePDF_IMAGE_SCALE_RATIO">PDF_IMAGE_SCALE_RATIO</a><br>&nbsp;&nbsp;&nbsp;&nbsp;ratio used to adjust the conversion of pixels to user units</dd>
							<dt><b>PDF_MARGIN_BOTTOM</b></dt>
				<dd>in file tcpdf_config.php, constant <a href="com-tecnick-tcpdf/_config---tcpdf_config.php.html#definePDF_MARGIN_BOTTOM">PDF_MARGIN_BOTTOM</a><br>&nbsp;&nbsp;&nbsp;&nbsp;bottom margin</dd>
							<dt><b>PDF_MARGIN_FOOTER</b></dt>
				<dd>in file tcpdf_config.php, constant <a href="com-tecnick-tcpdf/_config---tcpdf_config.php.html#definePDF_MARGIN_FOOTER">PDF_MARGIN_FOOTER</a><br>&nbsp;&nbsp;&nbsp;&nbsp;footer margin</dd>
							<dt><b>PDF_MARGIN_HEADER</b></dt>
				<dd>in file tcpdf_config.php, constant <a href="com-tecnick-tcpdf/_config---tcpdf_config.php.html#definePDF_MARGIN_HEADER">PDF_MARGIN_HEADER</a><br>&nbsp;&nbsp;&nbsp;&nbsp;header margin</dd>
							<dt><b>PDF_MARGIN_LEFT</b></dt>
				<dd>in file tcpdf_config.php, constant <a href="com-tecnick-tcpdf/_config---tcpdf_config.php.html#definePDF_MARGIN_LEFT">PDF_MARGIN_LEFT</a><br>&nbsp;&nbsp;&nbsp;&nbsp;left margin</dd>
							<dt><b>PDF_MARGIN_RIGHT</b></dt>
				<dd>in file tcpdf_config.php, constant <a href="com-tecnick-tcpdf/_config---tcpdf_config.php.html#definePDF_MARGIN_RIGHT">PDF_MARGIN_RIGHT</a><br>&nbsp;&nbsp;&nbsp;&nbsp;right margin</dd>
							<dt><b>PDF_MARGIN_TOP</b></dt>
				<dd>in file tcpdf_config.php, constant <a href="com-tecnick-tcpdf/_config---tcpdf_config.php.html#definePDF_MARGIN_TOP">PDF_MARGIN_TOP</a><br>&nbsp;&nbsp;&nbsp;&nbsp;top margin</dd>
							<dt><b>PDF_PAGE_FORMAT</b></dt>
				<dd>in file tcpdf_config.php, constant <a href="com-tecnick-tcpdf/_config---tcpdf_config.php.html#definePDF_PAGE_FORMAT">PDF_PAGE_FORMAT</a><br>&nbsp;&nbsp;&nbsp;&nbsp;page format</dd>
							<dt><b>PDF_PAGE_ORIENTATION</b></dt>
				<dd>in file tcpdf_config.php, constant <a href="com-tecnick-tcpdf/_config---tcpdf_config.php.html#definePDF_PAGE_ORIENTATION">PDF_PAGE_ORIENTATION</a><br>&nbsp;&nbsp;&nbsp;&nbsp;page orientation (P=portrait, L=landscape)</dd>
							<dt><b>PDF_PRODUCER</b></dt>
				<dd>in file tcpdf.php, constant <a href="com-tecnick-tcpdf/_tcpdf.php.html#definePDF_PRODUCER">PDF_PRODUCER</a><br>&nbsp;&nbsp;&nbsp;&nbsp;define default PDF document producer</dd>
							<dt><b>PDF_UNIT</b></dt>
				<dd>in file tcpdf_config.php, constant <a href="com-tecnick-tcpdf/_config---tcpdf_config.php.html#definePDF_UNIT">PDF_UNIT</a><br>&nbsp;&nbsp;&nbsp;&nbsp;document unit of measure [pt=point, mm=millimeter, cm=centimeter, in=inch]</dd>
							<dt><b>PieSector</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodPieSector">TCPDF::PieSector()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Draw the sector of a circle.</dd>
							<dt><b>pixelsToUnits</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodpixelsToUnits">TCPDF::pixelsToUnits()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Converts pixels to User's Units.</dd>
							<dt><b>Polycurve</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodPolycurve">TCPDF::Polycurve()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Draws a poly-Bezier curve.</dd>
							<dt><b>Polygon</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodPolygon">TCPDF::Polygon()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Draws a polygon.</dd>
							<dt><b>putHtmlListBullet</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodputHtmlListBullet">TCPDF::putHtmlListBullet()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Output an HTML list bullet or ordered item symbol</dd>
					</dl>
	</div>
	<a href="elementindex_com-tecnick-tcpdf.html#top">top</a><br>
  <hr />
	<a name="r"></a>
	<div>
		<h2>r</h2>
		<dl>
							<dt><b>$re_spaces</b></dt>
				<dd>in file tcpdf.php, variable <a href="com-tecnick-tcpdf/TCPDF.html#var$re_spaces">TCPDF::$re_spaces</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Regular expression used to find blank characters used for word-wrapping.</dd>
							<dt><b>$rMargin</b></dt>
				<dd>in file tcpdf.php, variable <a href="com-tecnick-tcpdf/TCPDF.html#var$rMargin">TCPDF::$rMargin</a></dd>
							<dt><b>$rtl</b></dt>
				<dd>in file tcpdf.php, variable <a href="com-tecnick-tcpdf/TCPDF.html#var$rtl">TCPDF::$rtl</a></dd>
							<dt><b>RadialGradient</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodRadialGradient">TCPDF::RadialGradient()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Paints a radial colour gradient.</dd>
							<dt><b>RadioButton</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodRadioButton">TCPDF::RadioButton()</a></dd>
							<dt><b>readDiskCache</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodreadDiskCache">TCPDF::readDiskCache()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Read data from a temporary file on filesystem.</dd>
							<dt><b>Rect</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodRect">TCPDF::Rect()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Draws a rectangle.</dd>
							<dt><b>RegularPolygon</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodRegularPolygon">TCPDF::RegularPolygon()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Draws a regular polygon.</dd>
							<dt><b>removeSHY</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodremoveSHY">TCPDF::removeSHY()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Removes SHY characters from text.</dd>
							<dt><b>rfread</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodrfread">TCPDF::rfread()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Binary-safe and URL-safe file read.</dd>
							<dt><b>rollbackTransaction</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodrollbackTransaction">TCPDF::rollbackTransaction()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;This method allows to undo the latest transaction by returning the latest saved TCPDF object with startTransaction().</dd>
							<dt><b>Rotate</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodRotate">TCPDF::Rotate()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Rotate object.</dd>
							<dt><b>RoundedRect</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodRoundedRect">TCPDF::RoundedRect()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Draws a rounded rectangle.</dd>
					</dl>
	</div>
	<a href="elementindex_com-tecnick-tcpdf.html#top">top</a><br>
  <hr />
	<a name="s"></a>
	<div>
		<h2>s</h2>
		<dl>
							<dt><b>$sign</b></dt>
				<dd>in file tcpdf.php, variable <a href="com-tecnick-tcpdf/TCPDF.html#var$sign">TCPDF::$sign</a><br>&nbsp;&nbsp;&nbsp;&nbsp;If true enables document signing</dd>
							<dt><b>$signature_data</b></dt>
				<dd>in file tcpdf.php, variable <a href="com-tecnick-tcpdf/TCPDF.html#var$signature_data">TCPDF::$signature_data</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Signature data</dd>
							<dt><b>$signature_max_lenght</b></dt>
				<dd>in file tcpdf.php, variable <a href="com-tecnick-tcpdf/TCPDF.html#var$signature_max_lenght">TCPDF::$signature_max_lenght</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Signature max lenght</dd>
							<dt><b>$spot_colors</b></dt>
				<dd>in file tcpdf.php, variable <a href="com-tecnick-tcpdf/TCPDF.html#var$spot_colors">TCPDF::$spot_colors</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Array of Spot colors</dd>
							<dt><b>$state</b></dt>
				<dd>in file tcpdf.php, variable <a href="com-tecnick-tcpdf/TCPDF.html#var$state">TCPDF::$state</a></dd>
							<dt><b>$subject</b></dt>
				<dd>in file tcpdf.php, variable <a href="com-tecnick-tcpdf/TCPDF.html#var$subject">TCPDF::$subject</a></dd>
							<dt><b>Scale</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodScale">TCPDF::Scale()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Vertical and horizontal non-proportional Scaling.</dd>
							<dt><b>ScaleX</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodScaleX">TCPDF::ScaleX()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Horizontal Scaling.</dd>
							<dt><b>ScaleXY</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodScaleXY">TCPDF::ScaleXY()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Vertical and horizontal proportional Scaling.</dd>
							<dt><b>ScaleY</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodScaleY">TCPDF::ScaleY()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Vertical Scaling.</dd>
							<dt><b>setAlpha</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodsetAlpha">TCPDF::setAlpha()</a></dd>
							<dt><b>SetAuthor</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodSetAuthor">TCPDF::SetAuthor()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Defines the author of the document.</dd>
							<dt><b>SetAutoPageBreak</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodSetAutoPageBreak">TCPDF::SetAutoPageBreak()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Enables or disables the automatic page breaking mode. When enabling, the second parameter is the distance from the bottom of the page that defines the triggering limit. By default, the mode is on and the margin is 2 cm.</dd>
							<dt><b>setBarcode</b></dt>
				<dd>in file barcodes.php, method <a href="com-tecnick-tcpdf/TCPDFBarcode.html#methodsetBarcode">TCPDFBarcode::setBarcode()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Set the barcode.</dd>
							<dt><b>setBarcode</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodsetBarcode">TCPDF::setBarcode()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Set document barcode.</dd>
							<dt><b>SetBooklet</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodSetBooklet">TCPDF::SetBooklet()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Set the booklet mode for double-sided pages.</dd>
							<dt><b>setBuffer</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodsetBuffer">TCPDF::setBuffer()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Set buffer content (always append data).</dd>
							<dt><b>setCellHeightRatio</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodsetCellHeightRatio">TCPDF::setCellHeightRatio()</a></dd>
							<dt><b>SetCellPadding</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodSetCellPadding">TCPDF::SetCellPadding()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Set the internal Cell padding.</dd>
							<dt><b>SetCompression</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodSetCompression">TCPDF::SetCompression()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Activates or deactivates page compression. When activated, the internal representation of each page is compressed, which leads to a compression ratio of about 2 for the resulting document. Compression is on by default.</dd>
							<dt><b>SetCreator</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodSetCreator">TCPDF::SetCreator()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Defines the creator of the document. This is typically the name of the application that generates the PDF.</dd>
							<dt><b>SetDefaultMonospacedFont</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodSetDefaultMonospacedFont">TCPDF::SetDefaultMonospacedFont()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Defines the default monospaced font.</dd>
							<dt><b>setDefaultTableColumns</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodsetDefaultTableColumns">TCPDF::setDefaultTableColumns()</a></dd>
							<dt><b>SetDisplayMode</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodSetDisplayMode">TCPDF::SetDisplayMode()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Defines the way the document is to be displayed by the viewer.</dd>
							<dt><b>SetDrawColor</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodSetDrawColor">TCPDF::SetDrawColor()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Defines the color used for all drawing operations (lines, rectangles and cell borders). It can be expressed in RGB components or gray scale. The method can be called before the first page is created and the value is retained from page to page.</dd>
							<dt><b>SetDrawColorArray</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodSetDrawColorArray">TCPDF::SetDrawColorArray()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Defines the color used for all drawing operations (lines, rectangles and cell borders).</dd>
							<dt><b>SetDrawSpotColor</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodSetDrawSpotColor">TCPDF::SetDrawSpotColor()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Defines the spot color used for all drawing operations (lines, rectangles and cell borders).</dd>
							<dt><b>setExtGState</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodsetExtGState">TCPDF::setExtGState()</a></dd>
							<dt><b>SetFillColor</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodSetFillColor">TCPDF::SetFillColor()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Defines the color used for all filling operations (filled rectangles and cell backgrounds). It can be expressed in RGB components or gray scale. The method can be called before the first page is created and the value is retained from page to page.</dd>
							<dt><b>SetFillColorArray</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodSetFillColorArray">TCPDF::SetFillColorArray()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Defines the color used for all filling operations (filled rectangles and cell backgrounds).</dd>
							<dt><b>SetFillSpotColor</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodSetFillSpotColor">TCPDF::SetFillSpotColor()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Defines the spot color used for all filling operations (filled rectangles and cell backgrounds).</dd>
							<dt><b>SetFont</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodSetFont">TCPDF::SetFont()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Sets the font used to print character strings.</dd>
							<dt><b>setFontBuffer</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodsetFontBuffer">TCPDF::setFontBuffer()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Set font buffer content.</dd>
							<dt><b>SetFontSize</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodSetFontSize">TCPDF::SetFontSize()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Defines the size of the current font.</dd>
							<dt><b>setFontSubBuffer</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodsetFontSubBuffer">TCPDF::setFontSubBuffer()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Set font buffer content.</dd>
							<dt><b>setFooter</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodsetFooter">TCPDF::setFooter()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;This method is used to render the page footer.</dd>
							<dt><b>setFooterFont</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodsetFooterFont">TCPDF::setFooterFont()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Set footer font.</dd>
							<dt><b>setFooterMargin</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodsetFooterMargin">TCPDF::setFooterMargin()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Set footer margin.</dd>
							<dt><b>setGraphicVars</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodsetGraphicVars">TCPDF::setGraphicVars()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Set graphic variables.</dd>
							<dt><b>setHeader</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodsetHeader">TCPDF::setHeader()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;This method is used to render the page header.</dd>
							<dt><b>setHeaderData</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodsetHeaderData">TCPDF::setHeaderData()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Set header data.</dd>
							<dt><b>setHeaderFont</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodsetHeaderFont">TCPDF::setHeaderFont()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Set header font.</dd>
							<dt><b>setHeaderMargin</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodsetHeaderMargin">TCPDF::setHeaderMargin()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Set header margin.</dd>
							<dt><b>setHtmlLinksStyle</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodsetHtmlLinksStyle">TCPDF::setHtmlLinksStyle()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Set the color and font style for HTML links.</dd>
							<dt><b>setHtmlVSpace</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodsetHtmlVSpace">TCPDF::setHtmlVSpace()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Set the vertical spaces for HTML tags.</dd>
							<dt><b>setImageBuffer</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodsetImageBuffer">TCPDF::setImageBuffer()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Set image buffer content.</dd>
							<dt><b>setImageScale</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodsetImageScale">TCPDF::setImageScale()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Set the adjusting factor to convert pixels to user units.</dd>
							<dt><b>setImageSubBuffer</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodsetImageSubBuffer">TCPDF::setImageSubBuffer()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Set image buffer content.</dd>
							<dt><b>setJPEGQuality</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodsetJPEGQuality">TCPDF::setJPEGQuality()</a></dd>
							<dt><b>SetKeywords</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodSetKeywords">TCPDF::SetKeywords()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Associates keywords with the document, generally in the form 'keyword1 keyword2 ...'.</dd>
							<dt><b>setLanguageArray</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodsetLanguageArray">TCPDF::setLanguageArray()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Set language array.</dd>
							<dt><b>setLastH</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodsetLastH">TCPDF::setLastH()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Set the last cell height.</dd>
							<dt><b>SetLeftMargin</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodSetLeftMargin">TCPDF::SetLeftMargin()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Defines the left margin. The method can be called before creating the first page. If the current abscissa gets out of page, it is brought back to the margin.</dd>
							<dt><b>SetLineStyle</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodSetLineStyle">TCPDF::SetLineStyle()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Set line style.</dd>
							<dt><b>SetLineWidth</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodSetLineWidth">TCPDF::SetLineWidth()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Defines the line width. By default, the value equals 0.2 mm. The method can be called before the first page is created and the value is retained from page to page.</dd>
							<dt><b>SetLink</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodSetLink">TCPDF::SetLink()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Defines the page and position a link points to.</dd>
							<dt><b>setListIndentWidth</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodsetListIndentWidth">TCPDF::setListIndentWidth()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Set custom width for list indentation.</dd>
							<dt><b>setLIsymbol</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodsetLIsymbol">TCPDF::setLIsymbol()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Set the default bullet to be used as LI bullet symbol</dd>
							<dt><b>SetMargins</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodSetMargins">TCPDF::SetMargins()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Defines the left, top and right margins. By default, they equal 1 cm. Call this method to change them.</dd>
							<dt><b>setOpenCell</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodsetOpenCell">TCPDF::setOpenCell()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Set the top/bottom cell sides to be open or closed when the cell cross the page.</dd>
							<dt><b>setPage</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodsetPage">TCPDF::setPage()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Move pointer at the specified document page and update page dimensions.</dd>
							<dt><b>setPageBuffer</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodsetPageBuffer">TCPDF::setPageBuffer()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Set page buffer content.</dd>
							<dt><b>setPageFormat</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodsetPageFormat">TCPDF::setPageFormat()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Set the page format</dd>
							<dt><b>setPageMark</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodsetPageMark">TCPDF::setPageMark()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Set start-writing mark on current page for multicell borders and fills.</dd>
							<dt><b>setPageOrientation</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodsetPageOrientation">TCPDF::setPageOrientation()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Set page orientation.</dd>
							<dt><b>setPageUnit</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodsetPageUnit">TCPDF::setPageUnit()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Set the units of measure for the document.</dd>
							<dt><b>setPDFVersion</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodsetPDFVersion">TCPDF::setPDFVersion()</a></dd>
							<dt><b>setPrintFooter</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodsetPrintFooter">TCPDF::setPrintFooter()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Set a flag to print page footer.</dd>
							<dt><b>setPrintHeader</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodsetPrintHeader">TCPDF::setPrintHeader()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Set a flag to print page header.</dd>
							<dt><b>SetProtection</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodSetProtection">TCPDF::SetProtection()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Set document protection</dd>
							<dt><b>SetRightMargin</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodSetRightMargin">TCPDF::SetRightMargin()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Defines the right margin. The method can be called before creating the first page.</dd>
							<dt><b>setRTL</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodsetRTL">TCPDF::setRTL()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Enable or disable Right-To-Left language mode</dd>
							<dt><b>setSignature</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodsetSignature">TCPDF::setSignature()</a></dd>
							<dt><b>SetSubject</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodSetSubject">TCPDF::SetSubject()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Defines the subject of the document.</dd>
							<dt><b>setTableHeader</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodsetTableHeader">TCPDF::setTableHeader()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;This method is used to render the table header on new page (if any).</dd>
							<dt><b>setTempRTL</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodsetTempRTL">TCPDF::setTempRTL()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Force temporary RTL language direction</dd>
							<dt><b>SetTextColor</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodSetTextColor">TCPDF::SetTextColor()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Defines the color used for text. It can be expressed in RGB components or gray scale. The method can be called before the first page is created and the value is retained from page to page.</dd>
							<dt><b>SetTextColorArray</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodSetTextColorArray">TCPDF::SetTextColorArray()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Defines the color used for text. It can be expressed in RGB components or gray scale.</dd>
							<dt><b>SetTextSpotColor</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodSetTextSpotColor">TCPDF::SetTextSpotColor()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Defines the spot color used for text.</dd>
							<dt><b>SetTitle</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodSetTitle">TCPDF::SetTitle()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Defines the title of the document.</dd>
							<dt><b>SetTopMargin</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodSetTopMargin">TCPDF::SetTopMargin()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Defines the top margin. The method can be called before creating the first page.</dd>
							<dt><b>setUserRights</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodsetUserRights">TCPDF::setUserRights()</a></dd>
							<dt><b>setViewerPreferences</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodsetViewerPreferences">TCPDF::setViewerPreferences()</a></dd>
							<dt><b>setVisibility</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodsetVisibility">TCPDF::setVisibility()</a></dd>
							<dt><b>SetX</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodSetX">TCPDF::SetX()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Defines the abscissa of the current position.</dd>
							<dt><b>SetXY</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodSetXY">TCPDF::SetXY()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Defines the abscissa and ordinate of the current position.</dd>
							<dt><b>SetY</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodSetY">TCPDF::SetY()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Moves the current abscissa back to the left margin and sets the ordinate.</dd>
							<dt><b>Skew</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodSkew">TCPDF::Skew()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Skew.</dd>
							<dt><b>SkewX</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodSkewX">TCPDF::SkewX()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Skew horizontally.</dd>
							<dt><b>SkewY</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodSkewY">TCPDF::SkewY()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Skew vertically.</dd>
							<dt><b>StarPolygon</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodStarPolygon">TCPDF::StarPolygon()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Draws a star polygon</dd>
							<dt><b>startPage</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodstartPage">TCPDF::startPage()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Starts a new page to the document. The page must be closed using the endPage() function.</dd>
							<dt><b>startPageGroup</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodstartPageGroup">TCPDF::startPageGroup()</a></dd>
							<dt><b>startTransaction</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodstartTransaction">TCPDF::startTransaction()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Stores a copy of the current TCPDF object used for undo operation.</dd>
							<dt><b>StartTransform</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodStartTransform">TCPDF::StartTransform()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Starts a 2D tranformation saving current graphic state.</dd>
							<dt><b>StopTransform</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodStopTransform">TCPDF::StopTransform()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Stops a 2D tranformation restoring previous graphic state.</dd>
							<dt><b>swapMargins</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodswapMargins">TCPDF::swapMargins()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Swap the left and right margins.</dd>
					</dl>
	</div>
	<a href="elementindex_com-tecnick-tcpdf.html#top">top</a><br>
  <hr />
	<a name="t"></a>
	<div>
		<h2>t</h2>
		<dl>
							<dt><b>$tagvspaces</b></dt>
				<dd>in file tcpdf.php, variable <a href="com-tecnick-tcpdf/TCPDF.html#var$tagvspaces">TCPDF::$tagvspaces</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Array used for custom vertical spaces for HTML tags</dd>
							<dt><b>$tempfontsize</b></dt>
				<dd>in file tcpdf.php, variable <a href="com-tecnick-tcpdf/TCPDF.html#var$tempfontsize">TCPDF::$tempfontsize</a></dd>
							<dt><b>$TextColor</b></dt>
				<dd>in file tcpdf.php, variable <a href="com-tecnick-tcpdf/TCPDF.html#var$TextColor">TCPDF::$TextColor</a></dd>
							<dt><b>$thead</b></dt>
				<dd>in file tcpdf.php, variable <a href="com-tecnick-tcpdf/TCPDF.html#var$thead">TCPDF::$thead</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Table header content to be repeated on each new page</dd>
							<dt><b>$theadMargin</b></dt>
				<dd>in file tcpdf.php, variable <a href="com-tecnick-tcpdf/TCPDF.html#var$theadMargin">TCPDF::$theadMargin</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Distance between the top of page and end of table headers on a new page.</dd>
							<dt><b>$title</b></dt>
				<dd>in file tcpdf.php, variable <a href="com-tecnick-tcpdf/TCPDF.html#var$title">TCPDF::$title</a></dd>
							<dt><b>$tMargin</b></dt>
				<dd>in file tcpdf.php, variable <a href="com-tecnick-tcpdf/TCPDF.html#var$tMargin">TCPDF::$tMargin</a></dd>
							<dt><b>$tmprtl</b></dt>
				<dd>in file tcpdf.php, variable <a href="com-tecnick-tcpdf/TCPDF.html#var$tmprtl">TCPDF::$tmprtl</a></dd>
							<dt><b>$transfmatrix</b></dt>
				<dd>in file tcpdf.php, variable <a href="com-tecnick-tcpdf/TCPDF.html#var$transfmatrix">TCPDF::$transfmatrix</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Array of transformation matrix</dd>
							<dt><b>$transfmrk</b></dt>
				<dd>in file tcpdf.php, variable <a href="com-tecnick-tcpdf/TCPDF.html#var$transfmrk">TCPDF::$transfmrk</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Array used to store positions of graphics transformation blocks inside the page buffer.</dd>
							<dt><b>tcpdf_config.php</b></dt>
				<dd>procedural page <a href="com-tecnick-tcpdf/_config---tcpdf_config.php.html">tcpdf_config.php</a></dd>
							<dt><b>TCPDF</b></dt>
				<dd>in file tcpdf.php, class <a href="com-tecnick-tcpdf/TCPDF.html">TCPDF</a><br>&nbsp;&nbsp;&nbsp;&nbsp;This is a PHP class for generating PDF documents without requiring external extensions.<br /></dd>
							<dt><b>tcpdf.php</b></dt>
				<dd>procedural page <a href="com-tecnick-tcpdf/_tcpdf.php.html">tcpdf.php</a></dd>
							<dt><b>TCPDFBarcode</b></dt>
				<dd>in file barcodes.php, class <a href="com-tecnick-tcpdf/TCPDFBarcode.html">TCPDFBarcode</a><br>&nbsp;&nbsp;&nbsp;&nbsp;PHP class to creates array representations for common 1D barcodes to be used with TCPDF (http://www.tcpdf.org).<br /></dd>
							<dt><b>Text</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodText">TCPDF::Text()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Prints a character string.</dd>
							<dt><b>TextField</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodTextField">TCPDF::TextField()</a></dd>
							<dt><b>Transform</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodTransform">TCPDF::Transform()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Apply graphic transformations.</dd>
							<dt><b>Translate</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodTranslate">TCPDF::Translate()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Translate graphic object horizontally and vertically.</dd>
							<dt><b>TranslateX</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodTranslateX">TCPDF::TranslateX()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Translate graphic object horizontally.</dd>
							<dt><b>TranslateY</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodTranslateY">TCPDF::TranslateY()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Translate graphic object vertically.</dd>
					</dl>
	</div>
	<a href="elementindex_com-tecnick-tcpdf.html#top">top</a><br>
  <hr />
	<a name="u"></a>
	<div>
		<h2>u</h2>
		<dl>
							<dt><b>$underline</b></dt>
				<dd>in file tcpdf.php, variable <a href="com-tecnick-tcpdf/TCPDF.html#var$underline">TCPDF::$underline</a></dd>
							<dt><b>$ur</b></dt>
				<dd>in file tcpdf.php, variable <a href="com-tecnick-tcpdf/TCPDF.html#var$ur">TCPDF::$ur</a><br>&nbsp;&nbsp;&nbsp;&nbsp;If true enables user's rights on PDF reader</dd>
							<dt><b>$ur_annots</b></dt>
				<dd>in file tcpdf.php, variable <a href="com-tecnick-tcpdf/TCPDF.html#var$ur_annots">TCPDF::$ur_annots</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Names specifying additional annotation-related usage rights for the document.</dd>
							<dt><b>$ur_document</b></dt>
				<dd>in file tcpdf.php, variable <a href="com-tecnick-tcpdf/TCPDF.html#var$ur_document">TCPDF::$ur_document</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Names specifying additional document-wide usage rights for the document.</dd>
							<dt><b>$ur_form</b></dt>
				<dd>in file tcpdf.php, variable <a href="com-tecnick-tcpdf/TCPDF.html#var$ur_form">TCPDF::$ur_form</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Names specifying additional form-field-related usage rights for the document.</dd>
							<dt><b>$ur_signature</b></dt>
				<dd>in file tcpdf.php, variable <a href="com-tecnick-tcpdf/TCPDF.html#var$ur_signature">TCPDF::$ur_signature</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Names specifying additional signature-related usage rights for the document.</dd>
							<dt><b>$Uvalue</b></dt>
				<dd>in file tcpdf.php, variable <a href="com-tecnick-tcpdf/TCPDF.html#var$Uvalue">TCPDF::$Uvalue</a><br>&nbsp;&nbsp;&nbsp;&nbsp;U entry in pdf document</dd>
							<dt><b>unhtmlentities</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodunhtmlentities">TCPDF::unhtmlentities()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Reverse function for htmlentities.</dd>
							<dt><b>UniArrSubString</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodUniArrSubString">TCPDF::UniArrSubString()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Extract a slice of the $uniarr array and return it as string.</dd>
							<dt><b>unichr</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodunichr">TCPDF::unichr()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Returns the unicode caracter specified by UTF-8 code</dd>
							<dt><b>unicode_data.php</b></dt>
				<dd>procedural page <a href="com-tecnick-tcpdf/_unicode_data.php.html">unicode_data.php</a></dd>
							<dt><b>UTF8ArrayToUniArray</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodUTF8ArrayToUniArray">TCPDF::UTF8ArrayToUniArray()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Convert an array of UTF8 values to array of unicode characters</dd>
							<dt><b>UTF8ArrSubString</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodUTF8ArrSubString">TCPDF::UTF8ArrSubString()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Extract a slice of the $strarr array and return it as string.</dd>
							<dt><b>utf8Bidi</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodutf8Bidi">TCPDF::utf8Bidi()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Reverse the RLT substrings using the Bidirectional Algorithm (http://unicode.org/reports/tr9/).</dd>
							<dt><b>UTF8StringToArray</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodUTF8StringToArray">TCPDF::UTF8StringToArray()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Converts UTF-8 strings to codepoints array.<br /></dd>
							<dt><b>utf8StrRev</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodutf8StrRev">TCPDF::utf8StrRev()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Reverse the RLT substrings using the Bidirectional Algorithm (http://unicode.org/reports/tr9/).</dd>
							<dt><b>UTF8ToLatin1</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodUTF8ToLatin1">TCPDF::UTF8ToLatin1()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Converts UTF-8 strings to Latin1 when using the standard 14 core fonts.<br /></dd>
							<dt><b>UTF8ToUTF16BE</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodUTF8ToUTF16BE">TCPDF::UTF8ToUTF16BE()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Converts UTF-8 strings to UTF16-BE.<br /></dd>
					</dl>
	</div>
	<a href="elementindex_com-tecnick-tcpdf.html#top">top</a><br>
  <hr />
	<a name="v"></a>
	<div>
		<h2>v</h2>
		<dl>
							<dt><b>$viewer_preferences</b></dt>
				<dd>in file tcpdf.php, variable <a href="com-tecnick-tcpdf/TCPDF.html#var$viewer_preferences">TCPDF::$viewer_preferences</a><br>&nbsp;&nbsp;&nbsp;&nbsp;PDF viewer preferences.</dd>
							<dt><b>$visibility</b></dt>
				<dd>in file tcpdf.php, variable <a href="com-tecnick-tcpdf/TCPDF.html#var$visibility">TCPDF::$visibility</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Restrict the rendering of some elements to screen or printout.</dd>
					</dl>
	</div>
	<a href="elementindex_com-tecnick-tcpdf.html#top">top</a><br>
  <hr />
	<a name="w"></a>
	<div>
		<h2>w</h2>
		<dl>
							<dt><b>$w</b></dt>
				<dd>in file tcpdf.php, variable <a href="com-tecnick-tcpdf/TCPDF.html#var$w">TCPDF::$w</a></dd>
							<dt><b>$wPt</b></dt>
				<dd>in file tcpdf.php, variable <a href="com-tecnick-tcpdf/TCPDF.html#var$wPt">TCPDF::$wPt</a></dd>
							<dt><b>Write</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodWrite">TCPDF::Write()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;This method prints text from the current position.<br /></dd>
							<dt><b>write1DBarcode</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodwrite1DBarcode">TCPDF::write1DBarcode()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Print a Linear Barcode.</dd>
							<dt><b>write2DBarcode</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodwrite2DBarcode">TCPDF::write2DBarcode()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Print 2D Barcode.</dd>
							<dt><b>writeBarcode</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodwriteBarcode">TCPDF::writeBarcode()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;This function is DEPRECATED, please use the new write1DBarcode() function.</dd>
							<dt><b>writeDiskCache</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodwriteDiskCache">TCPDF::writeDiskCache()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Writes data to a temporary file on filesystem.</dd>
							<dt><b>writeHTML</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodwriteHTML">TCPDF::writeHTML()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Allows to preserve some HTML formatting (limited support).<br />  IMPORTANT: The HTML must be well formatted - try to clean-up it using an application like HTML-Tidy before submitting.</dd>
							<dt><b>writeHTMLCell</b></dt>
				<dd>in file tcpdf.php, method <a href="com-tecnick-tcpdf/TCPDF.html#methodwriteHTMLCell">TCPDF::writeHTMLCell()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Prints a cell (rectangular area) with optional borders, background color and html text string.</dd>
					</dl>
	</div>
	<a href="elementindex_com-tecnick-tcpdf.html#top">top</a><br>
  <hr />
	<a name="x"></a>
	<div>
		<h2>x</h2>
		<dl>
							<dt><b>$x</b></dt>
				<dd>in file tcpdf.php, variable <a href="com-tecnick-tcpdf/TCPDF.html#var$x">TCPDF::$x</a></dd>
					</dl>
	</div>
	<a href="elementindex_com-tecnick-tcpdf.html#top">top</a><br>
  <hr />
	<a name="y"></a>
	<div>
		<h2>y</h2>
		<dl>
							<dt><b>$y</b></dt>
				<dd>in file tcpdf.php, variable <a href="com-tecnick-tcpdf/TCPDF.html#var$y">TCPDF::$y</a></dd>
					</dl>
	</div>
	<a href="elementindex_com-tecnick-tcpdf.html#top">top</a><br>
  <hr />
	<a name="z"></a>
	<div>
		<h2>z</h2>
		<dl>
							<dt><b>$ZoomMode</b></dt>
				<dd>in file tcpdf.php, variable <a href="com-tecnick-tcpdf/TCPDF.html#var$ZoomMode">TCPDF::$ZoomMode</a></dd>
					</dl>
	</div>
	<a href="elementindex_com-tecnick-tcpdf.html#top">top</a><br>
        <div class="credit">
		    <hr />
		    Documentation generated on Wed, 13 May 2009 13:03:42 +0200 by <a href="http://www.phpdoc.org">phpDocumentor 1.4.1</a>
	      </div>
      </td></tr></table>
    </td>
  </tr>
</table>

</body>
</html>