<?php

class addliteratura_gClass extends additem_gClass
{
    protected function initParams_gFunc()
    {
        $this->setParam_gFunc('formtype-add','add_item');
        $this->setParam_gFunc('formtype-edit', 'edit_item');
        $this->setParam_gFunc('itemtype', 'literatura');
    }

    protected function saveData_gFunc()
    {
        $newData_pVar = array();
        $newData_pVar['autor'] = $this->getFieldValue_gFunc('literatura_autor');
        $newData_pVar['name'] = $this->getFieldValue_gFunc('literatura_name');
        $newData_pVar['vydavatelstvo'] = $this->getFieldValue_gFunc('literatura_vydavatelstvo');
        $newData_pVar['year'] = $this->getFieldValue_gFunc('literatura_year');
        $newData_pVar['info'] = $this->getFieldValue_gFunc('literatura_info');
        $newData_pVar['status'] = 'active';
        $newData_pVar['link'] = $this->getFieldValue_gFunc('literatura_link');
        $newName_pVar = implode(', ', $newData_pVar);

        $item_id_pVar = intval($this->getFieldValue_gFunc('literatura_item_id'));
        $old_item_id_pVar = $item_id_pVar;
        if($item_id_pVar) {
            $sql_pVar =  'SELECT `qmd5` FROM `%titems_literatura__data` WHERE `item_id` = %d';
            $old_md5_pVar = db_public_gClass::getField_gFunc($sql_pVar, __FILE__, __LINE__, $item_id_pVar);
            $newData_pVar['item_id'] = $item_id_pVar;
        }

        $item = $newData_pVar;
        $item['search_name']=$newName_pVar;
        $item['qmd5']=md5($newName_pVar);


        $item_id_pVar = items_gClass::saveOrUpdateItem_gFunc('literatura', $item);
        $this->uploadFiles_gFunc('items_literatura_', 'secured', $item_id_pVar);


        //items_gClass::editItemByForm_gFunc($this->params['itemtype'], $this, array('search_name'=>$newName_pVar, 'qmd5'=>md5($newName_pVar)));

        //$item_id_pVar = $this->getVar_gFunc('item_id');

        $items_all_pVar = main_gClass::getSessionData_gFunc('selected_items', array());
        if(isset($items_all_pVar['literatura']) && $item_id_pVar) {
            $items_pVar = array_keys($items_all_pVar['literatura']);
        }
        else {
            $items_pVar = array();
        }

        $items_pVar[] = $item_id_pVar;
        $items_pVar = array_unique($items_pVar);


        $aktualnost = null;
        if(preg_match('/(\d{4})/', $newName_pVar, $matches_pVar)) {
            $aktualnost = $matches_pVar[1];
        }
        if(!is_numeric($aktualnost)) {
            $aktualnost = null;
        }

        $data_pVar = array('literatura_md5'=>md5($newName_pVar), 'literatura'=>$newName_pVar, 'aktualnost'=>$aktualnost);
        $data_str_pVar = '%s, %s, %xs';
        $where_str_pVar = '`literatura_md5` = %s';

        if($old_item_id_pVar) {
            foreach($items_pVar as $item_pVar) {
                if($old_item_id_pVar && $old_item_id_pVar != $item_pVar) {
                    $sql_pVar =  'SELECT `qmd5` FROM `%titems_literatura__data` WHERE `item_id` = %d';
                    $tmp_pVar = db_public_gClass::getField_gFunc($sql_pVar, __FILE__, __LINE__, $item_pVar);
                    $where_pVar = array($tmp_pVar);
                    $sql_pVar =  'DELETE FROM `%titems_literatura__data` WHERE `item_id` = %d';
                    db_public_gClass::execute_gFunc($sql_pVar, __FILE__, __LINE__, $item_pVar);
                }
                else {
                    $where_pVar = array($old_md5_pVar);
                }

                db_public_gClass::updateData_gFunc('%titems_test_questions__data', $data_str_pVar, $data_pVar, $where_str_pVar, $where_pVar, __FILE__, __LINE__);
            }
        }

        $items_all_pVar['literatura'] = array();
        main_gClass::setSessionData_gFunc('selected_items', $items_all_pVar);

    }
}

class addliteratura extends addliteratura_gClass {}
