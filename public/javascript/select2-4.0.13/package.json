{"name": "select2", "description": "Select2 is a jQuery based replacement for select boxes. It supports searching, remote data sets, and infinite scrolling of results.", "homepage": "https://select2.org", "author": {"name": "<PERSON>", "url": "https://github.com/kevin-brown"}, "contributors": [{"name": "<PERSON>", "url": "https://github.com/ivaynberg"}, {"name": "<PERSON>", "url": "https://github.com/alexweissman"}], "repository": {"type": "git", "url": "git://github.com/select2/select2.git"}, "bugs": {"url": "https://github.com/select2/select2/issues"}, "keywords": ["select", "autocomplete", "typeahead", "dropdown", "multiselect", "tag", "tagging"], "license": "MIT", "main": "dist/js/select2.js", "style": "dist/css/select2.css", "files": ["src", "dist"], "version": "4.0.13", "jspm": {"main": "js/select2", "directories": {"lib": "dist"}}, "devDependencies": {"almond": "~0.3.1", "grunt": "^1.0.4", "grunt-cli": "^1.3.2", "grunt-contrib-concat": "^1.0.1", "grunt-contrib-connect": "^2.0.0", "grunt-contrib-jshint": "^1.1.0", "grunt-contrib-qunit": "^1.3.0", "grunt-contrib-requirejs": "^1.0.0", "grunt-contrib-uglify": "~4.0.1", "grunt-contrib-watch": "~1.1.0", "grunt-sass": "^2.1.0", "jquery-mousewheel": "~3.1.13", "node-sass": "^4.12.0"}, "dependencies": {}}