<?xml version="1.0" encoding="utf-8" ?>
<truEngine-document xmlns:te="http://www.truengine.sk">
<?php
	if(!isset(@{layout})) {
		@{layout} = '';
	}
?>
	<te:content-set name="doccontent"><te:content-get name="content" /></te:content-set>
    <meta http-equiv="content-type" content="text/html; charset=utf-8" />
    <title>OPUS SAPIENTIAE: <te:content-get name="head-title" /></title>
    <link rel="stylesheet" href="/css/truEngine.css" type="text/css" media="screen" te:if="@{layout}!='text'" />

    <te:if test="\App\Legacy\LegacyApp::oldJsEnabled()">
        <link rel="stylesheet" href="/css/ui-themes/smoothness/jquery-ui-1.8.2.custom.css" type="text/css" media="screen" te:if="@{layout}!='text'" />
    <te:else />
        <link rel="stylesheet" href="/javascript/jquery-ui-1.13.3.full-smoothness/jquery-ui.min.css" type="text/css" media="screen" te:if="@{layout}!='text'" />
        <link rel="stylesheet" href="/javascript/select2-4.0.13/dist/css/select2.css" type="text/css" media="screen" te:if="@{layout}!='text'" />
        <link rel="stylesheet" href="/css/mixedtype/mixedtype.css" type="text/css" media="screen" te:if="@{layout}!='text'" />
    </te:if>

	<script type="text/javascript">
		var web_dir = '@{config:runtime:web_dir}';

		var dt_t =		'@L{teraz}';
		var dt_pnm =	'@L{pred niekoľkými minútami}';
		var dt_psh =	'@L{pred štvrť hodinou}';
		var dt_pph =	'@L{pred pol hodinou}';
		var dt_pth =	'@L{pred trištvrte hodinou}';
		var dt_ph =		'@L{pred hodinou}';
		var dt_d =		'@L{dnes}';
		var dt_v =		'@L{včera}';
		var dt_p =		'@L{predvčerom}';
		var dt_pt =		'@L{pred týždňom}';
		var dt_pm =		'@L{pred mesiacom}';

		var dt_pxh =	'@L{pred ? hodinami}';
		var dt_pxd = 	'@L{pred ? dňami}';
		var dt_pxt = 	'@L{pred ? týždňami}';
		var dt_pxm =	'@L{pred ? mesiacmi}';

	</script>

    <te:if test="\App\Legacy\LegacyApp::oldJsEnabled()">

                <script type="text/javascript" src="/js/jquery-1.4.2.min.js"></script>
                <script type="text/javascript">var $j = jQuery.noConflict();</script>
                <script type="text/javascript" src="/js/jquery-ui-1.8.2.custom.min.js"></script>
                <script type="text/javascript" src="/js/jquery.blockUI.js"></script>
                <script type="text/javascript" src="/js/jquery.cookie.js"></script>
                <script type="text/javascript" src="/js/php.min.js"></script>
                <script type="text/javascript" src="/js/jquery.date.time.picker.js"></script>
                <script type="text/javascript" src="/js/jquery.form.js"></script>


                <script type="text/javascript" src="/js/mootools-1.2.1.js"></script>
                <script type="text/javascript" src="/js/mootools-1.2-more.js"></script>
                <script type="text/javascript">var oldJsEnabled = true;</script>
                <script type="text/javascript" src="/js/json_parse.js"></script>
                <script type="text/javascript" src="/js/truEngine.js"></script>
                <script type="text/javascript" src="/js/Autocompleter.js"></script>
                <script type="text/javascript" src="/js/Autocompleter.Local.js"></script>
                <script type="text/javascript" src="/js/Autocompleter.Request.js"></script>
                <script type="text/javascript" src="/js/Observer.js"></script>
                <script type="text/javascript" src="/js/docEdit.js"></script>
                <script type="text/javascript" src="/sk/milkbox/js/milkbox.js"></script>
                <link rel="stylesheet" href="/sk/milkbox/css/milkbox.css" type="text/css" media="screen" />
                <link rel="stylesheet" href="/css/Autocompleter.css" type="text/css" media="screen" />
                <link rel="stylesheet" href="/css/jquery.date.time.picker.css" type="text/css" media="screen" />
                <script type="text/javascript" src="/js/json_parse.js"></script>
                <script type="text/javascript" src="/js/truEngine.js"></script>
        <te:else />
                <script type="text/javascript" src="/javascript/jquery-3.7.1.min.js"></script>
                <script type="text/javascript" src="/javascript/mixedtype.js"></script>
                <script type="text/javascript">var $j = $;</script>
                <script type="text/javascript">var oldJsEnabled = false;</script>
                <script type="text/javascript" src="/javascript/jquery-ui-1.13.3.full-smoothness/jquery-ui.min.js"></script>
                <script type="text/javascript" src="/javascript/select2-4.0.13/dist/js/select2.full.js"></script>
                <script type="text/javascript" src="/js/truEngine.js"></script>
    </te:if>


	<te:if test="main_gClass::getServerVar_gFunc('SERVER_NAME') != 'kega.developer.d.ui42.sk'">
		<script type="text/javascript">
			var _gaq = _gaq || [];
			_gaq.push(['_setAccount', 'UA-********-2']);
			_gaq.push(['_trackPageview']);
		  (function() {
		  var ga = document.createElement('script'); ga.type = 'text/javascript'; ga.async = true;
		  ga.src = ('https:' == document.location.protocol ? 'https://ssl' : 'http://www') + '.google-analytics.com/ga.js';
		   var s = document.getElementsByTagName('script')[0]; s.parentNode.insertBefore(ga, s);
	  		})();

		</script>
	</te:if>

</truEngine-document>
