<?php


class list_role_rights_gClass extends source_gClass
{
    protected function getData()
    {


        if(!session_gClass::userHasRightsAccess_gFunc(s_system_show_role_rights)) {
            return(array());
        }
        if(!isset($this->params['role_name']) || empty($this->params['role_name'])) {
            return(array('title'=>'??'));
        }

        $ret_pVar = array();

        $roles_pVar = db_access3_rights_gClass::getRoles_gFunc('name');

        if(!isset($roles_pVar[$this->params['role_name']])) {
            return(array('title'=>'?'));
        }

        $this->saveRequestData_gFunc('%taccess__roles_rights', 'role_right', s_system_edit_role_rights, array('role_id'=>$this->params['role_name']));

        $ret_pVar['title'] = $roles_pVar[$this->params['role_name']]['title'];

        $sql_pVar = 'SELECT `n`.*, `c`.*, `c`.`'.main_gClass::getLanguage_gFunc().'_category_name` as `category_name`, `n`.`'.main_gClass::getLanguage_gFunc().'_name` as `name`
						FROM `%taccess__names` as `n`
						LEFT JOIN `%taccess__names_categories` as `c` ON `n`.`access_category_id`=`c`.`access_category_id`
						ORDER BY `c`.`'.main_gClass::getLanguage_gFunc().'_category_name`, `n`.`'.main_gClass::getLanguage_gFunc().'_name`';
        $ret_pVar['actions'] = db_public_gClass::getResultArray_gFunc($sql_pVar, __FILE__, __LINE__, false);
        $ret_pVar['actions_index'] = array();
        foreach($ret_pVar['actions'] as $k_pVar=>$action_pVar) {
            if(!empty($action_pVar['module'])) {
                $modules_pVar = explode(',', $action_pVar['module']);
                $ok_pVar = false;
                foreach ($modules_pVar as $vv_pVar) {
                    if(modules_gClass::isModuleRegistred_gFunc($vv_pVar, false)) {
                        $ok_pVar = true;
                        break;
                    }
                }
                if(!$ok_pVar) {
                    unset($ret_pVar['actions'][$k_pVar]);
                    continue;
                }
            }
            elseif(!empty($action_pVar['category_module'])) {
                $modules_pVar = explode(',', $action_pVar['category_module']);
                $ok_pVar = false;
                foreach ($modules_pVar as $vv_pVar) {
                    if(modules_gClass::isModuleRegistred_gFunc($vv_pVar, false)) {
                        $ok_pVar = true;
                        break;
                    }
                }
                if(!$ok_pVar) {
                    unset($ret_pVar['actions'][$k_pVar]);
                    continue;
                }
            }
            $ret_pVar['actions_index'][$action_pVar['access_id']] = $k_pVar;
        }

        list($rights_pVar, $groups_pVar, $from_pVar) = session_session_gClass::getUserRightsFromDb_gFunc($this->params['role_name'], 1);
        $ret_pVar['rights'] = $rights_pVar;
        $ret_pVar['from'] = $from_pVar;

        return($ret_pVar);
    }

    /**
     * Funkcia nacita data z requestu a ulozi ich do tabulky...
     * @param $table_pVar
     * @param $fieldName_pVar
     * @param $otherFieldsData_pVar
     * @return unknown_type
     */
    protected function saveRequestData_gFunc($table_pVar, $fieldNamePrefix_pVar, $access_pVar, $otherFieldsData_pVar = array())
    {

        $data_pVar = $otherFieldsData_pVar;
        $selected_pVar = main_gClass::getInputString_gFunc('selected', main_gClass::SRC_GETPOST_pVar, '/allow|deny|unset/');
        if($selected_pVar !== 'allow' && $selected_pVar !== 'deny' && $selected_pVar !== 'unset') {
            return;
        }

        if(!session_gClass::userHasRightsAccess_gFunc($access_pVar)) {
            return;
        }

        $vars_pVar = main_gClass::getInputVarNames_gFunc(main_gClass::SRC_GETPOST_pVar);
        foreach($vars_pVar as $k_pVar) {
            // @TODO: je to trosku neoptimalne, ale nie je to kriticke (casto sa nepouziva). Rozumnejsie by bolo najskor si zostavit velky query a potom to spravit jednym, a nie takto mrte queries posielat...
            if(substr($k_pVar, 0, 6) !== 'right_') {
                continue;
            }
            $right_pVar = substr($k_pVar, 6);
            if(!is_numeric($right_pVar)) {
                continue;
            }
            $right_pVar = intval($right_pVar);

            if(main_gClass::getInputString_gFunc($k_pVar, main_gClass::SRC_GETPOST_pVar) !== 'on') {
                continue;
            }

            $changed_pVar = false;
            // ziskam povodnu hodnotu
            $sql_pVar = 'SELECT * FROM `' . $table_pVar . '` WHERE `' . $fieldNamePrefix_pVar . '_id` = %d';
            $sql_data_pVar = array($right_pVar);
            foreach($otherFieldsData_pVar as $kk_pVar=>$vv_pVar) {
                $sql_pVar .= ' AND `' . $kk_pVar . '` = %s';
                $sql_data_pVar[] = $vv_pVar;
            }
            $oldValue_pVar = db_public_gClass::getResult_gFunc($sql_pVar, __FILE__, __LINE__, $sql_data_pVar);

            if(is_array($oldValue_pVar) && ($selected_pVar === 'unset' || $oldValue_pVar[$fieldNamePrefix_pVar . '_state'] !== $selected_pVar)) {
                // najskor zmazem stare zaznamy ak existuju...
                $sql_pVar = 'DELETE FROM `' . $table_pVar . '` WHERE `' . $fieldNamePrefix_pVar . '_id` = %d';
                $sql_data_pVar = array($right_pVar);
                foreach($otherFieldsData_pVar as $kk_pVar=>$vv_pVar) {
                    $sql_pVar .= ' AND `' . $kk_pVar . '` = %s';
                    $sql_data_pVar[] = $vv_pVar;
                }
                db_public_gClass::execute_gFunc($sql_pVar, __FILE__, __LINE__, $sql_data_pVar);
                $changed_pVar = true;
            }

            // a teraz insertnem nove.
            if(($selected_pVar === 'allow' || $selected_pVar === 'deny') && (!is_array($oldValue_pVar) || $oldValue_pVar[$fieldNamePrefix_pVar . '_state'] !== $selected_pVar)) {
                $data_pVar[$fieldNamePrefix_pVar . '_id'] = $right_pVar;
                $data_pVar[$fieldNamePrefix_pVar . '_state'] = $selected_pVar;
                db_public_gClass::insertData_gFunc($table_pVar, str_repeat('%s,', count($otherFieldsData_pVar)) . '%d,%s', $data_pVar, __FILE__, __LINE__);
                $changed_pVar = true;
            }

            //// zalogujem
            if($changed_pVar) {

                $xdata_pVar = array(
                    'user_id'=>session_gClass::getUserDetail_gFunc('user_id'),
                    'record_time'=>'now()',
                    'record_type1'=>'',
                    'record_type2'=>$selected_pVar,
                    'record_data'=>''
                );

                if($table_pVar === '%taccess__roles_rights') {
                    $xdata_pVar['record_type1'] = 'role';
                    $xdata_pVar['record_data'] = $otherFieldsData_pVar['role_id'] . '|' . $right_pVar;
                }
                elseif($table_pVar === '%taccess__users_rights') {
                    $xdata_pVar['record_type1'] = 'user';
                    $xdata_pVar['record_data'] = $otherFieldsData_pVar['user_id'] . '|' . $right_pVar;
                }
                elseif($table_pVar === '%taccess__groups_rights') {
                    $xdata_pVar['record_type1'] = 'group';
                    $xdata_pVar['record_data'] = $otherFieldsData_pVar['group_id'] . '|' . $right_pVar;
                }
                if(is_array($oldValue_pVar)) {
                    $xdata_pVar['record_data'] .= '|' . $oldValue_pVar[$fieldNamePrefix_pVar . '_state'];
                }

                db_public_gClass::insertData_gFunc('%taccess__log', '%d,%r,%s,%s,%s', $xdata_pVar, __FILE__, __LINE__);
            }
        }
    }
}

class list_role_rights extends list_role_rights_gClass {}
