<?php

class test_gClass extends source_gClass
{

    protected function getData()
    {
        if(!isset($this->params['test_id']) || !is_numeric($this->params['test_id'])) {
            return(array('test_status'=>array('display'=>false, 'edit'=>false, 'update'=>false)));
        }

        // nastavim vychodzie hodnoty, dalej ich modifikujem na false.
        $edit_enabled_pVar = true;
        $update_enabled_pVar = true;
        $display_enabled_pVar = true;

        // select nastaveni testu
        $test_pVar = array();
        $sql_pVar = 'SELECT * FROM `%ttests_running` WHERE `id` = %d';
        $sql_params_pVar = array($this->params['test_id']);
        $test_pVar['data'] = db_public_gClass::getResult_gFunc($sql_pVar, __FILE__, __LINE__, $sql_params_pVar);
        $test_pVar['data']['settings'] = kega_gClass::explodeSettings_gFunc($test_pVar['data']['settings']);

        // test neexistuje
        if(!is_array($test_pVar['data'])) {
            return(array('test_status'=>array('display'=>false, 'edit'=>false, 'update'=>false)));
        }

// dd($test_pVar['data']['user_id'], session_gClass::getUserDetail_gFunc('user_id'));


        // nepatri pouzivatelovi. Ak ma prava na zobrazenie cudzich testov, tak mu povolim zobrazenie.
        if($test_pVar['data']['user_id'] !== session_gClass::getUserDetail_gFunc('user_id')) {
            $edit_enabled_pVar = false;
            $update_enabled_pVar = false;
            if(!session_gClass::userHasRightsAccess_gFunc(s_test_display_user_test)) {
                $display_enabled_pVar = false;
            }
            $test_pVar['data']['settings']['sposob_testovania'] = 'vsetko'; // urobim nahlad vsetkych otazok.
        }

        // odstartujem test, ak treba a je to povolene
        if($edit_enabled_pVar && empty($test_pVar['data']['time_first']) && $test_pVar['data']['status'] == 'waiting') {
            if(($test_pVar['data']['time_start'] != NULL && strtotime($test_pVar['data']['time_start']) > time())
                || ($test_pVar['data']['time_end'] != NULL && strtotime($test_pVar['data']['time_end']) < time())) {
                // nie je povoleny pre tento datum
                $edit_enabled_pVar = false;
                $update_enabled_pVar = false;
            }
            else {
                $sql_pVar = 'UPDATE `%ttests_running` SET `time_first` = now(), `status`=\'running\' WHERE `id` = %d AND `user_id` = %d AND `time_first` IS NULL AND `status` = \'waiting\'';
                $sql_params_pVar = array($this->params['test_id'], session_gClass::getUserDetail_gFunc('user_id'));
                db_public_gClass::execute_gFunc($sql_pVar, __FILE__, __LINE__, $sql_params_pVar);
                $test_pVar['data']['time_first'] = date('Y-m-d H:i:s');
                $test_pVar['data']['status'] = 'running';
                $update_enabled_pVar = false;

                if(isset($test_pVar['data']['settings']['cas']) && intval($test_pVar['data']['settings']['cas'])) {
                    $minT_pVar = intval($test_pVar['data']['settings']['cas']) + 30;
                }
                else {
                    $minT_pVar = 70;
                }
                db_session_gClass::setSessionTimeout_gFunc($minT_pVar, 1);

//                \App\Tracker\Tracker::getInstance()->track('test_start', [
//                    'test_id' => $this->params['test_id'],
//
//                ]);
            }
        }

        // ak test nie je odstartovany, a nema pravo na pozeranie neodstartovanych testov
        if($test_pVar['data']['status'] === 'waiting' && !session_gClass::userHasRightsAccess_gFunc(s_test_display_user_test_prepared)) {
            $display_enabled_pVar = false;
        }

        $request_data_pVar = array();

        if($test_pVar['data']['status'] !== 'running') {
            // ak test nebezi, tak zakazem edit a update. Moze si iba pozerat otazky, ak su splnene ostatne predpoklady.
            $update_enabled_pVar = false;
            $edit_enabled_pVar = false;
        }
        else {
            // overim, ci uz nie je prekroceny casovy limit (status je running, takze ho zmenim na closed, a zakazem update.)
            // akceptujem zapis dat s toleranciou + 30 sec, koli nepresnosti casu na stanici, a tiez oneskorenie requestu.. snad to bude stacit.
            if($update_enabled_pVar) {
                if(isset($test_pVar['data']['settings']['cas']) && (strtotime($test_pVar['data']['time_first']) + intval($test_pVar['data']['settings']['cas']) * 60) < time()) {
                    // casovy limit vyprsal
                    $edit_enabled_pVar = false;
                    $test_pVar['data']['status'] = 'closed';
                    $sql_pVar = 'UPDATE `%ttests_running` SET `status`=\'closed\' WHERE `id` = %d AND `status` = \'running\'';
                    $sql_params_pVar = array($this->params['test_id']);
                    db_public_gClass::execute_gFunc($sql_pVar, __FILE__, __LINE__, $sql_params_pVar);
                    if((strtotime($test_pVar['data']['time_first']) + intval($test_pVar['data']['settings']['cas']) * 60 + 30) < time()) {
                        $update_enabled_pVar = false;
                    }
                }
            }

            // nacitam request
            if($update_enabled_pVar) {
                $request_keys_pVar = main_gClass::getInputVarNames_gFunc(main_gClass::SRC_POST_pVar);
                $tag_pVar = 'question_' . $test_pVar['data']['id'] . '_';
                $tag_len_pVar = strlen($tag_pVar);
                foreach($request_keys_pVar as $v_pVar) {
                    if(substr($v_pVar, 0, $tag_len_pVar) != $tag_pVar) {
                        continue;
                    }
                    $tmp_pVar = explode('_', $v_pVar);
                    if(substr($v_pVar, 0, $tag_len_pVar + 5) == $tag_pVar . 'flag_') {
                        if(main_gClass::getInputString_gFunc($v_pVar, main_gClass::SRC_POST_pVar)) {
                            $request_data_pVar[$tmp_pVar[4]] = true;
                        }
                        else {
                            $request_data_pVar[$tmp_pVar[4]] = false;
                        }
                    }
                    else {
                        if(!isset($request_data_pVar[$tmp_pVar[3]])) {
                            $request_data_pVar[$tmp_pVar[3]] = false;
                        }
                    }
                }
            }

        }

        // selectnem otazky
        $data_pVar = question_selector_gClass::selectQuestions_gFunc('%ttests_running__questions', 'running_test_id', $this->params['test_id'], false, false, isset($test_pVar['data']['settings']['language'])?$test_pVar['data']['settings']['language']:false);
        $test_pVar['questions'] = $data_pVar['questions'];
        $test_pVar['answers'] = $data_pVar['answers'];

        // spracujem request
        if($update_enabled_pVar) {
            $updated_questions_pVar = array();
            foreach($request_data_pVar as $k_pVar=>$v_pVar) {
                $question_id_pVar = $test_pVar['answers'][$k_pVar]['test_question'];
                $answers_pVar = explode(',', $test_pVar['questions'][$question_id_pVar]['db_answer_ids']);

                $results_pVar = explode('/', $test_pVar['questions'][$question_id_pVar]['answer_results']);
                if(count($results_pVar) == 1) {
                    $results_pVar = array(str_repeat(',', count($answers_pVar)-1), str_repeat(',', count($answers_pVar)-1));
                }
                $results_pVar[0] = explode(',', $results_pVar[0]);
                $results_pVar[1] = explode(',', $results_pVar[1]);
                $key_pVar = array_search($k_pVar, $answers_pVar);

                if($test_pVar['answers'][$k_pVar]['spravnost'] == 'spravne') {
                    $results_pVar[0][$key_pVar] = 'S';
                }
                else {
                    $results_pVar[0][$key_pVar] = 'N';
                }
                if($v_pVar) {
                    if($results_pVar[0][$key_pVar] == 'S') {
                        $results_pVar[1][$key_pVar] = 'S';
                    }
                    else {
                        $results_pVar[1][$key_pVar] = 'N';
                    }
                    $test_pVar['answers'][$k_pVar]['checked'] = $v_pVar;
                }
                else {
                    if($results_pVar[0][$key_pVar] == 'S') {
                        $results_pVar[1][$key_pVar] = 'N';
                    }
                    else {
                        $results_pVar[1][$key_pVar] = 'S';
                    }
                }

                $results_pVar[0] = implode(',', $results_pVar[0]);
                $results_pVar[1] = implode(',', $results_pVar[1]);
                $test_pVar['questions'][$question_id_pVar]['answer_results'] = implode('/', $results_pVar);
                $updated_questions_pVar[$question_id_pVar] = $question_id_pVar;
            }

            // ulozim request
            foreach($updated_questions_pVar as $v_pVar) {
                $sql_pVar = 'UPDATE `%ttests_running__questions` SET `answer_results` = %s WHERE `running_test_id` = %d AND `db_question_id` = %d';
                db_public_gClass::execute_gFunc($sql_pVar, __FILE__, __LINE__, array($test_pVar['questions'][$v_pVar]['answer_results'] , $test_pVar['data']['id'], $v_pVar));
                main_gClass::invalidateCacheTag_gFunc('test_questions_stats', $v_pVar);
                main_gClass::invalidateCacheTag_gFunc('test_answers_stats', $v_pVar);
            }

            // vypocitam pocty odpovedi (spravne/nespravne)
            $answers_OK_pVar = 0;
            $answers_OK_OK_pVar = 0;
            $answers_FAIL_pVar = 0;
            $answers_TOTAL_pVar = 0;
            $questions_FAIL_pVar = 0;
            $questions_TOTAL_pVar = 0;
            foreach($test_pVar['questions'] as $k_pVar=>$v_pVar) {
                $questions_TOTAL_pVar++;
                $questions_FAIL_pVar++;
                $tmp_pVar = explode('/', $v_pVar['answer_results']);
                if(isset($tmp_pVar[1])) {
                    $answers_OK_pVar += substr_count($tmp_pVar[1], 'S');
                    $answers_FAIL_pVar += substr_count($tmp_pVar[1], 'N');
                    if(!substr_count($tmp_pVar[1], 'N')) {
                        $questions_FAIL_pVar--;
                    }
                    $n_pVar = strlen($tmp_pVar[0]);
                    for($i_pVar = 0; $i_pVar < $n_pVar; $i_pVar++) {
                        if($tmp_pVar[0][$i_pVar] == 'S' && $tmp_pVar[1][$i_pVar] == 'S') {
                            $answers_OK_OK_pVar++;
                        }
                    }
                }
                $tmp_pVar = explode(',', $v_pVar['db_answer_ids']);
                $answers_TOTAL_pVar += count($tmp_pVar);
            }

            // vypocitam pocet bodov
            if(!isset($test_pVar['data']['settings']['typ_hodnotenia']) || empty($test_pVar['data']['settings']['typ_hodnotenia'])) {
                $test_pVar['data']['settings']['typ_hodnotenia'] = 'standard';
            }

            switch($test_pVar['data']['settings']['typ_hodnotenia']) {
                case 'dvojmo':
                    $score_pVar = $answers_OK_pVar * 2;
                    $total_score_pVar = count($test_pVar['answers']);
                    break;
                case 'prijmacky':
                    $score_pVar = $answers_OK_pVar - ($answers_TOTAL_pVar/2);
                    $total_score_pVar = count($test_pVar['answers']);
                    break;
                case 'celoodpovedovy':
                    $score_pVar = $questions_TOTAL_pVar - $questions_FAIL_pVar;
                    $total_score_pVar = $questions_TOTAL_pVar;
                    break;
                case 'inverzny':
                    if(!isset($test_pVar['data']['settings']['max_errors']) || empty($test_pVar['data']['settings']['max_errors']) || !intval($test_pVar['data']['settings']['max_errors'])) {
                        $test_pVar['data']['settings']['max_errors'] = $answers_TOTAL_pVar;
                    }
                    $test_pVar['data']['settings']['max_errors'] = intval($test_pVar['data']['settings']['max_errors']);
                    if($test_pVar['data']['settings']['max_errors'] > $answers_TOTAL_pVar) {
                        $test_pVar['data']['settings']['max_errors'] = $answers_TOTAL_pVar;
                    }
                    $x_pVar = $answers_TOTAL_pVar - ($answers_TOTAL_pVar / $test_pVar['data']['settings']['max_errors']) *  ($answers_TOTAL_pVar - $answers_OK_pVar);
                    if($x_pVar < 0) {
                        $x_pVar = 0;
                    }
                    $score_pVar = $x_pVar;
                    $total_score_pVar = count($test_pVar['answers']);
                    break;
                case 'standard':
                default:
                    $score_pVar = $answers_OK_pVar;
                    $total_score_pVar = count($test_pVar['answers']);
                    break;
            }

            // vypocitam ci sa ma test zaradovat do statistik
            if(floatval($answers_OK_pVar) / floatval($answers_TOTAL_pVar) > 0.55 && ($answers_OK_OK_pVar) >= 3) {
                $stat_pVar = 'yes';
            }
            else {
                $stat_pVar = 'no';
            }

            $ip_pVar = main_gClass::getServerVar_gFunc('REMOTE_ADDR');

            // updatnem cas a pocty odpovedi, bodovanie, ip.
            $sql_pVar = 'UPDATE `%ttests_running` SET
					`time_total` = TIME_TO_SEC(now()) - TIME_TO_SEC(`time_first`),
					`score` = %d,
					`n_answers_ok` = %d,
					`n_answers_fail` = %d,
					`in_stats` = %s,
					`ip` = %s
					WHERE `id` = %d AND `user_id` = %d AND `status` = \'running\'';
            $sql_params_pVar = array($score_pVar, $answers_OK_pVar, $answers_FAIL_pVar, $stat_pVar, $ip_pVar, $this->params['test_id'], session_gClass::getUserDetail_gFunc('user_id'));
            db_public_gClass::execute_gFunc($sql_pVar, __FILE__, __LINE__, $sql_params_pVar);

            $test_pVar['data']['score'] = $score_pVar;

            // aktualizujem hall of fame
            if(isset($test_pVar['data']['settings']['halloffame'])
                && $test_pVar['data']['settings']['halloffame'] == 'yes') {
                if(intval($test_pVar['data']['source_test_id'])) {
                    self::writeHallOfFame_gFunc($test_pVar['data']['source_test_id'], 'test', $test_pVar['data']['user_id'], $score_pVar, $total_score_pVar);
                }
                if(intval($test_pVar['data']['source_template_id'])) {
                    self::writeHallOfFame_gFunc($test_pVar['data']['source_template_id'], 'template', $test_pVar['data']['user_id'], $score_pVar, $total_score_pVar);
                }
            }

            main_gClass::invalidateCacheTag_gFunc('user_test_stats', session_gClass::getUserDetail_gFunc('user_id'));
        }

        // updatnem checkboxy podla aktualneho stavu, + nastavim indexy otazok, ked uz robim foreach
        // + nastavim $questions_done_pVar na true, ak uz bolo odpovedane na vsetky otazky (uzitocne ak sa testuje po jednej otazke)
        $q_index_pVar = 0;
        $questions_done_pVar = true;
        $test_pVar['status'] = array('ok'=>0, 'total'=>0, 'status'=>0, 'total_answers'=>0);

        foreach($test_pVar['questions'] as $k_pVar=>$question_pVar) {
            $answers_pVar = explode(',', $question_pVar['db_answer_ids']);
            $test_pVar['status']['total_answers'] += count($answers_pVar);
            //$test_pVar['status']['total'] += count($answers_pVar);
            $test_pVar['questions'][$k_pVar]['index'] = ++$q_index_pVar;// nastavil som index otazky
            if(empty($question_pVar['answer_results'])) {
                $questions_done_pVar = false;
                continue;
            }
            $results_pVar = explode('/', $question_pVar['answer_results']);
            if(count($results_pVar) == 1) {
                $results_pVar = array(str_repeat(',', count($answers_pVar)-1), str_repeat(',', count($answers_pVar)-1));
            }
            $results_pVar[0] = explode(',', $results_pVar[0]);
            $results_pVar[1] = explode(',', $results_pVar[1]);
            foreach($answers_pVar as $kk_pVar=>$vv_pVar) {
                if($results_pVar[1][$kk_pVar] == 'S' && $results_pVar[0][$kk_pVar] == 'S') {
                    $test_pVar['answers'][$vv_pVar]['checked'] = true;
                }
                elseif($results_pVar[1][$kk_pVar] == 'N' && $results_pVar[0][$kk_pVar] == 'N') {
                    $test_pVar['answers'][$vv_pVar]['checked'] = true;
                }

                if($results_pVar[1][$kk_pVar] == 'S') {
                    $test_pVar['answers'][$vv_pVar]['OK'] = true;
                }
                else {
                    $test_pVar['answers'][$vv_pVar]['OK'] = false;
                }

                if($results_pVar[1][$kk_pVar] == 'S') {
                    //$test_pVar['status']['ok']++;
                }
            }
        }
        $test_pVar['status']['ok'] = $test_pVar['data']['score'];
        $test_pVar['status']['total'] = 0;

        if(isset($test_pVar['data']['settings']['typ_hodnotenia'])) {
            $typ = $test_pVar['data']['settings']['typ_hodnotenia'];
        }
        else {
            $typ = 'standard';
        }
        switch($typ) {
            case 'dvojmo':
                $test_pVar['status']['total'] = $test_pVar['status']['total_answers'] * 2;
                break;
            case 'prijmacky':
                $test_pVar['status']['total'] = $test_pVar['status']['total_answers'] / 2;
                break;
            case 'celoodpovedovy':
                $test_pVar['status']['total'] = count($test_pVar['questions']);
                break;
            case 'inverzny':
                $test_pVar['status']['total'] = $test_pVar['status']['total_answers'];
                break;
            case 'standard':
            default:
                $test_pVar['status']['total'] = $test_pVar['status']['total_answers'];
                break;
        }

        if($test_pVar['status']['total']) {
            $test_pVar['status']['status'] = sprintf('%0.2f', ($test_pVar['status']['ok']/$test_pVar['status']['total']) * 100);
        }
        else {
            $test_pVar['status']['status'] = '0.00';
        }

        // ukoncim test, ak treba. A ak som neposlal test_save (znamena to ze len ukladam test, ale neukoncujem)
        if($update_enabled_pVar) {
            if($test_pVar['data']['settings']['sposob_testovania'] != 'postupne'
                || $questions_done_pVar) {

                if(!main_gClass::getInputString_gFunc('test_save', main_gClass::SRC_REQUEST_pVar, false, false)) {
                    $sql_pVar = 'UPDATE `%ttests_running` SET `status`=\'closed\' WHERE `id` = %d AND `user_id` = %d AND `status` = \'running\'';
                    $sql_params_pVar = array($this->params['test_id'], session_gClass::getUserDetail_gFunc('user_id'));
                    db_public_gClass::execute_gFunc($sql_pVar, __FILE__, __LINE__, $sql_params_pVar);
                    $test_pVar['data']['status'] = 'closed';
                }
            }
        }

        $test_pVar['current_question'] = 0;
        // nastavim premennu current_question, ak je rezim testovania po jednej otazke.
        if($test_pVar['data']['settings']['sposob_testovania'] == 'postupne' && $test_pVar['data']['status'] === 'running') {
            if($test_pVar['data']['settings']['ukazat_odpovede'] == 'postupne' && is_array($request_data_pVar) && count($request_data_pVar)) {
                $keys_pVar = array_keys($request_data_pVar);
                if(isset($test_pVar['answers'][reset($keys_pVar)])) {
                    $test_pVar['current_question'] = $test_pVar['answers'][reset($keys_pVar)]['test_question'];
                    $edit_enabled_pVar = false;
                }
            }
            if(!$test_pVar['current_question']) {
                foreach($test_pVar['questions'] as $k_pVar=>$v_pVar) {
                    if(empty($v_pVar['answer_results'])) {
                        $test_pVar['current_question'] = $v_pVar['item_id'];
                        break;
                    }
                }
            }
        }

        // ak bolo odpovedane na vsetky otazky, unsetnem otazky, ak nezobrazujem vysledky.
        if($questions_done_pVar) {
            $edit_enabled_pVar = false;
            $update_enabled_pVar = false;
            if($test_pVar['data']['settings']['ukazat_odpovede'] == 'nie') {
                unset($test_pVar['questions']);
                unset($test_pVar['answers']);
            }
        }

        if(!$display_enabled_pVar) {
            unset($test_pVar['questions']);
            unset($test_pVar['answers']);
        }

        $test_pVar['test_status'] = array('display'=>$display_enabled_pVar, 'edit'=>$edit_enabled_pVar, 'update'=>$update_enabled_pVar);

        if($test_pVar['data']['user_id']) {
            $test_pVar['data']['user'] = session_gClass::getUserDetails_gFunc($test_pVar['data']['user_id']);
        }
        return($test_pVar);
    }

    private function writeHallOfFame_gFunc($test_id_pVar, $type_pVar, $user_id_pVar, $score_pVar, $totalScore_pVar)
    {
        $sql_pVar = 'DELETE FROM `%ttest_hall_of_fame` WHERE
						`status` = \'active\'
						AND `test_id` = %d
						AND `type` = %s
						AND `user_id` = %d
						AND `score` < %d
		';
        db_public_gClass::execute_gFunc($sql_pVar, __FILE__, __LINE__, array($test_id_pVar, $type_pVar, $user_id_pVar, $score_pVar));


        $sql_pVar = 'SELECT id,score FROM `%ttest_hall_of_fame` WHERE
						`status` = \'active\'
						AND `test_id` = %d
						AND `type` = %s
						AND user_id = %d
		';
        $old_score_pVar = db_public_gClass::getResultArray_gFunc($sql_pVar, __FILE__, __LINE__, array($test_id_pVar, $type_pVar, $user_id_pVar));
        if(is_array($old_score_pVar) && count($old_score_pVar)) {
            // uz je zapisany a ma zapisane vyssie score
            return;
        }

        // zistim, ci to je na zapis
        $sql_pVar = 'SELECT score FROM `%ttest_hall_of_fame` WHERE
						`status` = \'active\'
						AND `test_id` = %d
						AND `type` = %s
						ORDER BY `score` DESC
						LIMIT 13,1';
        $limit_score_pVar = db_public_gClass::getField_gFunc($sql_pVar, __FILE__, __LINE__, array($test_id_pVar, $type_pVar));

        if(floatval($limit_score_pVar) > $score_pVar) {
            return;
        }

        // zapisem usera
        $data_pVar = array('test_id'=>$test_id_pVar, 'type'=>$type_pVar, 'user_id'=>$user_id_pVar, 'score'=>$score_pVar, 'total_score'=>$totalScore_pVar, 'join_date'=>'now()');
        db_public_gClass::insertData_gFunc('%ttest_hall_of_fame', '%d, %s, %d, %f, %f, now()', $data_pVar, __FILE__, __LINE__);

        // zmazem ine zaznamy usera (eee.. umoznim byt v tabulke viac krat :)

        // zmazem zaznamy z tabulky, ktore su nad limit
        if($limit_score_pVar) {
            db_public_gClass::execute_gFunc('DELETE FROM `%ttest_hall_of_fame` WHERE
												`status` = \'active\'
												AND `test_id` = %d
												AND `type` = %s
												AND `score` < %f', __FILE__, __LINE__,
                array($test_id_pVar, $type_pVar, $limit_score_pVar));
        }
    }
}

class test extends test_gClass {}
