<?php

class list_questions_gClass extends source_gClass
{

    protected function getData()
    {
//        if($this->params && isset($this->params['type']) && $this->params['type'] === 'laravel') {
//            //dd('laravel');
//
//            \App\Truengine\DataFactory::factory('kega_list_questions')->get();
//            ????????
//
//
//            return 'LARAVEL'; // return collection (or object with meta data and collection)....? nejaky objekt typu source class :)  a spravim si factory na vyrobu tychto objektov
//        }

        $GLOBALS['debug'] = true;
        $questions_pVar = items_gClass::getItems_gFunc('test_questions', $this->params);
//dd($questions_pVar[1]);
        unset($questions_pVar['filter']);
        $this->formatData_gFunc($questions_pVar);
        return($questions_pVar);
    }

    protected function formatData_gFunc(&$data_pVar)
    {

        foreach ($data_pVar as $k_pVar=>$v_pVar) {
            if(substr($k_pVar, 0, 1) == '_') {
                continue;
            }
            $data_pVar[$k_pVar]['literatura'] = string_gClass::formatLinks_gFunc($data_pVar[$k_pVar]['literatura']);
        }
    }

}

class list_questions extends list_questions_gClass { }
