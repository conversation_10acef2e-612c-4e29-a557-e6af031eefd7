<?php

namespace App\View\Components;

use Mixedtype\Forms\Fields\Fieldset;
use Mixedtype\Forms\Fields\TextField;
use Mixedtype\Forms\Fields\SelectField;
use Mixedtype\Forms\Fields\TextareaField;
use Mixedtype\Forms\Form;
use Mixedtype\Forms\Interface\FormInterface;

class QuestionForm extends Form implements FormInterface
{
    protected function initForm():FormInterface
    {
        return $this
            ->addFields([
                (new Fieldset('categorization_fieldset', 'Zaradenie'))
                    ->addFields([
                        (new SelectField('test_questions_modul', 'Modul')),
                        (new SelectField('test_questions_program', 'Cieľ. št. program')),
                        (new SelectField('test_questions_predmet', 'Predmet')),
                        (new SelectField('test_questions_kategoria', 'Problematika (kategória)')),
                        (new SelectField('test_questions_podkategoria', 'Podkategória')),
                    ]),
                (new Fieldset('main', 'Základné informácie'))
                    ->addFields([
                        (new TextareaField('test_questions_sk_otazka', '<PERSON><PERSON><PERSON> (text)'))
                        ->setLanguages(['sk', 'en']),
                    ])
            ]);
    }
}
