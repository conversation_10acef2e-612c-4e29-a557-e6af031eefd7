<?php

namespace Mixedtype\Components;

trait ComponentFactoryTrait
{
    private ?string $componentId = null;

    public function withAttributes(array $attributes): ?\Mixedtype\Components\Component
    {
        ComponentFactory::storeComponentAttribs(
            $this->getComponentId(),
            $attributes
        );
        return parent::withAttributes($attributes);
    }

    function getComponentId()
    {
        if($this->componentId === null) {
            $this->componentId = ComponentIdManager::generateNewId();
        }
        return $this->componentId;
    }

    function setComponentId($componentId)
    {
        if($this->componentId === null) {
            $this->componentId = $componentId;
        }
    }

    public function addComponentCustomParamsToArray($params = []) : array
    {
        $params['_component_id'] = $this->getComponentId();
        $params['_component_class'] = get_class($this);
        return $params;
    }

    public function renderComponent() : string
    {
//        dd(app('view')->startComponent('dasdas', []));
//        dd(app('blade.compiler'));

        return app('blade.compiler')->renderComponent($this);
    }
}
