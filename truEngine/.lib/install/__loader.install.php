<?php 

if(!isStamp_gFunc('_loader', 'vytvorenie tabuliek files a files_log')) {
	$sql_pVar = "
		CREATE TABLE IF NOT EXISTS `%tfiles` (
			`file_id` int(10) unsigned NOT NULL auto_increment,
			`file_type` enum('file','image') NOT NULL default 'file',
			`location` enum('secured','public','isic','questions') NOT NULL default 'secured',
			`full_name` varchar(255) NOT NULL default '',
			`ref_tag` enum('others','items_users_foto','items_users_foto_rec') NOT NULL default 'others',
			`ref_value` int(10) unsigned NOT NULL default '0',
			`original_md5` varchar(32) NOT NULL default '',
			`md5` varchar(32) NOT NULL default '',
			`size` int(10) unsigned NOT NULL,
			`custom_name` varchar(255) NULL DEFAULT NULL,
			`copyright` varchar(255) NULL DEFAULT NULL,		
			PRIMARY KEY  (`file_id`),
			KEY `file_type` (`file_type`),
			KEY `ref_tag` (`ref_tag`,`ref_value`)
		) ENGINE=InnoDB
	";
	if(!db_public_gClass::execute_gFunc($sql_pVar, __FILE__, __LINE__)) {
		return(false);
	}
	
	$sql_pVar = "
		CREATE TABLE IF NOT EXISTS `%tfiles_log` (
			`change_id` int(10) unsigned NOT NULL auto_increment,
			`user_id` int(10) unsigned NOT NULL default '0',
			`change_datetime` datetime NOT NULL default '0000-00-00 00:00:00',
			`change_type` enum('upload','delete','clone','delete_clone','delete_record') NOT NULL default 'upload',
			`file_id` int(10) unsigned NOT NULL default '0',
			`change_info` text,
			PRIMARY KEY  (`change_id`)
			) ENGINE=InnoDB
	";
	if(!db_public_gClass::execute_gFunc($sql_pVar, __FILE__, __LINE__)) {
		return(false);
	}	
	
	stamp_gFunc('_loader', 'vytvorenie tabuliek files a files_log');
}

if(!isStamp_gFunc('_loader', 'vytvorenie tabuliek cache')) {
	$sql_pVar = "
		CREATE TABLE IF NOT EXISTS `%tcache_tags` (
			`tag` varchar(255) NOT NULL,
			`object_id` int(10) unsigned NOT NULL DEFAULT 0,
			`object_tag` varchar(255) DEFAULT NULL,
			`cached_time` datetime,
			PRIMARY KEY  (`tag`, `object_id`)
		) ENGINE=InnoDB
	";
	if(!db_public_gClass::execute_gFunc($sql_pVar, __FILE__, __LINE__)) {
		return(false);
	}
	
	stamp_gFunc('_loader', 'vytvorenie tabuliek cache');
}

if(!isStamp_gFunc('_loader', 'vytvorenie tabuliek access__log, access__names, access__names_categories')) {
	$sql_pVar = "
		CREATE TABLE IF NOT EXISTS `%taccess__log` (                                                                                                        
                    `record_id` int(10) unsigned NOT NULL AUTO_INCREMENT,                                                                                  
                    `record_time` datetime DEFAULT NULL,                                                                                                   
                    `user_id` int(10) unsigned DEFAULT NULL,                                                                                               
                    `record_type1` enum('group','role','user') DEFAULT NULL,                                                                               
                    `record_type2` enum('add','delete','update','allow','deny','unset','group_add','group_delete','role_add','role_delete') DEFAULT NULL,  
                    `record_data` text,                                                                                                                    
                    PRIMARY KEY (`record_id`)                                                                                                              
                  ) ENGINE=InnoDB";
	if(!db_public_gClass::execute_gFunc($sql_pVar, __FILE__, __LINE__)) {
		return(false);
	}
		
	$sql_pVar = "
		CREATE TABLE IF NOT EXISTS `%taccess__names_categories` (
		  `access_category_id` int(10) unsigned NOT NULL auto_increment,
		  `prefix` varchar(255) NOT NULL,
		  `sk_category_name` varchar(255) NOT NULL default '',
		  `category_module` varchar(255) COMMENT 'nazov modulu ktory musibyt registrovany, aby sa toto zobrazilo, alebo nazov modulov oddelenych ciarkov (ciarka = operator OR)',
		  PRIMARY KEY  (`access_category_id`)
		) ENGINE=InnoDB;
	";
	if(!db_public_gClass::execute_gFunc($sql_pVar, __FILE__, __LINE__)) {
		return(false);
	}

	$sql_pVar = "
		CREATE TABLE IF NOT EXISTS `%taccess__names` (
		  `access_id` int(10) unsigned NOT NULL auto_increment,
		  `access_category_id` int(10) unsigned NOT NULL default 2,
		  `alias` varchar(255) NOT NULL COMMENT 'nazov konstanty sa vygeneruje z prefixu s_, prefixu kategorie, a aliasu. Za alias mozem pridat dalsie nazvy konstant, ktore chcem generovat - tie sa uz nemenia napr. system -> logged_on,s_logged_on vygeneruje s_system_logged_on a s_logged_on',
		  `sk_name` varchar(255) NOT NULL default '',
		  `archive` enum('yes','no') NOT NULL default 'yes' COMMENT 'ci sa bude archivovat v logu',
		  `module` varchar(255) COMMENT 'nazov modulu ako v tabulke names_categories. Ak je NULL, uplatni sa nastavenie z names_categories. Ak je nastavene, prebije names_categories',
		  PRIMARY KEY  (`access_id`)
		) ENGINE=InnoDB;
	";
	if(!db_public_gClass::execute_gFunc($sql_pVar, __FILE__, __LINE__)) {
		return(false);
	}

	stamp_gFunc('_loader', 'vytvorenie tabuliek access__log, access__names, access__names_categories');
}

if(!isStamp_gFunc('_loader', 'prvotna inicializacia tabuliek access__names, access__names_categories')) {
	$sql_pVar = "
		INSERT INTO `%taccess__names_categories` (`access_category_id`,	`prefix`,	`sk_category_name`,		`category_module`)
				VALUES 							  (1, 					'system',	'Systém',				'_loader')
					  							 ,(2, 					'common',	'Všeobecné',			'_loader')
												 ,(3,					'document',	'Dokumenty',			'documents')
												 ,(4, 					'users',	'Správa používateľov',	'access3_rights')
												 ,(5, 					'test',		'Test',					'kega')
					  
	";
	if(!db_public_gClass::execute_gFunc($sql_pVar, __FILE__, __LINE__)) {
		return(false);
	}

	$sql_pVar = "
		INSERT INTO `%taccess__names` (	`access_id`,	`access_category_id`,	`alias`,					`sk_name`,												`archive`, `module`)
							   VALUES (	1, 				1,						'superadmin',				'Super administrátor',									'yes',		NULL)
					  				 ,(	2,				1,						'admin',					'Administrátor',										'yes',		NULL)
					  				 ,(	3,				1,						'logged_on,s_logged_on',	'Prihlásený',											'no',		NULL)
					  				 
					  				 ,(	4,				3,						'read',						'Čítanie dokumentu',									'yes',		'_loader')
					  				 
					  				 ,(	5,				4,						'add_superadmin',			'Vytvorenie nového super administrátora',				'yes',		NULL)
					  				 ,(	6,				4,						'add_admin',				'Vytvorenie nového administrátora',						'yes',		NULL)
					  				 ,(	7,				4,						'add_user',					'Vytvorenie nového používateľa',						'yes',		NULL)
					  				 ,(	8,				4,						'edit_superadmin',			'Editovanie super administrátora',						'yes',		NULL)
					  				 ,(	9,				4,						'edit_admin',				'Editovanie administrátora',							'yes',		NULL)
					  				 ,(	10,				4,						'edit_user',				'Editovanie používateľa',								'yes',		NULL)
					  				 ,(	11,				4,						'edit_admin_owned',			'Editovanie administrátora s vlastníckymi právami',		'yes',		NULL)
					  				 ,(	12,				4,						'edit_user_owned',			'Editovanie používateľa s vlastníckymi právami',		'yes',		NULL)
					  				 ,(	13,				4,						'edit_myself',				'Editovanie svojho profilu',							'yes',		NULL)
					  				 ,(	14,				4,						'delete_superadmin',		'Zmazanie super administrátora',						'yes',		NULL)
					  				 ,(	15,				4,						'delete_admin',				'Zmazanie administrátora',								'yes',		NULL)
					  				 ,(	16,				4,						'delete_user',				'Zmazanie používateľa',									'yes',		NULL)
					  				 ,(	17,				4,						'delete_admin_owned',		'Zmazanie administrátora s vlastníckymi právami',		'yes',		NULL)
					  				 ,(	18,				4,						'delete_user_owned',		'Zmazanie používateľa s vlastníckymi právami',			'yes',		NULL)
									 ,(	19,				4,						'show_user',				'Zobrazenie používateľa',								'yes',		NULL)
									 ,(	20,				4,						'show_user_owned',			'Zobrazenie používateľa s vlastníckymi právami',		'yes',		NULL)
									 
									 ,(	53,				4,						'show_userlist',			'Zobrazenie zoznamu používateľov',						'yes',		NULL)
									 ,(	54,				4,						'show_userlist_owned',		'Zobrazenie zoznamu používateľov s vlastníckymi právami',	'yes',		NULL)
									 ,(	55,				4,						'show_onlinelist',			'Zobrazenie zoznamu prihlásených používateľov',			'yes',		NULL)
									 ,(	56,				4,						'show_accesslog',			'Zobrazenie logu prihlásení/odhlásení',					'yes',		NULL)
									 
					  				 
					  				 ,(	21,				5,						'add_xquestion',				'Vytvorenie novej predotázky',						'yes',		'kega')
					  				 ,(	22,				5,						'edit_xquestion',				'Editovanie predotázky',							'yes',		'kega')
					  				 ,(	23,				5,						'delete_xquestion',				'Zmazanie predotázky',								'yes',		'kega')
					  				 ,(	24,				5,						'show_xquestion',				'Zobrazenie predotázky',							'yes',		'kega')
					  				 ,(	25,				5,						'edit_xquestion_owned',			'Editovanie predotázky s vlastníckymi právami',		'yes',		'kega')
					  				 ,(	26,				5,						'delete_xquestion_owned',		'Zmazanie predotázky s vlastníckymi právami',		'yes',		'kega')
					  				 ,(	27,				5,						'show_xquestion_owned',			'Zobrazenie predotázky s vlastníckymi právami',		'yes',		'kega')
					  				 
									 ,(	28,				5,						'add_xanswer',					'Vytvorenie novej predodpovede',					'yes',		'kega')
					  				 ,(	29,				5,						'edit_xanswer',					'Editovanie predodpovede',							'yes',		'kega')
					  				 ,(	30,				5,						'delete_xanswer',				'Zmazanie predodpovede',							'yes',		'kega')
					  				 ,(	31,				5,						'show_xanswer',					'Zobrazenie predodpovede',							'yes',		'kega')
					  				 ,(	32,				5,						'add_xanswer_owned',			'Vytvorenie novej predodpovede k predotázke s vlastníckymi právami',	'yes',		'kega')
					  				 ,(	33,				5,						'edit_xanswer_owned',			'Editovanie predodpovede s vlastníckymi právami',	'yes',		'kega')
					  				 ,(	34,				5,						'delete_xanswer_owned',			'Zmazanie predodpovede s vlastníckymi právami',		'yes',		'kega')
					  				 ,(	35,				5,						'show_xanswer_owned',			'Zobrazenie predodpovede s vlastníckymi právami',	'yes',		'kega')
					  				 
					  				 ,(	36,				5,						'add_question',					'Vytvorenie novej otázky',							'yes',		'kega')
					  				 ,(	37,				5,						'edit_question',				'Editovanie otázky',								'yes',		'kega')
					  				 ,(	38,				5,						'delete_question',				'Zmazanie otázky',									'yes',		'kega')
					  				 ,(	39,				5,						'show_question',				'Zobrazenie otázky',								'yes',		'kega')
					  				 ,(	40,				5,						'edit_question_owned',			'Editovanie otázky s vlastníckymi právami',			'yes',		'kega')
					  				 ,(	41,				5,						'delete_question_owned',		'Zmazanie otázky s vlastníckymi právami',			'yes',		'kega')
					  				 ,(	42,				5,						'show_question_owned',			'Zobrazenie otázky s vlastníckymi právami',			'yes',		'kega')
					  				 
									 ,(	43,				5,						'add_answer',					'Vytvorenie novej odpovede',						'yes',		'kega')
					  				 ,(	44,				5,						'edit_answer',					'Editovanie odpovede',								'yes',		'kega')
					  				 ,(	45,				5,						'delete_answer',				'Zmazanie odpovede',								'yes',		'kega')
					  				 ,(	46,				5,						'show_answer',					'Zobrazenie odpovede',								'yes',		'kega')
					  				 ,(	47,				5,						'add_answer_owned',				'Vytvorenie novej odpovede k otázke s vlastníckymi právami',	'yes',		'kega')
					  				 ,(	48,				5,						'edit_answer_owned',			'Editovanie odpovede s vlastníckymi právami',		'yes',		'kega')
					  				 ,(	49,				5,						'delete_answer_owned',			'Zmazanie odpovede s vlastníckymi právami',			'yes',		'kega')
					  				 ,(	50,				5,						'show_answer_owned',			'Zobrazenie odpovede s vlastníckymi právami',		'yes',		'kega')
					  				 
					  				 ,(	51,				5,						'convert_xquestion_to_question','Konvertovať predotázku na otázku',					'yes',		'kega')
					  				 ,(	52,				5,						'convert_question_to_xquestion','Konvertovať otázku na predotázku',					'yes',		'kega')
					  				 ,( 57,				5,						'edit_properties',				'Editovať kategórie otázok a predotázok',			'yes', 		'kega')
	";
	if(!db_public_gClass::execute_gFunc($sql_pVar, __FILE__, __LINE__)) {
		return(false);
	}

	stamp_gFunc('_loader', 'prvotna inicializacia tabuliek access__names, access__names_categories');
}

if(!isStamp_gFunc('_loader', 'rights management defs')) {
	
	install_gClass::addAccessName_gFunc('users', 'show_requests', 'Zobrazenie zoznamu registrácií', 'access3_session', false);
	install_gClass::addAccessName_gFunc('users', 'show_requestlog', 'Zobrazenie logu registrácií', 'access3_session', false);
	
	install_gClass::addAccessName_gFunc('system', 'show_roles', 'Zobraziť zoznam rolí', 'access3_rights', true);
	install_gClass::addAccessName_gFunc('system', 'show_role_rights', 'Zobraziť práva rolí', 'access3_rights', true);
	install_gClass::addAccessName_gFunc('system', 'edit_role_rights', 'Editovať práva roly', 'access3_rights', true);
	
	install_gClass::addAccessName_gFunc('system', 'edit_user_rights', 'Editovať práva používateľa', 'access3_rights', true);
	install_gClass::addAccessName_gFunc('system', 'show_user_rights', 'Zobraziť práva používateľa', 'access3_rights', true);
	install_gClass::addAccessName_gFunc('system', 'show_user_groups', 'Zobraziť skupiny používateľa', 'access4_groups', true);
	install_gClass::addAccessName_gFunc('system', 'add_user_group', 'Pridať skupinu používateľovi', 'access4_groups', true);
	install_gClass::addAccessName_gFunc('system', 'delete_user_group', 'Zmazať skupinu používateľovi', 'access4_groups', true);
	
	install_gClass::addAccessName_gFunc('system', 'add_group', 'Pridať novú skupinu', 'access4_groups', true);
	install_gClass::addAccessName_gFunc('system', 'delete_group', 'Zmazať skupinu', 'access4_groups', true);
	
	install_gClass::addAccessName_gFunc('system', 'show_group_rights', 'Zobraziť práva skupiny', 'access4_groups', true);
	install_gClass::addAccessName_gFunc('system', 'edit_group_rights', 'Editovať práva skupiny', 'access4_groups', true);
	install_gClass::addAccessName_gFunc('system', 'show_groups', 'Zobraziť zoznam skupín', 'access4_groups', true);
	
	install_gClass::addAccessName_gFunc('system', 'show_group_roles', 'Zobraziť roly v skupine', 'access4_groups', true);
	install_gClass::addAccessName_gFunc('system', 'show_group_users', 'Zobraziť používateľov v skupine', 'access4_groups', true);	
	install_gClass::addAccessName_gFunc('system', 'add_group_role', 'Pridať rolu do skupiny', 'access4_groups', true);
	install_gClass::addAccessName_gFunc('system', 'delete_group_role', 'Zmazať rolu zo skupiny', 'access4_groups', true);
	install_gClass::addAccessName_gFunc('system', 'add_group_user', 'Pridať používateľa do skupiny', 'access4_groups', true);
	install_gClass::addAccessName_gFunc('system', 'delete_group_user', 'Zmazať používateľa zo skupiny', 'access4_groups', true);
	
	install_gClass::addAccessName_gFunc('system', 'show_myself_rights', 'Zobraziť svoje práva', 'access3_groups', true);
	
	install_gClass::addAccessName_gFunc('system', 'show_backup_log', 'Zobraziť log zálohovania', 'backup', true);
	install_gClass::addAccessName_gFunc('system', 'show_install_log', 'Zobraziť log inštalácií', '_loader', true);
	install_gClass::addAccessName_gFunc('system', 'debug', 'Môže debugovať systém.', '_loader', true);
	
	install_gClass::addAccessName_gFunc('users', 'export_csv', 'Exportovať používateľov do CSV súboru.', 'access3_session', true);
	install_gClass::addAccessName_gFunc('users', 'export_xml', 'Exportovať používateľov do XML súboru.', 'access3_session', true);
	install_gClass::addAccessName_gFunc('users', 'export_txt', 'Exportovať používateľov do TXT súboru.', 'access3_session', true);
	install_gClass::addAccessName_gFunc('users', 'export_pdf', 'Exportovať používateľov do PDF súboru.', 'access3_session', true);
	
	install_gClass::addAccessName_gFunc('test', 'questions_export_csv', 'Exportovať otázky do CSV súboru.', 'kega', true);
	install_gClass::addAccessName_gFunc('test', 'questions_export_xml', 'Exportovať otázky do XML súboru.', 'kega', true);
	install_gClass::addAccessName_gFunc('test', 'questions_export_txt', 'Exportovať otázky do TXT súboru.', 'kega', true);
	install_gClass::addAccessName_gFunc('test', 'questions_export_pdf', 'Exportovať otázky do PDF súboru.', 'kega', true);
	
	install_gClass::addAccessName_gFunc('system', 'export_data_csv', 'Exportovať data do CSV súboru.', 'export_csv', true);
	install_gClass::addAccessName_gFunc('system', 'export_data_xml', 'Exportovať data do XML súboru.', 'export_xml', true);
	install_gClass::addAccessName_gFunc('system', 'export_data_txt', 'Exportovať data do TXT súboru.', 'export_txt', true);
	install_gClass::addAccessName_gFunc('system', 'export_data_pdf', 'Exportovať data do PDF súboru.', 'export_html', true);
	
	install_gClass::addAccessName_gFunc('system', 'import_data', 'Importovať data zo súboru.', 'import', true);
	
	install_gClass::addAccessName_gFunc('test', 'list_tests', 'Zobraziť zoznam cvičných testov', 'kega', true);
	install_gClass::addAccessName_gFunc('test', 'list_official_tests', 'Zobraziť zoznam oficiálnych testov', 'kega', true);
	install_gClass::addAccessName_gFunc('test', 'list_unactive_tests', 'Zobrazovať neaktívne cvičné testy', 'kega', true);
	install_gClass::addAccessName_gFunc('test', 'list_official_unactive_tests', 'Zobrazovať neaktívne oficiálne testy', 'kega', true);	
	install_gClass::addAccessName_gFunc('test', 'add_test', 'Vytvoriť nový cvičný test', 'kega', true);
	install_gClass::addAccessName_gFunc('test', 'add_official_test', 'Vytvoriť nový oficiálny test', 'kega', true);
	install_gClass::addAccessName_gFunc('test', 'list_templates', 'Zobraziť zoznam šablón testov', 'kega', true);
	install_gClass::addAccessName_gFunc('test', 'add_template', 'Vytvoriť novú šablónu testu', 'kega', true);
	install_gClass::addAccessName_gFunc('test', 'edit_template', 'Editovať šablónu testu', 'kega', true);
	install_gClass::addAccessName_gFunc('test', 'show_template', 'Zobrazenie šablóny testu', 'kega', false);
	install_gClass::addAccessName_gFunc('test', 'delete_template', 'Zmazanie šablóny testu', 'kega', false);
	install_gClass::addAccessName_gFunc('test', 'template_new_test', 'Vyvorenie cvičného testu zo šablóny', 'kega', true);
	install_gClass::addAccessName_gFunc('test', 'template_new_official_test', 'Vyvorenie oficiálneho testu zo šablóny', 'kega', true);
	install_gClass::addAccessName_gFunc('test', 'template_run', 'Spustenie testu zo šablóny', 'kega', true);
	install_gClass::addAccessName_gFunc('test', 'test_run', 'Spustenie testu z pripraveného testu', 'kega', true);
	install_gClass::addAccessName_gFunc('test', 'generate_print', 'Vygenerovanie tlačovej predlohy', 'kega', true);
	install_gClass::addAccessName_gFunc('test', 'display_user_test', 'Zobrazenie testu iného používateľa', 'kega', true);
	install_gClass::addAccessName_gFunc('test', 'display_user_test_prepared', 'Zobrazenie nespusteného testu iného používateľa', 'kega', true);
	install_gClass::addAccessName_gFunc('users', 'add_tester', 'Vytvorenie nového oficiálneho testera', 'kega', true);
	install_gClass::addAccessName_gFunc('users', 'edit_tester', 'Editovanie oficiálneho testera', 'kega', true);
	install_gClass::addAccessName_gFunc('users', 'delete_tester', 'Zmazanie oficiálneho testera', 'kega', true);

	install_gClass::addAccessName_gFunc('test', 'list_instances', 'Zobraziť zoznam inštancii testov', 'kega', true);
	install_gClass::addAccessName_gFunc('test', 'list_my_instances', 'Zobraziť zoznam svojich inštancií testov', 'kega', true);
	install_gClass::addAccessName_gFunc('test', 'report_error', 'Nahlásiť chybnú otázku', 'kega', true);
	install_gClass::addAccessName_gFunc('test', 'show_comments', 'Zobraziť komentáre', 'kega', true);
	install_gClass::addAccessName_gFunc('test', 'delete_comment', 'Zmazať komentáre', 'kega', true);
	install_gClass::addAccessName_gFunc('test', 'delete_owner_comment', 'Zmazať svoj komentár', 'kega', true);
	install_gClass::addAccessName_gFunc('test', 'delete_test', 'Zmazať test', 'kega', true);
	install_gClass::addAccessName_gFunc('test', 'delete_test_data', 'Zmazať test s dátami', 'kega', true);
	
	install_gClass::addAccessName_gFunc('users', 'add_client', 'Vytvoriť nového klienta', 'todo', true);
	install_gClass::addAccessName_gFunc('users', 'add_client_assistant', 'Vytvoriť nového asistenta', 'todo', true);
	install_gClass::addAccessName_gFunc('users', 'add_ceo', 'Vytvoriť nového riaditeľa', 'todo', true);
	install_gClass::addAccessName_gFunc('users', 'add_project_manager', 'Vytvoriť nového projektového manažera', 'todo', true);
	install_gClass::addAccessName_gFunc('users', 'add_worker', 'Vytvoriť nového pracovníka', 'todo', true);
	install_gClass::addAccessName_gFunc('users', 'add_accountant', 'Vytvoriť nového účtovníka', 'todo', true);
	
	install_gClass::addAccessName_gFunc('users', 'show_field_email', 'Čítať používateľský profil (pole \'email\')', 'access2_session', false);
	install_gClass::addAccessName_gFunc('users', 'show_field_last_remote_ip', 'Čítať používateľský profil (pole \'last_remote_ip\')', 'access2_session', false);
	install_gClass::addAccessName_gFunc('users', 'show_field_last_login_ip', 'Čítať používateľský profil (pole \'last_login_ip\')', 'access2_session', false);
	install_gClass::addAccessName_gFunc('users', 'show_field_last_remote_key', 'Čítať používateľský profil (pole \'last_remote_key\')', 'access2_session', false);
	install_gClass::addAccessName_gFunc('users', 'show_field_enabled_remote_ips', 'Čítať používateľský profil (pole \'enabled_remote_ips\')', 'access2_session', false);
	install_gClass::addAccessName_gFunc('users', 'show_field_enabled_remote_keys', 'Čítať používateľský profil (pole \'enabled_remote_keys\')', 'access2_session', false);
	install_gClass::addAccessName_gFunc('users', 'show_field_email_check_status', 'Čítať používateľský profil (pole \'email_check_status\')', 'access2_session', false);
	install_gClass::addAccessName_gFunc('users', 'show_field_date_of_birth', 'Čítať používateľský profil (pole \'date_of_birth\')', 'access2_session', false);
	install_gClass::addAccessName_gFunc('users', 'show_field_mobil', 'Čítať používateľský profil (pole \'mobil\')', 'access2_session', false);
	install_gClass::addAccessName_gFunc('users', 'show_field_password', 'Čítať používateľský profil (pole \'password\')', 'access2_session', false);
	
	install_gClass::addAccessName_gFunc('users', 'edit_myself_field_status', 'Editovať vlastný profil (pole \'status\')', 'access2_session', false);
	install_gClass::addAccessName_gFunc('users', 'edit_myself_field_last_name', 'Editovať vlastný profil (pole \'last_name\')', 'access2_session', false);
	install_gClass::addAccessName_gFunc('users', 'edit_myself_field_first_name', 'Editovať vlastný profil (pole \'first_name\')', 'access2_session', false);
	install_gClass::addAccessName_gFunc('users', 'edit_myself_field_timeout', 'Editovať vlastný profil (pole \'timeout\')', 'access2_session', false);
	install_gClass::addAccessName_gFunc('users', 'edit_myself_field_login_enabled', 'Editovať vlastný profil (pole \'login_enabled\')', 'access2_session', false);
	install_gClass::addAccessName_gFunc('users', 'edit_myself_field_disabled_login_level', 'Editovať vlastný profil (pole \'disabled_login_level\')', 'access2_session', false);
	install_gClass::addAccessName_gFunc('users', 'edit_myself_field_login', 'Editovať vlastný profil (pole \'login\')', 'access2_session', false);
	install_gClass::addAccessName_gFunc('users', 'edit_myself_field_password', 'Editovať vlastný profil (pole \'password\')', 'access2_session', false);
	install_gClass::addAccessName_gFunc('users', 'edit_myself_field_titul_pred', 'Editovať vlastný profil (pole \'titul_pred\')', 'access2_session', false);
	install_gClass::addAccessName_gFunc('users', 'edit_myself_field_titul_za', 'Editovať vlastný profil (pole \'titul_za\')', 'access2_session', false);
	install_gClass::addAccessName_gFunc('users', 'edit_myself_field_nick', 'Editovať vlastný profil (pole \'nick\')', 'access2_session', false);
	install_gClass::addAccessName_gFunc('users', 'edit_myself_field_gender', 'Editovať vlastný profil (pole \'gender\')', 'access2_session', false);
	install_gClass::addAccessName_gFunc('users', 'edit_myself_field_email', 'Editovať vlastný profil (pole \'email\')', 'access2_session', false);
	install_gClass::addAccessName_gFunc('users', 'edit_myself_field_foto', 'Editovať vlastný profil (pole \'foto\')', 'access2_session', false);
	install_gClass::addAccessName_gFunc('users', 'edit_myself_field_user_role', 'Editovať vlastný profil (pole \'user_role\')', 'access2_session', false);
	install_gClass::addAccessName_gFunc('users', 'edit_myself_field_default_language', 'Editovať vlastný profil (pole \'default_language\')', 'access2_session', false);
	install_gClass::addAccessName_gFunc('users', 'edit_myself_field_date_of_birth', 'Editovať vlastný profil (pole \'date_of_birth\')', 'access2_session', false);
	install_gClass::addAccessName_gFunc('users', 'edit_myself_field_address', 'Editovať vlastný profil (pole \'address\')', 'access2_session', false);
	install_gClass::addAccessName_gFunc('users', 'edit_myself_field_mobil', 'Editovať vlastný profil (pole \'mobil\')', 'access2_session', false);
	install_gClass::addAccessName_gFunc('users', 'edit_myself_field_icq', 'Editovať vlastný profil (pole \'icq\')', 'access2_session', false);
	install_gClass::addAccessName_gFunc('users', 'edit_myself_field_msn', 'Editovať vlastný profil (pole \'msn\')', 'access2_session', false);
	install_gClass::addAccessName_gFunc('users', 'edit_myself_field_skype', 'Editovať vlastný profil (pole \'skype\')', 'access2_session', false);
	
	install_gClass::addAccessName_gFunc('users', 'register_user', 'Registrovať sa cez registračný formulár', 'access3_rights', false);
	install_gClass::addAccessName_gFunc('users', 'accept_request', 'Akceptovať registráciu', 'access3_rights', false);
		
	install_gClass::addAccessName_gFunc('todo', 'add_client', 'Vytvoriť nového klienta', 'todo', true);
	install_gClass::addAccessName_gFunc('todo', 'add_project', 'Vytvoriť nový projekt', 'todo', true);
	install_gClass::addAccessName_gFunc('todo', 'add_task', 'Vytvoriť novú úlohu', 'todo', true);
	
	install_gClass::addAccessName_gFunc('users', 'show_field_isic', 'Čítať používateľský profil (pole \'isic\')', 'access2_session', false);
	install_gClass::addAccessName_gFunc('users', 'set_isic_force_ok', 'Schváliť neplatný alebo nevyplnený ISIC', 'access3_rights,kega', false);
	install_gClass::addAccessName_gFunc('users', 'show_field_password_uk', 'Čítať používateľský profil (pole \'password_uk\')', 'access2_session', false);
	
	install_gClass::addAccessName_gFunc('users', 'edit_myself_field_isic', 'Editovať vlastný profil (pole \'isic\')', 'access2_session', false);
	install_gClass::addAccessName_gFunc('users', 'edit_myself_field_rocnik', 'Editovať vlastný profil (pole \'rocnik\')', 'access2_session', false);
	install_gClass::addAccessName_gFunc('users', 'edit_myself_field_smer', 'Editovať vlastný profil (pole \'smer\')', 'access2_session', false);
	install_gClass::addAccessName_gFunc('users', 'edit_myself_field_password_uk', 'Editovať vlastný profil (pole \'password_uk\')', 'access2_session', false);
	
	install_gClass::addAccessName_gFunc('test', 'show_users_count', 'Zobraziť počty otázok na používaťeľov', 'kega', false);

	install_gClass::addAccessName_gFunc('test', 'accept_xquestion', 'Akceptovanie predotázky', 'kega', true);
	install_gClass::addAccessName_gFunc('test', 'reject_xquestion', 'Zamietnutie predotázky', 'kega', true);
	
	install_gClass::addAccessName_gFunc('test', 'rozpis_prace', 'Zobraziť rozpis práce', 'kega', true);
	install_gClass::addAccessName_gFunc('test', 'rozpis_prace_zapis', 'Zapísať sa do rozpisu práce', 'kega', true);
	install_gClass::addAccessName_gFunc('test', 'rozpis_prace_edit', 'Editovať rozpis práce', 'kega', true);
	install_gClass::addAccessName_gFunc('test', 'rozpis_prace_delete', 'Zmazať záznam z rozpisu práce', 'kega', true);
	install_gClass::addAccessName_gFunc('test', 'rozpis_prace_archive', 'Archivovať rozpis práce', 'kega', true);
	
	install_gClass::addAccessName_gFunc('test', 'edit_from_test', 'Editovať otázku z testu', 'kega', true);

	install_gClass::addAccessNameCategory_gFunc('cdouk', 'Centrálna databáza osôb UK', 'cdouk');
	install_gClass::addAccessName_gFunc('cdouk', 'checkisic', 'Aktualizovať data z CDO-UK', 'cdouk', false);
	
	install_gClass::addAccessName_gFunc('document', 'edit', 'Editovať dokumenty', 'cms_edit', false);
	
	
	install_gClass::addAccessName_gFunc('users', 'add_pacient', 'Pridať nového pacienta', 'av', true);
	install_gClass::addAccessName_gFunc('users', 'edit_pacient', 'Editovať pacienta', 'av', true);
	
	stamp_gFunc('_loader', 'rights management defs');
	
	main_gClass::initRights_gFunc(true);
}



return(true);