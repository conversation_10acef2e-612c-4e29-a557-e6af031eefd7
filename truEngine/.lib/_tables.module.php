<?php
if(isset($_SERVER['SCRIPT_FILENAME']) && strtolower($_SERVER['SCRIPT_FILENAME']) == strtolower(str_replace("\\",'/',__FILE__))) { header("HTTP/1.0 404 Not Found");  return(false); }
if(isset($GLOBALS['file__tables_pVar'])) return(true);
$GLOBALS['file__tables_pVar']=true;


class table_gClass extends source_gClass {
	private $objectData_pVar;
	private $objectType_pVar;
	private $objectId_pVar;

	function __construct($action_pVar = 'get', $objectId_pVar = false)
	{
		$this->objectData_pVar = array();

		$this->objectData_pVar['columns'] = array();
		$this->objectData_pVar['rows'] = array();
		$this->objectData_pVar['attributes'] = array();
		$this->objectData_pVar['format'] = array();

		parent::__construct($action_pVar);
		$this->objectType_pVar = $this->getClassName_gFunc();
		if($objectId_pVar === false) {
			$this->objectId_pVar = $this->objectType_pVar;
		}
		else {
			$this->objectId_pVar = $objectId_pVar;
		}
	}

	public function handleAction_gFunc()
	{
		$this->initTable_gFunc();
		return(parent::handleAction_gFunc());
	}

	protected function initTable_gFunc()
	{
		return;
	}

	protected function getData()
	{
		return($this->getTableData_gFunc());
	}

	function getObjectData_gFunc()
	{

		$this->objectData_pVar['table_id'] = $this->objectId_pVar;

		$data_pVar = $this->objectData_pVar;

		return($data_pVar);
	}

	function saveDataToDb_gFunc($tableName_pVar)
	{
		if(!modules_gClass::isModuleRegistred_gFunc('db')) {
			return(false);
		}

		return(true);
	}

	public function setVar_gFunc($varName_pVar, $varValue_pVar)
	{
		$this->objectData_pVar['vars'][$varName_pVar] = $varValue_pVar;
	}

	public function getVar_gFunc($varName_pVar)
	{
		if(isset($this->objectData_pVar['vars'][$varName_pVar])) {
			return($this->objectData_pVar['vars'][$varName_pVar]);
		}
		else {
			return(false);
		}
	}

	public function setAttribute_gFunc($attributeName_pVar, $attributeValue_pVar)
	{
		$this->objectData_pVar['attributes'][$attributeName_pVar] = $attributeValue_pVar;
	}

	function getTableData_gFunc()
	{
		return($this->getObjectData_gFunc());
	}

	function setData_gFunc($data_pVar = array())
	{
		if(isset($data_pVar['_format_handlers'])) {
			$format_handlers_pVar = $data_pVar['_format_handlers'];
		}
		else {
			$format_handlers_pVar = array();
		}
		$this->objectData_pVar['rows'] = $data_pVar;
		foreach ($this->objectData_pVar['rows'] as $krow_pVar=>$row_pVar) {
			if(substr($krow_pVar, 0 , 1) === '_') {
				$this->objectData_pVar[$krow_pVar] = $row_pVar;
				unset($this->objectData_pVar['rows'][$krow_pVar]);
				continue;
			}
			foreach ($row_pVar as $kData=>$vData_pVar) {
				$this->objectData_pVar['rows'][$krow_pVar][$kData] = array('data'=>$vData_pVar, 'value'=>$vData_pVar);
			}
		}
		$this->detectColumnsFromData_gFunc();

		$this->objectData_pVar['format'] = $format_handlers_pVar;
	}

	private function detectColumnsFromData_gFunc()
	{
		$this->objectData_pVar['columns'] = array();
		$row_pVar = reset($this->objectData_pVar['rows']);
		if(is_array($row_pVar)) {
			foreach ($row_pVar as $k_pVar=>$v_pVar) {
				$this->addColumn_gFunc($k_pVar, $k_pVar);
			}
		}
	}

	public function setColumnsFromString_gFunc($colStr_pVar)
	{
		$colData_pVar = explode(',', $colStr_pVar);
		$this->objectData_pVar['columns'] = array();

		foreach ($colData_pVar as $v_pVar) {
			$v_pVar = explode(':', $v_pVar);
			$this->addColumn_gFunc($v_pVar[0], isset($v_pVar[1])?$v_pVar[1]:$v_pVar[0]);
		}
	}

	private function addColumn_gFunc($columnName_pVar, $columnLabel_pVar)
	{
		$this->objectData_pVar['columns'][$columnName_pVar] = array();
		$this->objectData_pVar['columns'][$columnName_pVar]['label'] = $columnLabel_pVar;
	}
}


return(true);
