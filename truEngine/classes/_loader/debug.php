<?php

class debug_gClass {
    protected static $debugEnabled_pVar = false;

    private function __construct()
    {

    }

    public static function enableDebug_gFunc()
    {
        self::$debugEnabled_pVar = true;
    }

    public static function getMicrotime_gFunc()
    {
        list($usec_pVar, $sec_pVar) = explode(' ', microtime());
        return ((float)$usec_pVar + (float)$sec_pVar);
    }

    public static function debugEnabled_gFunc()
    {
        if(debug_gClass::$debugEnabled_pVar && modules_gClass::isModuleRegistred_gFunc('debug')) {
            return(true);
        }
        else {
            return(false);
        }
    }

    public static function traceArray_gFunc($arr_pVar, $title_pVar = '')
    {
        if(debug_gClass::debugEnabled_gFunc()) {
            return(debug_debug_gClass::traceArray_gFunc($arr_pVar, $title_pVar));
        }
        else {
            return('');
        }
    }

    public static function getSystemStatus_gFunc()
    {
        if(debug_gClass::debugEnabled_gFunc()) {
            return(debug_debug_gClass::getSystemStatus_gFunc());
        }
        else {
            return('');
        }
    }

}
