<?php

namespace Mixedtype\Components\View;

use Illuminate\View\ComponentSlot;

class Factory extends \Illuminate\View\Factory
{

    /**
     * Start the slot rendering process.
     *
     * @param  string  $name
     * @param  string|null  $content
     * @param  array  $attributes
     * @return void
     */
    public function slot($name, $content = null, $attributes = [])
    {
        if (func_num_args() === 2 || $content !== null) {
            if(!isset($this->slots[$this->currentComponent()][$name])) {
                $this->slots[$this->currentComponent()][$name] = $content;
            }
            else {
                if(!is_array($this->slots[$this->currentComponent()][$name])) {
                    $this->slots[$this->currentComponent()][$name] = [$this->slots[$this->currentComponent()][$name]];
                }
                $this->slots[$this->currentComponent()][$name][] = $content;
            }

        } elseif (ob_start()) {
            $index = 0;
            if(!isset($this->slots[$this->currentComponent()][$name])) {
                $this->slots[$this->currentComponent()][$name] = '';
            }
            else {
                if(!is_array($this->slots[$this->currentComponent()][$name])) {
                    $this->slots[$this->currentComponent()][$name] = [$this->slots[$this->currentComponent()][$name]];
                }
                $index = count($this->slots[$this->currentComponent()][$name]);
                $this->slots[$this->currentComponent()][$name][$index] = '';
            }

            $this->slotStack[$this->currentComponent()][] = [$name, $attributes, $index];
        }
    }

    /**
     * Save the slot content for rendering.
     *
     * @return void
     */
    public function endSlot()
    {
        last($this->componentStack);

        $currentSlot = array_pop(
            $this->slotStack[$this->currentComponent()]
        );

        [$currentName, $currentAttributes, $index] = $currentSlot;

        if(!$index) {
            $this->slots[$this->currentComponent()][$currentName] = new ComponentSlot(
                trim(ob_get_clean()), $currentAttributes
            );
        }
        else {
            if(!is_array($this->slots[$this->currentComponent()][$currentName])) {
                $this->slots[$this->currentComponent()][$currentName] = [$this->slots[$this->currentComponent()][$currentName]];
            }
            $this->slots[$this->currentComponent()][$currentName][$index] = new ComponentSlot(
                trim(ob_get_clean()), $currentAttributes
            );
        }


    }
}
