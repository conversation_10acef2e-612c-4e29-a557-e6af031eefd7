<?php

class timeFormat_gClass {
    static function time_gFunc($time_pVar = false)
    {
        if($time_pVar === false) {
            $time_pVar = time();
        }
        return('<span class="t_'.date('YmdHis', $time_pVar).'">' . date('Y-m-d H:i:s', $time_pVar) . '<script type="text/javascript">timetmp = datetime(' . $time_pVar . ', \''.main_gClass::getLanguage_gFunc().'\'); if(timetmp) $j(\'.t_'.date('YmdHis', $time_pVar).'\').html(\'<span title="' . date('Y-m-d H:i:s', $time_pVar) . '">\' + timetmp + \'</span>\');</script></span>');
    }
}
