<?xml version="1.0" encoding="utf-8" ?>
<truEngine-document xmlns:te="http://www.truengine.sk">

<te:if test="!empty(@{question:modul})">
	<a href="/otazky?filter=%26status%3D@{question:status}%26modul%3D@{question:modul}" style="color:#aa0000">@{question:modul_enum_name_item}</a>
	<te:if test="!empty(@{question:program})">
		&amp;gt; <a href="/otazky?filter=%26status%3D@{question:status}%26modul%3D@{question:modul}%26program%3D@{question:program}" style="color:#00aa00">@{question:program_enum_name_item}</a>
		<te:if test="!empty(@{question:predmet})">
			&amp;gt; <a href="/otazky?filter=%26status%3D@{question:status}%26modul%3D@{question:modul}%26program%3D@{question:program}%26predmet%3D@{question:predmet}" style="color:#00aaaa">@{question:predmet_enum_name_item}</a>
				<te:if test="!empty(@{question:kategoria})">
					&amp;gt; <a href="/otazky?filter=%26status%3D@{question:status}%26modul%3D@{question:modul}%26program%3D@{question:program}%26predmet%3D@{question:predmet}%26kategoria%3D@{question:kategoria}" style="color:#aaaa00">@{question:kategoria_enum_name_item}</a>
						<te:if test="!empty(@{question:podkategoria})">
							&amp;gt; <a href="/otazky?filter=%26status%3D@{question:status}%26modul%3D@{question:modul}%26program%3D@{question:program}%26predmet%3D@{question:predmet}%26kategoria%3D@{question:kategoria}%26podkategoria%3D@{question:podkategoria}" style="color:#aa00aa">@{question:podkategoria_enum_name_item}</a>
						</te:if>
				</te:if>
		</te:if>
	</te:if>
</te:if>

</truEngine-document>
