<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('routes', function (Blueprint $table) {
            $table->id();
            $table->string('route_name',255)->nullable()->default(null);
            $table->string('request_domain', 255)->nullable()->default(null);
            $table->string('request_url', 255);

            $table->string('site', 100)->nullable()->default(null);
            $table->string('language', 2)->nullable()->default(null);

            $table->string('action_controller', 255)->nullable()->default(null);
            $table->string('action_action', 100);

            $table->string('data_type', 100)->nullable()->default(null);
            $table->bigInteger('data_id')->nullable()->default(null);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('routes');
    }
};
