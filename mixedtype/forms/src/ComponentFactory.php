<?php

namespace Mixedtype\Forms;

class ComponentFactory
{
    static public function createFromComponentId($componentId)
    {
        $componentParams = Component::restoreComponentParams($componentId);
        if(empty($componentParams)) {
            return null;
        }

        $specialParams = [];
        $specialParams['_component_id'] = $componentId;
        $specialParams['_component_class'] = $componentParams['_component_class'];


        $componentClass = $componentParams['_component_class'];
        unset($componentParams['_component_class']);
        unset($componentParams['_component_id']);

        return new $componentClass(...$componentParams);
    }
}
