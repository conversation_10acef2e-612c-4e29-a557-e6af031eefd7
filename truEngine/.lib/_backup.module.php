<?php
if(isset($_SERVER['SCRIPT_FILENAME']) && strtolower($_SERVER['SCRIPT_FILENAME']) == strtolower(str_replace("\\",'/',__FILE__))) { header("HTTP/1.0 404 Not Found");  return(false); }
if(isset($GLOBALS['file__backup_pVar'])) return(true);
$GLOBALS['file__backup_pVar']=true;

if(!modules_gClass::isModuleRegistred_gFunc('forms'))
{
    return(false);
}
if(!modules_gClass::isModuleRegistred_gFunc('tables'))
{
    return(false);
}

define('BACKUP_EOL', CR . NL);

class backup_gClass
{
	private $exported_pVar;
	private $file_pVar;
	private $settings_pVar;
	public $target_pVar;
	private $comment_pVar;
	public $session_id_pVar;
	private $done_pVar;

	function __construct($settings_pVar, $target_pVar = 'download_only', $comment_pVar = '')
	{
		$this->exported_pVar = array();
		$this->settings_pVar = $settings_pVar;
		$this->target_pVar = $target_pVar;
		$this->comment_pVar = $comment_pVar;
		$this->session_id_pVar = false;
		$this->done_pVar = false;
	}

	function backup_gFunc()
	{
		log_gClass::logQueriesOff_gFunc();
		if(count($this->exported_pVar)) {
			$file_mode_pVar = 'ab';
		}
		else {
			$file_mode_pVar = 'wb';
		}

        $dirName_pVar= main_gClass::getConfigVar_gFunc('documents_dir', 'main');
        if(empty($dirName_pVar)) {
                return('error');
        }
        $dirName_pVar .= '.backup/';

        if(!count($this->exported_pVar)) {
	        $fileName_pVar = 'backup_' . time() . '.dat';
	        $this->exported_pVar['fileName_pVar'] = $fileName_pVar;
        }
        else {
        	$fileName_pVar = $this->exported_pVar['fileName_pVar'];
        }
		$this->file_pVar = fopen($dirName_pVar . $fileName_pVar, $file_mode_pVar);
		if(!$this->file_pVar) {
			return('error');
		}

		if(!isset($this->exported_pVar['header'])) {
			$data_pVar = array(
				'version'=>'TruEngine Backup 1.0',
				'settings'=>$this->settings_pVar,
				'target'=>$this->target_pVar,
				'comment'=>addcslashes($this->comment_pVar, BACKUP_EOL), // tu neescapujem spetne lomitko, lebo to je escapovane formularom.
				'backup_time'=>date('Y-m-d H:i:s')
			);
			$this->_backup_header_gFunc($data_pVar);
			$this->exported_pVar['header'] = true;
		}

		$settings_pVar = explode(',', $this->settings_pVar);

		$this->done_pVar = true;

		log_gClass::write_gFunc('SYSTEM_DOCUMENT', '1');

		if($this->done_pVar && in_array('database_data', $settings_pVar)) {
			if(!$this->_backup_items_data_gFunc()) {
				$this->done_pVar = false;
			}
		}

		if($this->done_pVar && in_array('files', $settings_pVar)) {
			if(!$this->_backup_files_gFunc()) {
				$this->done_pVar = false;
			}
		}

		if($this->done_pVar && in_array('statistics', $settings_pVar)) {
			if(!$this->_backup_stats_gFunc()) {
				$this->done_pVar = false;
			}
		}


		fclose($this->file_pVar);

		if($this->done_pVar) {
			$logData_pVar = array('user_id'=>session_gClass::getUserDetail_gFunc('user_id'),
								  'datetime'=>'now()',
								  'type'=>'backup',
								  'settings'=>$this->settings_pVar,
								  'comment'=>addslashes($this->comment_pVar),
								  'target'=>$this->target_pVar,
								  'file'=>$this->exported_pVar['fileName_pVar']);
			db_public_gClass::insertData_gFunc('%tbackup_log', '%d,%r,%s,%s,%s,%s,%s', $logData_pVar, __FILE__, __LINE__);

			if(!empty($this->session_id_pVar)) {
				$this->destroySession($this->session_id_pVar);
				$this->session_id_pVar = false;
			}
			if($this->target_pVar === 'download_only' || $this->target_pVar === 'download_and_save') {
				ob_clean();
				$buffer_pVar = new ob_cache_gClass();
				main_gClass::openTrash_gFunc();
		        $dirName_pVar= main_gClass::getConfigVar_gFunc('documents_dir', 'main');
		        if(empty($dirName_pVar)) {
		                return('error');
		        }
		        $dirName_pVar .= '.backup/';
				$f_pVar = fopen($dirName_pVar . $this->exported_pVar['fileName_pVar'], 'rb');
				while(!feof($f_pVar)) {
					$buffer_pVar->echo_gFunc(fgets($f_pVar));
				}
				unset($buffer_pVar);
				fclose($f_pVar);
				if($this->target_pVar === 'download_only') {
					unlink($dirName_pVar . $this->exported_pVar['fileName_pVar']);
				}
			}
			return('done');
		}
		else {
			if(!empty($this->session_id_pVar)) {
				$this->updateSession($this->session_id_pVar);
			}
			else {
				$this->session_id_pVar = $this->createSession();
			}
			return('continue');
		}
	}

	protected function _backup_header_gFunc($data_pVar)
	{
		fputs($this->file_pVar, serialize($data_pVar) . BACKUP_EOL);
	}

	/**
	 * Vracia true, ak boli exportovane vsetky polozky...
	 * Inak vracia false, a tym signalizuje ze sa bude este pokracovat...
	 * @return unknown_type
	 */
	function _backup_items_data_gFunc()
	{
		if(!modules_gClass::isModuleRegistred_gFunc('items')) {
			return(true);
		}
		$sql_pVar = 'SELECT * FROM `%titems___data` ORDER BY `item_id`';
		$infos_pVar = db_public_gClass::getResultArray_gFunc($sql_pVar, __FILE__, __LINE__);
		if(!$infos_pVar === true) {
			$infos_pVar = array();
		}
		$fields_pVar = count($infos_pVar)?implode('|',array_keys(reset($infos_pVar))):'';

		if(!isset($this->exported_pVar['items__data'])) {
			$this->fputcsv(array('@#', 'items__data',count($infos_pVar), $fields_pVar));
			foreach($infos_pVar as $v_pVar) {
				$this->fputcsv($v_pVar);
			}
			$this->exported_pVar['items__data'] = true;
		}

		foreach($infos_pVar as $v_pVar) {
			if(!empty($v_pVar['name'])) {
				$this->_backup_items_data_by_name_gFunc($v_pVar['name'], $v_pVar);
			}
		}
		if(modules_gClass::isModuleRegistred_gFunc('kega')) {
			$this->_backup_table_gFunc('tests', 'id');
			$this->_backup_table_gFunc('tests__questions', array('test_id','db_question_id'));
			$this->_backup_table_gFunc('tests_running', 'id');
			$this->_backup_table_gFunc('tests_running__questions', array('running_test_id', 'db_question_id'));
			$this->_backup_table_gFunc('test_work', 'item_id');
			$this->_backup_table_gFunc('test_hall_of_fame', 'id');
		}

		$this->_backup_table_gFunc('files', 'file_id');
		$this->_backup_table_gFunc('files_log', 'change_id');
		$this->_backup_table_gFunc('access__change_password', 'request_id');
		$this->_backup_table_gFunc('access__check_mail', 'request_id');
		$this->_backup_table_gFunc('access__log','record_id');

		if(modules_gClass::isModuleRegistred_gFunc('cdouk')) {
			$this->_backup_table_gFunc('cdouk__changes', array('isic','first_checked'), array('isic','first_checked'));
			$this->_backup_table_gFunc('cdouk__status', 'isic', 'isic');
		}

		$this->_backup_table_gFunc('users_accesslog', 'access_id');
		$this->_backup_table_gFunc('users_requestlog', 'request_id');

		return(true);
	}

	private function _backup_items_data_by_name_gFunc($systemName_pVar, $systemInfo_pVar)
	{
		if($systemInfo_pVar['tree_defs'] === 'yes') {
			$this->_backup_table_gFunc('items_' . $systemName_pVar . '__tree__defs', 'tree_id');
			$fieldIds_pVar = array();
			$sql_pVar = 'SELECT * FROM `%titems_' . $systemName_pVar . '__tree__defs`';
			$data_pVar = db_public_gClass::getResultArray_gFunc($sql_pVar, __FILE__, __LINE__);
			foreach($data_pVar as $v_pVar) {
				$tmp_pVar = explode(',', $v_pVar['tree_def']);
				$fieldIds_pVar = array_merge($fieldIds_pVar, $tmp_pVar);
			}
			if(count($fieldIds_pVar)) {
				$tmp_pVar = implode('\',\'', $fieldIds_pVar);
				if(strlen($tmp_pVar)) {
					$tmp_pVar = '\'' . $tmp_pVar . '\'';
					$sql_pVar = 'SELECT `f`.`tag` as `enum_field_tag`, `v`.* FROM `%titems_' . $systemName_pVar . '__values` as `v`
									LEFT JOIN `%titems_' . $systemName_pVar . '__fields` as `f` ON `f`.`field_id` = `v`.`enum_field_id`
									WHERE `f`.`tag` IN (%r)';
					$data_pVar = db_public_gClass::getResultArray_gFunc($sql_pVar, __FILE__, __LINE__, array($tmp_pVar));

					$fields_pVar = count($data_pVar)?implode('|',array_keys(reset($data_pVar))):'';
					$this->fputcsv(array('@#', 'items_' . $systemName_pVar . '__values:tree', count($data_pVar), $fields_pVar, implode(',', $fieldIds_pVar)));
					foreach($data_pVar as $v_pVar) {
						$this->fputcsv($v_pVar);
					}
				}
			}
			$this->_backup_table_gFunc('items_' . $systemName_pVar . '__tree__values', 'tree_rule_id');
		}
		$this->_backup_table_gFunc('items_' . $systemName_pVar . '__data', 'item_id');
		$this->_backup_table_gFunc('items_' . $systemName_pVar . '__comments', 'comment_id');
		$this->_backup_table_gFunc('items_' . $systemName_pVar . '__changes', 'change_id');
		$this->_backup_table_gFunc('items_' . $systemName_pVar . '__change_details', 'change_detail_id');
	}



	private function _backup_stats_gFunc()
	{
		$this->_backup_table_gFunc('log', array('request_id','time_sec','time_usec'), 'request_id');
		$this->_backup_table_gFunc('maintainance__access', array('year','day','document_id','pcid','user_id'), 'pcid');
		$this->_backup_table_gFunc('maintainance__documents', array('document_id'));
		$this->_backup_table_gFunc('maintainance__errors', array('id'));
		$this->_backup_table_gFunc('maintainance__queries_slow', array('id'));
		$this->_backup_table_gFunc('maintainance__queries_base', array('md5'));
		$this->_backup_table_gFunc('maintainance__stats_documents_per_day', array('year','day','document_id'));
		$this->_backup_table_gFunc('maintainance__stats_documents_per_month', array('year','month','document_id'));
		$this->_backup_table_gFunc('maintainance__stats_documents_per_week', array('year','week','document_id'));
		$this->_backup_table_gFunc('maintainance__stats_ext_per_month', array('year','month','type','tag'), array('type', 'tag'));
		$this->_backup_table_gFunc('maintainance__stats_per_day', array('year','day','system'));
		$this->_backup_table_gFunc('maintainance__stats_per_hour', array('year','day','hour','system'));
		$this->_backup_table_gFunc('maintainance__stats_per_month', array('year','month','system'));

		return(true);
	}

	function _backup_files_gFunc()
	{
		$sql_pVar = 'SELECT * FROM `%tfiles`';
		$files_pVar = db_public_gClass::getResultArray_gFunc($sql_pVar, __FILE__, __LINE__);

		$this->fputcsv(array('@#', 'files_data'));

		if(!isset($this->exported_pVar['file_md5'])) {
			$this->exported_pVar['file_md5'] = array();
		}
		$filedefs_pVar = main_gClass::getConfigSectionVar_gFunc('files');
		foreach($files_pVar as $file_pVar) {
			$fileName_pVar = $file_pVar['full_name'];
			if(!isset($filedefs_pVar[$file_pVar['location']])) {
				continue;
			}
			$fileName_pVar = $filedefs_pVar[$file_pVar['location']] . $fileName_pVar;
			if(isset($this->exported_pVar['file_md5'][$file_pVar['md5']])) {
				continue;
			}

			$fileData_pVar = array();
			$fileData_pVar['md5'] = $file_pVar['md5'];
			if(file_exists($fileName_pVar)) {
				$fileData_pVar['content'] = base64_encode(file_get_contents($fileName_pVar));
				$this->fputcsv($fileData_pVar);
				$this->exported_pVar['file_md5'][$file_pVar['md5']] = true;
			}
		}

		$this->fputcsv(array('files_data_end'));
		return(true);
	}

	function _backup_table_gFunc($tableNameWithoutPrefix_pVar, $keyName_pVar, $stringKeys_pVar = array())
	{
		if(!is_array($keyName_pVar)) {
			$keyName_pVar = array($keyName_pVar);
		}
		if(!is_array($stringKeys_pVar)) {
			$stringKeys_pVar = array($stringKeys_pVar);
		}
		if(!isset($this->exported_pVar[$tableNameWithoutPrefix_pVar])
		|| $this->exported_pVar[$tableNameWithoutPrefix_pVar] !== true) {
			if(isset($this->exported_pVar[$tableNameWithoutPrefix_pVar])) {
				$limit_id_pVar = $this->exported_pVar[$tableNameWithoutPrefix_pVar];
			}
			else {
				$limit_id_pVar = array();
				foreach($keyName_pVar as $vv_pVar) {
					if(in_array($vv_pVar, $stringKeys_pVar)) {
						$limit_id_pVar[$vv_pVar] = ' ';
					}
					else {
						$limit_id_pVar[$vv_pVar] = -1;
					}
				}
			}
			$limit_count_pVar = 1000;
			$last_id_pVar = array();
			foreach($keyName_pVar as $vv_pVar) {
				if(in_array($vv_pVar, $stringKeys_pVar)) {
					$last_id_pVar[$vv_pVar] = ' ';
				}
				else {
					$last_id_pVar[$vv_pVar] = -1;
				}
			}
			while(1) {
				//$where_str_pVar = implode('` > %d AND `', $keyName_pVar) . '` > %d';
				$order_by_string_pVar = implode('`,`', $keyName_pVar);

				$where_str_pVar = '';
				$n_pVar = count($keyName_pVar);
				foreach($keyName_pVar as $key_pVar) {
					$n_pVar--;
					if(!empty($where_str_pVar)) {
						$where_str_pVar .= ' AND ';
					}
					$where_str_pVar .= '`' . $key_pVar . '` ';
					if(!$n_pVar) {
						$where_str_pVar .= '>';
					}
					else {
						$where_str_pVar .= '>=';
					}
					if(in_array($key_pVar, $stringKeys_pVar)) {
						 $where_str_pVar .= '%s';
					}
					else {
						$where_str_pVar .= '%d';
					}
				}

				$sql_pVar = 'SELECT * FROM `%t' . $tableNameWithoutPrefix_pVar . '` WHERE ' . $where_str_pVar . ' ORDER BY `' . $order_by_string_pVar . '` LIMIT 0,%d';
                $params_pVar = array_merge($limit_id_pVar, array($limit_count_pVar));


                if($tableNameWithoutPrefix_pVar === 'cdouk__changes') dd($sql_pVar, array_merge($limit_id_pVar, array($limit_count_pVar)));
				$data_pVar = db_public_gClass::getResultArray_gFunc($sql_pVar, __FILE__, __LINE__, $params_pVar);
				if($data_pVar === true) {
					$data_pVar = array();
				}
				$fields_pVar = count($data_pVar)?implode('|',array_keys(reset($data_pVar))):'';
				$this->fputcsv(array('@#', $tableNameWithoutPrefix_pVar, count($data_pVar), $fields_pVar));
				$last_id_pVar = array();
				foreach($keyName_pVar as $vv_pVar) {
					if(in_array($vv_pVar, $stringKeys_pVar)) {
						$last_id_pVar[$vv_pVar] = ' ';
					}
					else {
						$last_id_pVar[$vv_pVar] = -1;
					}
				}
				foreach($data_pVar as $v_pVar) {
					$this->fputcsv($v_pVar);
					$last_id_pVar = array();
					foreach($keyName_pVar as $vv_pVar) {
						$last_id_pVar[$vv_pVar] = $v_pVar[$vv_pVar];
					}
				}
				if(count($data_pVar) < $limit_count_pVar) {
					break;
				}
				$limit_id_pVar = $last_id_pVar;
			}
			if(count($data_pVar) < $limit_count_pVar) {
				$this->exported_pVar[$tableNameWithoutPrefix_pVar] = true;
				return(true);
			}
			else {
				$this->exported_pVar[$tableNameWithoutPrefix_pVar] = $last_id_pVar;
				return(false);
			}
		}
	}

	function fputcsv($fields_pVar)
	{
		$str_pVar = '';
		$i_pVar = 0;
		foreach($fields_pVar as $v_pVar) {
			if($i_pVar) {
				$str_pVar .= chr(0xb7);
			}
			if(is_null($v_pVar)) {
				$str_pVar .= '\N';
			}
			elseif(is_numeric($v_pVar)) {
				$str_pVar .= $v_pVar;
			}
			else {
				//$str_pVar .= '' . addcslashes($v_pVar, BACKUP_EOL . '\\"') . '';
				$str_pVar .= str_replace(LF, chr(0xa8), str_replace(CR, chr(0xb8), $v_pVar));

			}
			$i_pVar++;
		}
		fputs($this->file_pVar, $str_pVar . BACKUP_EOL);
	}


	function createSession()
	{
        $dirName_pVar= main_gClass::getConfigVar_gFunc('documents_dir', 'main');
        if(empty($dirName_pVar)) {
                return(false);
        }
        $session_id_pVar = md5('truEngine' . time() . rand(0,100));
        if($this->updateSession($session_id_pVar)) {
        	return($session_id_pVar);
        }
        else {
	        return(false);
        }
	}

	function continueSession($session_id_pVar)
	{
	    $dirName_pVar= main_gClass::getConfigVar_gFunc('documents_dir', 'main');
        if(empty($dirName_pVar)) {
                return(false);
        }
        $dirName_pVar .= '.tmp/';
	    $fileName_pVar = 'session_' . $session_id_pVar . '.dat';
		$data_pVar = file_get_contents($dirName_pVar . $fileName_pVar);
		if($data_pVar === false) {
			return(false);
		}
		$data_pVar = unserialize($data_pVar);
		if(!is_array($data_pVar)) {
			return(false);
		}

		$this->exported_pVar = $data_pVar[0];
		$this->settings_pVar = $data_pVar[1];
		$this->target_pVar = $data_pVar[2];
		$this->comment_pVar = $data_pVar[3];
		$this->session_id_pVar = $session_id_pVar;
		return(true);
	}

	function updateSession($session_id_pVar)
	{
	    $dirName_pVar= main_gClass::getConfigVar_gFunc('documents_dir', 'main');
        if(empty($dirName_pVar)) {
                return(false);
        }
        $dirName_pVar .= '.tmp/';
	    $fileName_pVar = 'session_' . $session_id_pVar . '.dat';

	    $data_pVar = array($this->exported_pVar, $this->settings_pVar, $this->target_pVar, $this->comment_pVar);
	    file_put_contents($dirName_pVar . $fileName_pVar, serialize($data_pVar));
	    return(true);
	}

	function destroySession($session_id_pVar)
	{
	    $dirName_pVar= main_gClass::getConfigVar_gFunc('documents_dir', 'main');
        if(empty($dirName_pVar)) {
                return(false);
        }
        $dirName_pVar .= '.tmp/';
	    $fileName_pVar = 'session_' . $session_id_pVar . '.dat';
	    if(file_exists($dirName_pVar . $fileName_pVar)) {
	    	unlink($dirName_pVar . $fileName_pVar);
	    }
	    return(true);
	}
}



class backup_continue_gClass extends source_gClass {
	protected function getData()
	{
		if(!isset($this->params['session']) || empty($this->params['session'])) {
			return(array('result'=>'none'));
		}
		if(!preg_match('/^[a-z0-9]{32}$/i', $this->params['session'])) {
			return(array('result'=>'error'));
		}
		$session_pVar = $this->params['session'];

		$backup_pVar = new backup_gClass(false);
		if($backup_pVar->continueSession($session_pVar)) {
			$result_pVar = $backup_pVar->backup_gFunc();
			if($result_pVar === 'continue') {
				return(array('result'=>$result_pVar, 'target'=>$backup_pVar->target_pVar, 'session'=>$backup_pVar->session_id_pVar));
			}
			else {
				return(array('result'=>$result_pVar, 'target'=>$backup_pVar->target_pVar));
			}
		}
		else {
			return(array('result'=>'error'));
		}
	}
}

class backup_continue extends backup_continue_gClass {}

class backup_form_gClass extends form_gClass {

	protected function initForm_gFunc($multiedit_pVar = false, $initFormRef_pVar = true)
	{
		$this->addFieldset_gFunc('settings', 'Nastavenia');

		$this->addField_gFunc('settings', 'backup_settings', 'set', 'Nastavenia zálohovania');
		$options_pVar = array();
		$options_pVar['database_data'] = 'data aplikácie';
		//@TODO: dorobit ostatne veci
		//$options_pVar['database_system'] = 'systémové data';
		$options_pVar['files'] = 'súbory';
		//$options_pVar['settings'] = 'konfiguráciu a nastavenia';
		//$options_pVar['templates'] = 'šablóny dokumentov';
		//$options_pVar['www_documents'] = 'www dokumenty';
		$options_pVar['statistics'] = 'systémové štatistiky';
		$this->setFieldOptions_gFunc('settings', 'backup_settings', $options_pVar);

		$this->addField_gFunc('settings', 'comment', 'text', 'Komentár k zálohe');

		$this->addField_gFunc('settings', 'backup_target', 'enum', 'Zálohu uložiť');
		$options_pVar = array();
		$options_pVar['download_only'] = 'neukladať na server iba stiahnuť do súboru';
		$options_pVar['download_and_save'] = 'uložiť na server a stiahnuť do súboru';
		$options_pVar['save_only'] = 'iba uložiť na server';
		$this->setFieldOptions_gFunc('settings', 'backup_target', $options_pVar);

		if(isset($this->params['submit_button_title'])) {
			$this->setVar_gFunc('submit_button_title', $this->params['submit_button_title'], false);
		}
	}

	protected function getData()
	{
		$data_pVar = $this->getFormData_gFunc();
		if($data_pVar['error_code'] !== self::RESULT_OK_pVar) {
			return($data_pVar);
		}

		$ret_pVar = $this->saveData_gFunc();

		$data_pVar = $this->getFormData_gFunc();
		foreach($ret_pVar as $k_pVar=>$v_pVar) {
			$data_pVar[$k_pVar] = $v_pVar;
		}

		return($data_pVar);
	}

	protected function saveData_gFunc()
	{
		$backup_pVar = new backup_gClass(
			$this->getFieldValue_gFunc('backup_settings')
			, $this->getFieldValue_gFunc('backup_target')
			, $this->getFieldValue_gFunc('comment'));

		$result_pVar = $backup_pVar->backup_gFunc();
		if($result_pVar == 'continue') {
			return(array('result'=>$result_pVar, 'target'=>$backup_pVar->target_pVar, 'session'=>$backup_pVar->session_id_pVar));
		}
		else {
			return(array('result'=>$result_pVar, 'target'=>$backup_pVar->target_pVar));
		}

	}

}

class backup_form extends backup_form_gClass {}

class backuplog_gClass extends table_gClass
{
	private function getBackupLog_gFunc($filter_pVar = array())
	{
    	if(!session_gClass::userHasRightsAccessAction_gFunc(s_system_show_backup_log)) {
    		return(array());
    	}

		if(isset($filter_pVar['pager'])) {
			$pager_pVar = explode(',', $filter_pVar['pager']);
			if(intval($pager_pVar[1])) {
				$pager_pVar[1] = intval($pager_pVar[1]) - 1;
			}
			unset($filter_pVar['pager']);
		}
		else {
			$pager_pVar = false;
		}

		if(is_array($pager_pVar) && count($pager_pVar) == 2 && intval($pager_pVar[0]) > 0) {
			$page_pVar = intval($pager_pVar[1]);
			$pageLen_pVar = intval($pager_pVar[0]);
			$offset_pVar = $pageLen_pVar * $page_pVar;
			$limit_str_pVar = ' LIMIT ' . $offset_pVar . ', ' . $pageLen_pVar;
		}
		else {
			$limit_str_pVar = '';
			$pager_pVar = false;
		}

    	$sql_pVar = 'select `ud`.`login`, `al`.`datetime`, `al`.`type`, `al`.`settings`, concat_ws(\'<br />\', `al`.`comment`, `al`.`file`) as `comment`, `al`.`target` FROM `%tbackup_log` as `al` LEFT JOIN `%titems_users__data` as `ud` ON `ud`.`item_id` = `al`.`user_id` order by `al`.`datetime` desc' . $limit_str_pVar;
    	$data_pVar = db_public_gClass::getResultArray_gFunc($sql_pVar, __FILE__, __LINE__, 'user_id');

		if($pager_pVar !== false) {
			$sql_pVar = 'SELECT count(`id`) as `n` FROM `%tbackup_log`';
			$pager_pVar[2] = db_public_gClass::getField_gFunc($sql_pVar, __FILE__, __LINE__, false);

			$data_pVar['_pager'] = array();
			$data_pVar['_pager']['pageLen'] = $pager_pVar[0];
			$data_pVar['_pager']['currentPage'] = (int)$pager_pVar[1] + 1;
			$data_pVar['_pager']['totalItems'] = $pager_pVar[2];
			$data_pVar['_pager']['totalPages'] = ceil($data_pVar['_pager']['totalItems'] / $data_pVar['_pager']['pageLen']);
		}

    	return($data_pVar);
	}

	protected function initTable_gFunc()
	{
		$data_pVar = self::getBackupLog_gFunc($this->params);

		$this->setData_gFunc($data_pVar);
		if(isset($this->params['columns'])) {
			$this->setColumnsFromString_gFunc($this->params['columns']);
		}
	}
}

class backuplog extends backuplog_gClass {}

return(true);
