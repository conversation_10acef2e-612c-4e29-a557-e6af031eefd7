<?xml version="1.0" encoding="utf-8" ?>
<truEngine-document xmlns:te="http://www.truengine.sk">

<form method="post" action="@{submit}">
<table style="width:100%" class="tabulka">
<te:set ref="@{last_prefix}" value="" />
<te:for each="@{rights:actions} as @{k}=>@{action}">
	<?php
		if(isset(@{rights:rights}[@{action:access_id}])) {
			if(@{rights:rights}[@{action:access_id}]) {
				@{access} = 'allow';
				@{class} = 'rights_allow';
			}
			else {
				@{access} = 'deny';
				@{class} = 'rights_deny';
			}
		}
		else {
			@{access} = '';
			@{class} = 'rights_none';
		}

		@{from} = array();
		if(isset(@{rights:from}[@{action:access_id}])) {
			foreach(@{rights:from}[@{action:access_id}] as $kk=>$vv) {
					$tmp = array();
					foreach($vv as $kkk=>$vvv) {
						foreach($vvv as $kkkk=>$vvvv) { $vvv[$kkkk] = intval($vvvv); }
						$tmpx = implode(',', $vvv);
						if($kkk === 0 || $kk === 'user') {
							$tmpx = str_replace('1', '<span class="rights_allow_txt">'.$kk.'</span>', $tmpx);
							$tmpx = str_replace('0', '<span class="rights_deny_txt">'.$kk.'</span>', $tmpx);
							@{from}[] = $tmpx;
							continue;
						}
						$tmpx = str_replace('1', '<span class="rights_allow_txt">'.$kkk.'</span>', $tmpx);
						$tmpx = str_replace('0', '<span class="rights_deny_txt">'.$kkk.'</span>', $tmpx);
						$tmp[] = $tmpx;
					}
					if(count($tmp)) {
						@{from}[] = $kk . '(' . implode(',',$tmp) . ')';
					}
			}
		}
		@{from} = implode(', ', @{from});
	?>
	<te:if test="@{last_prefix} != @{action:prefix}">
		<tr>
			<td colspan="5">
				<h3>@{action:category_name}</h3>
			</td>
		</tr>
		<tr>
			<th></th>
			<th>ID</th>
			<th><te:string id="Stav" /></th>
			<th><te:string id="Názov" /></th>
			<th><te:string id="Získané z" /></th>
		</tr>
		<te:set ref="@{last_prefix}" value="@{action:prefix}" />
	</te:if>
	<tr class="@{class}">
		<td>
			<te:access access="@{edit_access}">
				<input type="checkbox" name="right_@{action:access_id}" />
				<te:else />
			</te:access>
		</td>
		<td><span title="@{action:prefix}: @{action:alias}">@{action:access_id}</span></td>
		<td>@{access}</td>
		<td>@{action:name}</td>
		<td>@{from}</td>
	</tr>
</te:for>
</table>

<te:access access="@{edit_access}">
	<input type="hidden" name="selected" value="" te:print="no" />
	<input type="button" value="@L{Označené povoliť (allow)}" class="rights_allow_button" onclick="this.form.selected.value='allow'; this.form.submit();" te:print="no" />
	<input type="button" value="@L{Označené zakázať (deny)}" class="rights_deny_button" onclick="this.form.selected.value='deny'; this.form.submit();" te:print="no" />
	<input type="button" value="@L{Označené zrušiť (bez nastavenia)}" class="rights_none_button" onclick="this.form.selected.value='unset'; this.form.submit();" te:print="no" />
</te:access>

</form>

</truEngine-document>
