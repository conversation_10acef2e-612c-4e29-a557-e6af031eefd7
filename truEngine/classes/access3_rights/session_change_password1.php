<?php

class session_change_password1_gClass extends form_gClass
{
    protected function initForm_gFunc($multiedit_pVar = false, $initFormRef_pVar = true)
    {
        $this->addField_gFunc('', 'email', 'varchar', 'E-mail', true);
    }

    protected function getData()
    {
        $formData_pVar = $this->getFormData_gFunc();
        if($formData_pVar['error_code'] !== self::RESULT_OK_pVar) {
            return($formData_pVar);
        }

        $email_pVar = $this->getFieldValue_gFunc('email');
        $no_change_pass = $this->getFieldValue_gFunc('no_change_pass');
        $no_change_pass = (empty($no_change_pass) || !$no_change_pass) ? 0:1;

        // pozriem ci existuje pouzivatel s danou adresou
        $users_pVar = db_session_gClass::getUserDataByEmail_gFunc($email_pVar);
        if(is_array($users_pVar)) {
            $time_limit = date('Y-m-d H:i:s', time() + 3600);
            $hash_pVar = sha1($email_pVar . time());

            if($no_change_pass) {
                $subject_pVar = 'Žiadosť o odblokovanie konta.';
            }
            else {
                $subject_pVar = 'Žiadosť o zmenu hesla.';
            }

            $template_pVar = '/.emails/change_password';
            $template_pVar = main_gClass::addLngPrefixToDocName_gFunc($template_pVar);

            $vars_pVar = array('mail'=>array());
            $vars_pVar['mail']['email'] = $email_pVar;
            $vars_pVar['mail']['url'] = main_gClass::getServerVar_gFunc('SERVER_NAME') . main_gClass::getConfigVar_gFunc('web_dir_gVar', 'runtime_pVar');
            $vars_pVar['mail']['url_change_pass'] = 'http://' . main_gClass::getServerVar_gFunc('SERVER_NAME') . main_gClass::makeUrl_gFunc(main_gClass::addLngPrefixToDocName_gFunc('/log-in/nove-heslo'));
            $vars_pVar['mail']['time'] = $time_limit;

            $vars_pVar['mail']['link'] = 'http://' . main_gClass::getServerVar_gFunc('SERVER_NAME') . main_gClass::makeUrl_gFunc(main_gClass::addLngPrefixToDocName_gFunc('/log-in/zmena-hesla'));
            if(strpos($vars_pVar['mail']['link'], '?') !== false) {
                $vars_pVar['mail']['link'] .= '&';
            }
            else {
                $vars_pVar['mail']['link'] .= '?';
            }
            $vars_pVar['mail']['link'] .= 'email=' . $email_pVar . '&hash=' . $hash_pVar;

            $templateContent_pVar = callStack_gClass::getDocContent_gFunc($template_pVar, $vars_pVar);
            email_gClass::mailHtml_gFunc($email_pVar, main_gClass::getConfigVar_gFunc('email', 'contacts'), $subject_pVar, $templateContent_pVar);
            $this->setVar_gFunc('msg', 'ok');

            // ulozim do DB
            $data_pVar = array(
                'email'=>$email_pVar,
                'hash'=>$hash_pVar,
                'request_time'=>date('Y-m-d H:i:s'),
                'destroy_time'=>$time_limit,
                'no_change_pass'=>$no_change_pass
            );
            db_public_gClass::insertData_gFunc('%taccess__change_password', '%s,%s,%s,%s,%d', $data_pVar, __FILE__, __LINE__);
        }
        else {
            $this->setVar_gFunc('msg', 'fail');
        }
        $this->setVar_gFunc('email', $email_pVar);

        return(parent::getData());
    }
}

class session_change_password1 extends session_change_password1_gClass {}

