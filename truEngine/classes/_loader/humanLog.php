<?php

class humanLog_gClass
{
    static function log_gFunc($event_pVar, $param_pVar)
    {
        $user_id_pVar = session_gClass::getUserDetail_gFunc('item_id');
        if(!$user_id_pVar) {
            $user_id_pVar = main_gClass::getServerVar_gFunc('REMOTE_ADDR');
        }
        $sql_pVar = 'INSERT INTO `%thumanlog` (event, event_time, user_id, param1)
								VALUES(%s, now(), %s, %s)';
        db_public_gClass::execute_gFunc($sql_pVar, __FILE__, __LINE__, array($event_pVar, $user_id_pVar, $param_pVar));
    }
}
