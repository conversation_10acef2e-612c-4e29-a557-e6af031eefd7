<?xml version="1.0" encoding="utf-8" ?>
<truEngine-document xmlns:te="http://www.truengine.sk">



<te:for each="@{log} as @{row}">
	<hr />
	<small><?php echo @{row:change.change_type}[0]; ?></small>
	<a href="/pouzivatelia/editovat-pouzivatela?item_id=@{row:change.user_id:0}">@{row:username}</a> <?php echo timeFormat_gClass::time_gFunc(strtotime(@{row:change.change_datetime:0})); ?>
	<small te:print="no"><a href="#" onclick="showHideBlock('@{row:sort}', 'block'); return(false);"><te:string id="zobraziť/skryť detaily" /></a></small>
	<br />
	
	<div style="display:none" id="@{row:sort}">
		<table style="font-size:8pt;" cellspacing="0" cellpadding="0">
			<te:for each="@{row:details.field_name} as @{k}=>@{field_name}">
				<tr>
					<td style="padding-right:20px; padding-left:20px;">
						<te:string id="Pole" />: 
						<te:if test="!empty(@{row:details.value_language}[@{k}])">
							<?php echo @{row:details.value_language}[@{k}]; ?>_
						</te:if>
						@{field_name}
					</td>
					<td>
						<te:string id="Pôvodná hodnota" />:
						<?php echo @{row:details.old_value}[@{k}]; ?>
						<br />
						<te:string id="Nová hodnota" />:
						<?php echo @{row:details.new_value}[@{k}]; ?>
					</td>
				</tr>
				<tr>
					<td><br /></td>
					<td></td>
				</tr>
			</te:for>
		</table>
	</div>
	
</te:for>

</truEngine-document>
