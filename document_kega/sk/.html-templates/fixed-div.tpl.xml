<?xml version="1.0" encoding="utf-8" ?>
<truEngine-document xmlns:te="http://www.truengine.sk">


<script type="text/javascript"><!--
/* Script by: www.jtricks.com
 * Version: 20071127
 * Latest version:
 * www.jtricks.com/javascript/navigation/fixed_menu.html
 */

var @{id} = 
{
    hasInner: typeof(window.innerWidth) == 'number',
    hasElement: document.documentElement != null
       && document.documentElement.clientWidth,

    menu: document.getElementById
        ? document.getElementById('@{id}')
        : document.all
          ? document.all['@{id}']
          : document.layers['@{id}']
};

@{id}.computeShifts = function()
{
    @{id}.shiftX = @{id}.hasInner
        ? pageXOffset
        : @{id}.hasElement
          ? document.documentElement.scrollLeft
          : document.body.scrollLeft;
    if (@{id}.targetLeft > 0)
        @{id}.shiftX += @{id}.targetLeft;
    else
    {
        @{id}.shiftX += 
            (@{id}.hasElement
              ? document.documentElement.clientWidth
              : @{id}.hasInner
                ? window.innerWidth - 20
                : document.body.clientWidth)
            - @{id}.targetRight
            - @{id}.menu.offsetWidth;
    }

    @{id}.shiftY = @{id}.hasInner
        ? pageYOffset
        : @{id}.hasElement
          ? document.documentElement.scrollTop
          : document.body.scrollTop;
    if (@{id}.targetTop > 0)
        @{id}.shiftY += @{id}.targetTop;
    else
    {
        @{id}.shiftY += 
            (@{id}.hasElement
            ? document.documentElement.clientHeight
            : @{id}.hasInner
              ? window.innerHeight - 20
              : document.body.clientHeight)
            - @{id}.targetBottom
            - @{id}.menu.offsetHeight;
    }
};

@{id}.moveMenu = function()
{
    @{id}.computeShifts();

    if (@{id}.currentX != @{id}.shiftX
        || @{id}.currentY != @{id}.shiftY)
    {
        @{id}.currentX = @{id}.shiftX;
        @{id}.currentY = @{id}.shiftY;

        if (document.layers)
        {
            @{id}.menu.left = @{id}.currentX;
            @{id}.menu.top = @{id}.currentY;
        }
        else
        {
            @{id}.menu.style.left = @{id}.currentX + 'px';
            @{id}.menu.style.top = @{id}.currentY + 'px';
        }
    }

    @{id}.menu.style.right = '';
    @{id}.menu.style.bottom = '';
};

@{id}.floatMenu = function()
{
    @{id}.moveMenu();
    setTimeout('@{id}.floatMenu()', 20);
};

// addEvent designed by Aaron Moore
@{id}.addEvent = function(element, listener, handler)
{
    if(typeof element[listener] != 'function' || 
       typeof element[listener + '_num'] == 'undefined')
    {
        element[listener + '_num'] = 0;
        if (typeof element[listener] == 'function')
        {
            element[listener + 0] = element[listener];
            element[listener + '_num']++;
        }
        element[listener] = function(e)
        {
            var r = true;
            e = (e) ? e : window.event;
            for(var i = 0; i < element[listener + '_num']; i++)
                if(element[listener + i](e) === false)
                    r = false;
            return r;
        }
    }

    //if handler is not already stored, assign it
    for(var i = 0; i < element[listener + '_num']; i++)
        if(element[listener + i] == handler)
            return;
    element[listener + element[listener + '_num']] = handler;
    element[listener + '_num']++;
};

@{id}.supportsFixed = function()
{
    var testDiv = document.createElement("div");
    testDiv.id = "testingPositionFixed";
    testDiv.style.position = "fixed";
    testDiv.style.top = "0px";
    testDiv.style.right = "0px";
    document.body.appendChild(testDiv);
    var offset = 1;
    if (typeof testDiv.offsetTop == "number"
        && testDiv.offsetTop != null 
        && testDiv.offsetTop != "undefined")
    {
        offset = parseInt(testDiv.offsetTop);
    }
    if (offset == 0)
    {
        return true;
    }

    return false;
};

@{id}.init = function()
{
    if (@{id}.supportsFixed())
        @{id}.menu.style.position = "fixed";
    else
    {
        var ob = 
            document.layers 
            ? @{id}.menu 
            : @{id}.menu.style;

        @{id}.targetLeft = parseInt(ob.left);
        @{id}.targetTop = parseInt(ob.top);
        @{id}.targetRight = parseInt(ob.right);
        @{id}.targetBottom = parseInt(ob.bottom);

        if (document.layers)
        {
            menu.left = 0;
            menu.top = 0;
        }
        @{id}.addEvent(window, 'onscroll', @{id}.moveMenu);
        @{id}.floatMenu();
    }
};

@{id}.addEvent(window, 'onload', @{id}.init);
//--></script>

</truEngine-document>
