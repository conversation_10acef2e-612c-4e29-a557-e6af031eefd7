<?php

class log_gClass
{
    private static $nWrites_pVar = 0;
    private static $buffer_pVar = '';
    private static $fileHandler_pVar = false;
    private static $fileName_pVar = false;
    private static $log_queries_pVar = 0; // logujem vsetky query dlhsie trvajuce ako $log_queries_pVar
    private static $log_queries_off_stack_pVar = array();
    private static $bqueries_cache_pVar = array();
    private static $queries_count_pVar = 0; // pocita iba ked je loovanie queries vypnute
    private static $queries_time_pVar = 0; // pocita iba ked je loovanie queries vypnute
    private static $queries_bytes_pVar = 0; // pocita iba ked je loovanie queries vypnute
    private static $firePHPCoreInitialized_pVar = false;
    private static $firePHPCoreBuffer_pVar = array();
    private static $firePHPCoreLock_pVar = false;
    private static $firePHPCoreTags_pVar = array();
    private static $firePHPCoreGroupTags_pVar = array();
    private static $firePHPCoreUserAgent_pVar = false;
    private static $firePHPCoreGroups_pVar = array();
    private static $firePHPCoreActiveGrop_pVar = array();
    private static $scriptStartTime_pVar = 0;
    private static $timestampLastValue_pVar = 0;

    private function __construct()
    {

    }

    /**
     * defaultne je logovanie queries zapnute.. Ja to niekde vypnem zavolanim logQueriesOff_gFunc.
     * Ak chcem opet zapnut, zavolam logQueriesOn_gFunc
     * Ak zadam parameter $timeLimit_pVar, tak vypnem logovanie queries kratsich ako $timeLimit_pVar (teda sa budu logovat iba pomale query)
     *
     * Vypnutie/zapnutie funguje ako stack. Plnim ho logQueriesOff_gFunc, vyprazdnujem s logQueriesOn_gFunc
     *
     * @param $timeLimit_pVar
     * @return unknown_type
     */
    static public function logQueriesOff_gFunc($timeLimit_pVar = 60000)
    {
        array_unshift(self::$log_queries_off_stack_pVar, self::$log_queries_pVar);
        self::$log_queries_pVar = $timeLimit_pVar;

    }

    static public function logQueriesOn_gFunc()
    {
        self::$log_queries_pVar = array_shift(self::$log_queries_off_stack_pVar);
        if(self::$log_queries_pVar === false) {
            self::$log_queries_pVar = 0;
        }
        self::flushQueries_gFunc();
    }

    static public function flushQueries_gFunc()
    {
        if(self::$queries_count_pVar) {
            self::write_gFunc('DB_QUERIES' , '(' . self::$queries_time_pVar . '; ' . self::$queries_count_pVar . '; ' . self::$queries_bytes_pVar . ')');
        }
        self::$queries_count_pVar = 0;
        self::$queries_time_pVar = 0;
        self::$queries_bytes_pVar = 0;

        // a este flushnem bqueries
        if(count(self::$bqueries_cache_pVar)) {
            foreach(self::$bqueries_cache_pVar as $k_pVar=>$v_pVar) {
                self::write_gFunc('DB_BQUERY', '(' . $k_pVar . '/' . $v_pVar['queries_count_pVar'] . '/' . $v_pVar['queries_time_pVar'] . '/' . base64_encode($v_pVar['location_pVar']) . '/' . base64_encode($v_pVar['query_string_pVar']). '/' . base64_encode(serialize($v_pVar['query_params_pVar'])) . ')');
            }
            self::$bqueries_cache_pVar = array();
        }
    }

    static public function cleanOld_gFunc()
    {
        $max_life_time_pVar = main_gClass::getConfigVar_gFunc('log_max_live_days', 'main');
        if(!$max_life_time_pVar) {
            $max_life_time_pVar = 90;
        }

        $max_life_time_pVar = (time() - ($max_life_time_pVar * 24 * 3600));

        $dirName_pVar= main_gClass::getConfigVar_gFunc('documents_dir', 'main');
        if(empty($dirName_pVar)) {
            return(false);
        }
        $dirName_pVar .= '.log/';

        $d = dir($dirName_pVar);
        while (false !== ($entry_pVar = $d->read())) {
            if(empty($entry_pVar) || $entry_pVar[0] == '.') {
                continue;
            }
            if(!is_dir($dirName_pVar . $entry_pVar)) {
                continue;
            }

            $nSkipped_pVar = 0;
            $d2 = dir($dirName_pVar . $entry_pVar);
            while (false !== ($entry2_pVar = $d2->read())) {
                if($entry2_pVar == '.' || $entry2_pVar == '..') {
                    continue;
                }
                $t = intval($entry2_pVar);
                if($t) {
                    if($t < $max_life_time_pVar) {
                        unlink($dirName_pVar . $entry_pVar . '/' . $entry2_pVar);
                        continue;
                    }
                }
                $nSkipped_pVar++;
            }
            if(!$nSkipped_pVar) {
                rmdir($dirName_pVar . $entry_pVar);
            }
        }
        $d->close();
        return(true);
    }

    static public function close_gFunc()
    {
        if(self::$fileHandler_pVar !== false) {
            self::flushQueries_gFunc();
            self::flush();
            log_gClass::write_gFunc('END', sprintf('%0.6f',debug_gClass::getMicrotime_gFunc()));
            fclose(self::$fileHandler_pVar);
            self::$fileHandler_pVar = false;
        }
    }

    static public function write_gFunc($messageType_pVar, $message_pVar = '', $flush_pVar = false, $human_pVar = false)
    {

        if($human_pVar) {
            humanLog_gClass::log_gFunc($messageType_pVar, $message_pVar);
        }


        return; // log is disabled

        if(self::$firePHPCoreLock_pVar) {
            return;
        }

        if(in_array($messageType_pVar, array(
            'LOAD_MODULE', 'LOAD_MODULE_OK', 'RIGHTS_OK', 'RIGHTS_FAIL', 'mySQL_transaction_start', 'mySQL_transaction_commit', 'TIMESTAMP'
        ))) {
            if(!main_gClass::isLogEnabled_gFunc($messageType_pVar)) {
                return;
            }
        }

        if($messageType_pVar === 'TIMESTAMP') {
            $timestamp_value_pVar = debug_gClass::getMicrotime_gFunc() - self::$timestampLastValue_pVar;
            self::$timestampLastValue_pVar = debug_gClass::getMicrotime_gFunc();
            $message_pVar = sprintf('(%0.6f) ', $timestamp_value_pVar) . $message_pVar;
        }

        self::$nWrites_pVar++;
        if(self::$fileHandler_pVar === false) {
            $fileName_pVar = self::getFileName_pVar();
            if(!empty($fileName_pVar)) {
                self::$fileHandler_pVar = fopen($fileName_pVar, 'ab');
                if(!self::$fileHandler_pVar) {
                    self::$fileHandler_pVar = false;
                    return;
                }
                self::flush();
            }
        }

        if(self::$nWrites_pVar == 1) {
            if($messageType_pVar === 'TIME_START') {
                self::$scriptStartTime_pVar = $message_pVar;
                self::$timestampLastValue_pVar =  self::$scriptStartTime_pVar;
            }
            self::firePHP_gFunc('LOG_GROUP', 'REQUEST');
            self::write_gFunc($messageType_pVar, $message_pVar, $flush_pVar);
            self::write_gFunc('REQUEST', '(' . main_gClass::getServerVar_gFunc('REQUEST_METHOD'). ') ' . main_gClass::getServerVar_gFunc('REQUEST_URI', main_gClass::SRC_SERVER_pVar), false);
            self::write_gFunc('QUERY', main_gClass::getServerVar_gFunc('QUERY_STRING'), false);
            self::write_gFunc('SERVER_NAME', main_gClass::getServerVar_gFunc('SERVER_NAME') . ':' . main_gClass::getServerVar_gFunc('SERVER_PORT'), false);
            $str_pVar = main_gClass::getServerVar_gFunc('REMOTE_ADDR').':'.main_gClass::getServerVar_gFunc('REMOTE_PORT');
            if(main_gClass::getServerVar_gFunc('REMOTE_HOST') !== null) {
                $str_pVar .= '[' . main_gClass::getServerVar_gFunc('REMOTE_HOST') . ']';
            }
            self::write_gFunc('REMOTE_ADDR', $str_pVar, false);
            self::write_gFunc('USER_AGENT', main_gClass::getServerVar_gFunc('HTTP_USER_AGENT'), false);
            if(main_gClass::getServerVar_gFunc('HTTP_ACCEPT') !== null) {
                self::write_gFunc('HTTP_ACCEPT', main_gClass::getServerVar_gFunc('HTTP_ACCEPT'), false);
            }
            if(main_gClass::getServerVar_gFunc('HTTP_ACCEPT_ENCODING') !== null) {
                self::write_gFunc('HTTP_ACCEPT_ENCODING', main_gClass::getServerVar_gFunc('HTTP_ACCEPT_ENCODING'), false);
            }
            if(main_gClass::getServerVar_gFunc('HTTP_ACCEPT_LANGUAGE') !== null) {
                self::write_gFunc('HTTP_ACCEPT_LANGUAGE', main_gClass::getServerVar_gFunc('HTTP_ACCEPT_LANGUAGE'), false);
            }
            if(main_gClass::getServerVar_gFunc('HTTP_ACCEPT_CHARSET') !== null) {
                self::write_gFunc('HTTP_ACCEPT_CHARSET', main_gClass::getServerVar_gFunc('HTTP_ACCEPT_CHARSET'), false);
            }
            if(main_gClass::getServerVar_gFunc('HTTP_REFERER') !== null) {
                self::write_gFunc('HTTP_REFERER', main_gClass::getServerVar_gFunc('HTTP_REFERER'), false);
            }
            if(main_gClass::getServerVar_gFunc('PHP_AUTH_USER') !== null) {
                self::write_gFunc('AUTH_USER', main_gClass::getServerVar_gFunc('PHP_AUTH_USER'), false);
            }
            if(main_gClass::getServerVar_gFunc('AUTH_TYPE') !== null) {
                self::write_gFunc('AUTH_TYPE', main_gClass::getServerVar_gFunc('AUTH_TYPE'), false);
            }
            self::firePHP_gFunc('LOG_GROUP_END', '');
            return;
        }

        if($message_pVar !== false) {
            $message_pVar = str_replace(LF, chr(0xa8), str_replace(CR, chr(0xb8), $message_pVar));

            $time_pVar = sprintf('%0.06f', debug_gClass::getMicrotime_gFunc());
            $data_pVar = $time_pVar .' (' . $messageType_pVar . ')' . ': ' . $message_pVar;

            $data_pVar = chunk_split($data_pVar, 1000, LF . chr(0xb7));
            $data_pVar = substr($data_pVar, 0, -2);
            $data_pVar .= LF;

            if(!empty(self::$fileHandler_pVar)) {
                //fputs(self::$fileHandler_pVar, $data_pVar);
            }
            else {
                self::$buffer_pVar .= $data_pVar;
            }

            self::firePHP_gFunc($messageType_pVar, $message_pVar, $time_pVar);
            if($human_pVar) {
                humanLog_gClass::log_gFunc($messageType_pVar, $message_pVar);
            }
        }
        if($flush_pVar) {
            self::flush();
        }
    }

    static public function firePHPstartGroup_gFunc($groupName_pVar)
    {
        if(!isset(self::$firePHPCoreGroups_pVar[$groupName_pVar])) {
            self::$firePHPCoreGroups_pVar[$groupName_pVar] = array();
        }
        array_unshift(self::$firePHPCoreActiveGrop_pVar, $groupName_pVar);
    }

    static public function firePHPendGroup_gFunc()
    {
        return;
        array_shift(self::$firePHPCoreActiveGrop_pVar);
    }

    static public function firePHP_gFunc($messageType_pVar, $message_pVar, $time_pVar = false)
    {
        return;
        if(self::$firePHPCoreInitialized_pVar === 0 || self::$firePHPCoreLock_pVar) {
            return;
        }

        if(self::$firePHPCoreUserAgent_pVar === false) {
            self::$firePHPCoreUserAgent_pVar = main_gClass::getServerVar_gFunc('HTTP_USER_AGENT');
            if(!empty(self::$firePHPCoreUserAgent_pVar)) {
                if(!strpos(self::$firePHPCoreUserAgent_pVar, 'FirePHP')) {
                    self::$firePHPCoreInitialized_pVar = 0;
                    return;
                }
            }
            else {
                self::$firePHPCoreUserAgent_pVar = false;
            }
        }

        if(self::$firePHPCoreInitialized_pVar === false) {
            $enable_fire_pVar = main_gClass::getConfigVar_gFunc('fire_php', 'debug');
            if($enable_fire_pVar !== 'enabled') {
                if($enable_fire_pVar !== false) {
                    self::$firePHPCoreInitialized_pVar = 0;
                    return;
                }
            }
            self::$firePHPCoreLock_pVar = true;
            if(modules_gClass::isModuleLoaded_gFunc('access2_session')) {
                if(session_gClass::rightsLoaded_gFunc()) {
                    if(!session_gClass::userHasRightsInfo_gFunc(s_system_debug)) {
                        self::$firePHPCoreInitialized_pVar = 0;
                        self::$firePHPCoreLock_pVar = false;
                        return;
                    }
                    self::$firePHPCoreInitialized_pVar = 1;
                    // nahram FirePHPCore
                    include_once('ext/FirePHPCore/lib/FirePHPCore/FirePHP.class.php');

                    $tags_pVar = main_gClass::getConfigVar_gFunc('fire_php_disabled_tags', 'debug');
                    self::$firePHPCoreTags_pVar = explode(',', $tags_pVar);

                    $tags_pVar = main_gClass::getConfigVar_gFunc('fire_php_grupped_tags', 'debug');
                    self::$firePHPCoreGroupTags_pVar = explode(',', $tags_pVar);

                    foreach(self::$firePHPCoreBuffer_pVar as $v_pVar) {
                        self::_firePHP_gFunc($v_pVar[0], $v_pVar[1], $v_pVar[2]);
                    }
                    self::$firePHPCoreBuffer_pVar = array();
                }
            }
            self::$firePHPCoreLock_pVar = false;
        }

        if(self::$firePHPCoreInitialized_pVar === false) {
            if($time_pVar === false) {
                $time_pVar = sprintf('%0.06f', debug_gClass::getMicrotime_gFunc());
            }
            self::$firePHPCoreBuffer_pVar[] = array($messageType_pVar, $message_pVar, $time_pVar);
        }

        if(self::$firePHPCoreInitialized_pVar === 1) {
            self::_firePHP_gFunc($messageType_pVar, $message_pVar, $time_pVar);
        }
    }

    static public function _firePHP_gFunc($messageType_pVar, $message_pVar, $time_pVar = false)
    {
        $useMessageType_pVar = $messageType_pVar;
        if(substr($messageType_pVar,0,8) === 'DB_QUERY') {
            $useMessageType_pVar = 'DB_QUERY';
        }

        if(!in_array($useMessageType_pVar, self::$firePHPCoreTags_pVar)) {
            if($time_pVar === false) {
                $time_pVar = sprintf('%0.06f', debug_gClass::getMicrotime_gFunc());
            }
            $time_pVar = $time_pVar - self::$scriptStartTime_pVar;
            $time_pVar = sprintf('%0.06f', $time_pVar);

            if(is_array(self::$firePHPCoreActiveGrop_pVar) && count(self::$firePHPCoreActiveGrop_pVar)
                && $messageType_pVar !== 'LOG_GROUP_END'
                && $messageType_pVar !== 'LOG_GROUP'
            ) {
                $title_pVar = reset(self::$firePHPCoreActiveGrop_pVar);
                self::$firePHPCoreGroups_pVar[$title_pVar][] = array($messageType_pVar, $message_pVar, $time_pVar);
                if($title_pVar !== $useMessageType_pVar
                    && in_array($useMessageType_pVar, self::$firePHPCoreGroupTags_pVar)) {
                    if(!isset(self::$firePHPCoreGroups_pVar[$useMessageType_pVar])) {
                        self::$firePHPCoreGroups_pVar[$useMessageType_pVar] = array();
                    }
                    self::$firePHPCoreGroups_pVar[$useMessageType_pVar][] = array($messageType_pVar, $message_pVar, $time_pVar);
                }
            }
            else {
                if(in_array($useMessageType_pVar, self::$firePHPCoreGroupTags_pVar)) {
                    if(!isset(self::$firePHPCoreGroups_pVar[$useMessageType_pVar])) {
                        self::$firePHPCoreGroups_pVar[$useMessageType_pVar] = array();
                    }
                    self::$firePHPCoreGroups_pVar[$useMessageType_pVar][] = array($messageType_pVar, $message_pVar, $time_pVar);
                }
                else {
                    if($messageType_pVar === 'LOG_GROUP') {
                        self::firePHPstartGroup_gFunc($message_pVar);
                    }
                    elseif($messageType_pVar === 'LOG_GROUP_END') {
                        self::firePHPendGroup_gFunc();
                    }
                    else {

                        if(isset($_SERVER['HTTP_USER_AGENT'])) {
                            $oldUserAgent_pVar = $_SERVER['HTTP_USER_AGENT'];
                        }
                        if(!empty(self::$firePHPCoreUserAgent_pVar)) {
                            $_SERVER['HTTP_USER_AGENT'] = self::$firePHPCoreUserAgent_pVar;
                        }

                        self::__firePHP_gFunc($messageType_pVar, $message_pVar, $time_pVar);

                        if(isset($oldUserAgent_pVar)) {
                            $_SERVER['HTTP_USER_AGENT'] = $oldUserAgent_pVar;
                        }
                        else {
                            unset($_SERVER['HTTP_USER_AGENT']);
                        }
                    }
                }
            }
        }


    }

    static public function __firePHP_gFunc($messageType_pVar, $message_pVar, $time_pVar)
    {
        $firephp_pVar = FirePHP::getInstance(true);
        if($messageType_pVar === 'Notice'
            || $messageType_pVar === 'Warning'
            ||$messageType_pVar === 'Error'
            ||$messageType_pVar === 'Fatal') {
            $firephp_pVar->error('(' . $time_pVar . ') ' . $messageType_pVar . ' ' . $message_pVar);
        }
        else {
            $firephp_pVar->log('(' . $time_pVar . ') ' . $messageType_pVar . ' ' . $message_pVar);
        }
    }

    static public function firePHPFlushGroups_gFunc()
    {
        if(self::$firePHPCoreInitialized_pVar === 0) {
            return;
        }
        self::$firePHPCoreInitialized_pVar = 0;
        if(isset($_SERVER['HTTP_USER_AGENT'])) {
            $oldUserAgent_pVar = $_SERVER['HTTP_USER_AGENT'];
        }
        if(!empty(self::$firePHPCoreUserAgent_pVar)) {
            $_SERVER['HTTP_USER_AGENT'] = self::$firePHPCoreUserAgent_pVar;
        }

        $firephp_pVar = FirePHP::getInstance(true);

        foreach(self::$firePHPCoreGroups_pVar as $groupName_pVar => $groupData_pVar) {
            $firephp_pVar->group($groupName_pVar);
            foreach($groupData_pVar as $v_pVar) {
                $firephp_pVar->log('(' . $v_pVar[2] . ') ' . $v_pVar[0] . ' ' . $v_pVar[1]);
            }
            $firephp_pVar->groupEnd();
        }
        if(isset($oldUserAgent_pVar)) {
            $_SERVER['HTTP_USER_AGENT'] = $oldUserAgent_pVar;
        }
        else {
            unset($_SERVER['HTTP_USER_AGENT']);
        }
    }

    static public function flush()
    {
        if(!empty(self::$fileHandler_pVar)) {
            if(!empty(self::$buffer_pVar)) {
                //fputs(self::$fileHandler_pVar, self::$buffer_pVar);
                self::$buffer_pVar = '';
            }
            fflush(self::$fileHandler_pVar);
        }
    }

    static public function getFileName_pVar()
    {
        if(self::$fileName_pVar === false)
        {
            $fileName_pVar= main_gClass::getConfigVar_gFunc('documents_dir', 'main');
            if(empty($fileName_pVar)) {
                return(false);
            }
            else {
                $fileName_pVar .= '.log/';
                $fatalLog_pVar = $fileName_pVar . 'errors.log';

                $logDirFormat_pVar = main_gClass::getConfigVar_gFunc('log_dir_format', 'main');

                $timeSuffix_pVar = sprintf('%0.023f', debug_gClass::getMicrotime_gFunc());

                if(!empty($logDirFormat_pVar)) {
                    $dirName_pVar = $fileName_pVar . date($logDirFormat_pVar);
                    if(!file_exists($dirName_pVar)) {
                        if (!@mkdir($concurrentDirectory = $dirName_pVar . '/', 0777, true) && !is_dir($concurrentDirectory)) {
                            throw new \RuntimeException(sprintf('Directory "%s" was not created', $concurrentDirectory));
                        }
                    }
                    $fileName_pVar = $dirName_pVar . '/' . $timeSuffix_pVar;
                }
                else {
                    $fileName_pVar .= $timeSuffix_pVar;
                }
            }
            $fileName_pVar .= '.log';
            self::$fileName_pVar = $fileName_pVar;

            ini_set('display_errors','on');
            ini_set('log_errors','on');
            ini_set('error_log', $fatalLog_pVar);
        }
        return(self::$fileName_pVar);
    }

    static public function writeQuery_gFunc($query_string_pVar, $duration_pVar, $fileName_pVar, $lineNum_pVar)
    {
        if(self::$log_queries_pVar <= $duration_pVar) {
            self::write_gFunc('DB_QUERY-' . $fileName_pVar . ':'. $lineNum_pVar, '(' . $duration_pVar . ') ' . $query_string_pVar);
        }
        else {
            self::$queries_count_pVar++;
            self::$queries_bytes_pVar += strlen($query_string_pVar);
            self::$queries_time_pVar += $duration_pVar;
        }
    }

    static public function writeBaseQuery_gFunc($query_string_pVar, $query_params_pVar, $duration_pVar, $fileName_pVar, $lineNum_pVar)
    {
        if(main_gClass::isLogEnabled_gFunc('DB_BQUERY')) {
            $md5_pVar = md5($query_string_pVar);
            if(!isset(self::$bqueries_cache_pVar[$md5_pVar])) {
                self::$bqueries_cache_pVar[$md5_pVar] = array('queries_count_pVar'=>0, 'queries_time_pVar'=>0);
            }
            self::$bqueries_cache_pVar[$md5_pVar]['query_string_pVar'] = $query_string_pVar;
            self::$bqueries_cache_pVar[$md5_pVar]['location_pVar'] = $fileName_pVar . ':' . $lineNum_pVar;
            self::$bqueries_cache_pVar[$md5_pVar]['queries_count_pVar']++;
            self::$bqueries_cache_pVar[$md5_pVar]['queries_time_pVar'] += $duration_pVar;
            self::$bqueries_cache_pVar[$md5_pVar]['query_params_pVar'] = $query_params_pVar;
        }
    }
}
