<?php


/**
 * stack na lokalne premenne sablony
 */
class varStack_gClass {
    private static $stacks_pVar = array();
    private static $lastStackId_pVar = -1;
    static public $vars = array();
    static public $globals = array();
    static private $globals_pVar = array();

    private function __construct()
    {

    }

    static function createStack_gFunc()
    {
        self::$lastStackId_pVar++;
        self::$stacks_pVar[self::$lastStackId_pVar] = self::$vars;
        self::$vars = array();
        foreach (self::$globals_pVar as $globalName_pVar=>$global_pVar) {
            if(!$global_pVar) {
                continue;
            }
            if(isset(self::$stacks_pVar[self::$lastStackId_pVar][$globalName_pVar])) {
                self::$vars[$globalName_pVar] = self::$stacks_pVar[self::$lastStackId_pVar][$globalName_pVar];
            }
        }
    }

    static function destroyStack_gFunc()
    {
        if(self::$lastStackId_pVar < 0) {
            self::$vars = array();
            return;
        }
        foreach (self::$globals_pVar as $globalName_pVar=>$global_pVar) {
            if(!$global_pVar) {
                continue;
            }
            if(isset(self::$vars[$globalName_pVar])) {
                self::$stacks_pVar[self::$lastStackId_pVar][$globalName_pVar] = self::$vars[$globalName_pVar];
            }
        }
        self::$vars = self::$stacks_pVar[self::$lastStackId_pVar];
        unset(self::$stacks_pVar[self::$lastStackId_pVar]);
        self::$lastStackId_pVar--;
    }

    static function getVarExpression_gFunc($varName_pVar)
    {
        if(substr($varName_pVar, 0, 8) == 'session:') {
            $varName_pVar = substr($varName_pVar, 8);
            return('session_gClass::getUserDetail_gFunc(\''.$varName_pVar.'\')');
        }
        if(substr($varName_pVar, 0, 9) == 'settings:') {
            $varName_pVar = substr($varName_pVar, 9);
            return('session_gClass::getSessionSettingsDetail_gFunc(\''.$varName_pVar.'\')');
        }
        if(substr($varName_pVar, 0, 11) == 'sessionvar:') {
            $varName_pVar = substr($varName_pVar, 11);
            return('main_gClass::getSessionData_gFunc(\''.$varName_pVar.'\')');
        }
        if(substr($varName_pVar, 0, 8) == 'request:') {
            $varName_pVar = substr($varName_pVar, 8);
            return('main_gClass::getInputString_gFunc(\''.$varName_pVar.'\', main_gClass::SRC_REQUEST_pVar)');
        }
        if(substr($varName_pVar, 0, 7) == 'config:') {
            $varName_pVar = substr($varName_pVar, 7);
            $varName_pVar = explode(':', $varName_pVar);
            return('main_gClass::getConfigVar_gFunc(\'' . $varName_pVar[1] . '\', \'' . $varName_pVar[0] . '\')');
        }
        if($varName_pVar == 'lng') {
            return('main_gClass::getLanguage_gFunc()');
        }

        $varsString_pVar = str_replace(':', '\'][\'' , $varName_pVar);
        return('vars::$vars[\'' . $varsString_pVar . '\']');

    }

    static function registerGlobal_gFunc($varName_pVar)
    {
        self::$globals_pVar[$varName_pVar] = true;
        $isSet_pVar = false;
        $value_pVar = false;
        foreach (self::$stacks_pVar as $k_pVar=>$stack_pVar) {
            if(isset($stack_pVar[$varName_pVar])) {
                $isSet_pVar = true;
                $value_pVar = $stack_pVar[$varName_pVar];
            }
            if($isSet_pVar) {
                self::$stacks_pVar[$k_pVar][$varName_pVar] = $value_pVar;
            }
        }
    }
}
