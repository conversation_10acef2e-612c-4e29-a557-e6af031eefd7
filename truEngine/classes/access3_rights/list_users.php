<?php

class list_users_gClass extends source_gClass
{

    protected function getData()
    {

        if(isset($this->params['cmd']) && !empty($this->params['cmd'])) {
            if(session_gClass::userHasRightsAccessAction_gFunc(s_system_superadmin)) {
                if($this->params['cmd'] == 'delete_old') {
                    $sql_pVar = 'UPDATE `%titems_users__data` set status = \'deleted\' WHERE status=\'request\' AND insert_time < date_sub(now(), interval 7 day)';
                    db_public_gClass::execute_gFunc($sql_pVar, __FILE__, __LINE__);
                }
                if($this->params['cmd'] == 'delete_all') {
                    $sql_pVar = 'UPDATE `%titems_users__data` set status = \'deleted\' WHERE status=\'request\'';
                    db_public_gClass::execute_gFunc($sql_pVar, __FILE__, __LINE__);
                }
            }
        }
        unset($this->params['cmd']);

        $filter_pVar = $this->params;
        if(!isset($filter_pVar['filter']) || strpos($filter_pVar['filter'], 'status=request') === false) {
            $filter_pVar[] = array('__operator'=>'<>', 'status'=>'request');
        }
//        $GLOBALS['havala']=1;
        $users_pVar = items_gClass::getItems_gFunc('users', $filter_pVar);
//        dd($users_pVar[22]);
        unset($users_pVar['filter']);
        $this->formatData_gFunc($users_pVar);
        return($users_pVar);
    }

    protected function formatData_gFunc(&$data_pVar)
    {
        if(modules_gClass::isModuleRegistred_gFunc('access4_groups')) {
            $groups_pVar = db_access4_groups_gClass::getGroups_gFunc('group_id');
        }
        foreach ($data_pVar as $k_pVar=>$v_pVar) {
            if(((string)$k_pVar)[0] === '_') {
                continue;
            }
            if(array_search('online', $data_pVar[$k_pVar]['online.session_status']) !== false) {
                $data_pVar[$k_pVar]['session_status_text'] = string_gClass::get(str__session_status_online_sVar);
            }
            else {
                $data_pVar[$k_pVar]['session_status_text'] = string_gClass::get(str__session_status_offline_sVar);
            }
            if(isset($v_pVar['user_groups']) && modules_gClass::isModuleRegistred_gFunc('access4_groups')) {
                if(!empty($v_pVar['user_groups'])) {
                    $data_pVar[$k_pVar]['user_groups_label'] = array();
                    $tmp_pVar = explode(',', $v_pVar['user_groups']);
                    foreach($tmp_pVar as $vv_pVar) {
                        if(!empty($vv_pVar) && isset($groups_pVar[$vv_pVar])) {
                            $data_pVar[$k_pVar]['user_groups_label'][] = $groups_pVar[$vv_pVar]['group_name'];
                        }
                    }
                    $data_pVar[$k_pVar]['user_groups_label'] = implode(', ', $data_pVar[$k_pVar]['user_groups_label']);
                }
                else {
                    $data_pVar[$k_pVar]['user_groups_label'] = '';
                }
            }
        }
    }
}

class list_users extends list_users_gClass {}
