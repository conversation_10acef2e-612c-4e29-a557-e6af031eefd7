@props([
    'type' => 'text',
    'name' => null,
    'id' => null,
    'label' => null,
    'hint' => null,
    'class' => 'mb-3',
    'value' => null,
    'icon' => null,
    'options' => null,

    'suggest' => null,

    'labelDescription' => null,
    'inputGroupBefore' => null,
    'inputGroupAfter' => null,
])

@php
    $name = $name??$type;
    $id = $id??$name;
    $isHorizontalForm = \Mixedtype\Components\ComponentIdManager::getComponent()->flagsArray['horizontal'] ?? false;


    if(!$getForm()->isRequest1stRender()) {
        $value = $getForm()->getFieldValue($name);
    }
    else {
        $value = $getForm()->getFieldValue($name, $value);
    }


    if($type === 'checkbox') {
        $value = (bool)$value;
    }

    // use attribute (etc. label-description) as slot
    $varname = 'label-description';
    if($labelDescription === null && isset($$varname)) {
        $labelDescription = $$varname;
    }
    $varname = 'input-group-after';
    if($inputGroupAfter === null && isset($$varname)) {
        $inputGroupAfter = $$varname;
    }

    if($inputGroupAfter !== null && !is_array($inputGroupAfter)) {
        $inputGroupAfter = [$inputGroupAfter];
    }

//    dd(get_defined_vars());

    $inputWrapperClasses = [];
    if($inputGroupBefore !== null || $inputGroupAfter !== null) {
        $inputWrapperClasses[] = 'input-group';
        $inputWrapperClasses[] = 'input-group-flat';
    }

@endphp

<script>
    if(window.mixedtype === undefined) {
        window.mixedtype = { components: {} };
    }
    if(window.mixedtype.components['{{ \Mixedtype\Components\ComponentIdManager::getComponentId() }}'] === undefined) {
        window.mixedtype.components['{{ \Mixedtype\Components\ComponentIdManager::getComponentId() }}'] = {};
    }
    if(window.mixedtype.components['{{ \Mixedtype\Components\ComponentIdManager::getComponentId() }}'].fields === undefined) {
        window.mixedtype.components['{{ \Mixedtype\Components\ComponentIdManager::getComponentId() }}'].fields = {};
    }

    window.mixedtype.components['{{ \Mixedtype\Components\ComponentIdManager::getComponentId() }}'].fields['{{ $name }}'] = {
        label: @json((string)$label),
        type: @json($type),
        hint: @json($hint),
        value: @json($value),
        suggest: @json(['data' => []]),
        options: @json($options),
        config: @json($configArray),
        init: function() { }
    };


</script>

@if(in_array($type, ['text', 'email', 'password', 'number', 'select', 'tags', 'file']))
        @include('mixedtype-formsnew::components.fields-includes.field')
    @else
        @include('mixedtype-formsnew::components.fields-includes.' . $type)
@endif

