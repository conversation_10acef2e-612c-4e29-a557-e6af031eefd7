<?php


class session_request_accept_gClass extends form_gClass
{
    protected function initForm_gFunc($multiedit_pVar = false, $initFormRef_pVar = true)
    {
        $this->addHiddenField_gFunc('user_id', 0, '/[0-9/+', intval($this->params['item_id']));

        $user_id_pVar = $this->getFieldValue_gFunc('user_id');

        $this->setVar_gFunc('user_id', $user_id_pVar, false);

        $user_pVar = items_gClass::getItem_gFunc('users', intval($user_id_pVar));
        $this->setVar_gFunc('user', $user_pVar);

        if(isset($this->params['submit_button_title'])) {
            $this->setVar_gFunc('submit_button_title', $this->params['submit_button_title'], false);
        }
    }

    protected function getData()
    {
        $formData_pVar = $this->getFormData_gFunc();
        if($formData_pVar['error_code'] !== self::RESULT_OK_pVar) {
            return($formData_pVar);
        }

        $user_id_pVar = (int)$this->getVar_gFunc('user_id', false);
        $user_pVar = items_gClass::getItem_gFunc('users', $user_id_pVar);

        if(!is_array($user_pVar)
            || $user_pVar['status'] != 'request'
            || ($user_pVar['email_check_status'] != 'checked' && $user_pVar['email_check_status'] != 'protected')
            || (isset($user_pVar['isic']) && $user_pVar['isic_ok'] != 'yes')
        ) {
            $this->setVar_gFunc('result', 'err', false);
        }
        else {
            $data_pVar = array('status'=>'active', 'item_id'=>$user_id_pVar);
            session_gClass::giveMeFullAccess_gFunc();
            items_gClass::saveOrUpdateItem_gFunc('users', $data_pVar);
            session_gClass::revokeMeFullAccess_gFunc();

            // ulozim do DB
            $data_pVar = array(
                'user_id'=>$user_id_pVar,
                'request_time'=>date('Y-m-d H:i:s'),
                'request_type'=>'accept',
                'admin_user_id'=>session_gClass::getUserDetail_gFunc('user_id')
            );
            db_public_gClass::insertData_gFunc('%tusers_requestlog', '%d,%s,%s,%d', $data_pVar, __FILE__, __LINE__);

            // poslem notifikaciu
            $subject_pVar = 'Registrácia schválená';
            $template_pVar = '/.emails/registration_accepted';
            $template_pVar = main_gClass::addLngPrefixToDocName_gFunc($template_pVar);

            $vars_pVar = array('mail'=>array());
            $vars_pVar['mail']['user'] = $user_pVar;
            $vars_pVar['mail']['url'] = main_gClass::getServerVar_gFunc('SERVER_NAME') . main_gClass::getConfigVar_gFunc('web_dir_gVar', 'runtime_pVar');

            $templateContent_pVar = callStack_gClass::getDocContent_gFunc($template_pVar, $vars_pVar);
            email_gClass::mailHtml_gFunc($user_pVar['email'], main_gClass::getConfigVar_gFunc('email', 'contacts'), $subject_pVar, $templateContent_pVar);

            $this->setVar_gFunc('result', 'ok', false);
        }

        return(parent::getData());
    }
}

class session_request_accept extends session_request_accept_gClass {}
