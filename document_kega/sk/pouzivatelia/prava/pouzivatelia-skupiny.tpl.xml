<?xml version="1.0" encoding="utf-8" ?>
<truEngine-document xmlns:te="http://www.truengine.sk" te:access="s_system_show_group_users">

<te:source name="groups" varname="groups" />
<?php @{title} = @{groups}[@{request:skupina}]['group_name']; ?>
<te:content-set name="title" value="Zoznam používateľov skupiny @{title}" />

<te:source name="group_add_users" users_add="@{request:users_add}" group_id="@{request:skupina}" />

<p te:access="s_system_add_group_user" te:if="@{groups}[@{request:skupina}]['group_type_id']==1">
	<a href="#" onclick="$('add_user_to_group').style.display='block'; return(false);"><te:string id="Pridať používateľov do skupiny @{title}" /></a>
	<div id="add_user_to_group" style="display:none;">
		<form action="/pouzivatelia/prava/pouzivatelia-skupiny&amp;skupina=@{request:skupina}" method="post" onsubmit="prepareSelectorPouzivatelia();">
			<te:include name="/sk/components/item-selector" type="users" order_by="last_name" label="nick" filter_settings="status=active&amp;last_name=LIKE(a%)" />
			<div style="text-align:right">
				<input type="hidden" id="users_add" name="users_add" value="a" />
				<input type="submit" value="@L{Pridať používateľov}" />
			</div>
		</form>
	</div>
</p>

<te:include name="/sk/components/filter" filter="@@{filter}" item_type="users" doc="/@{doc}" read_only="true" filter_settings="user_groups=LIKE(%,@{request:skupina},%)" />

<te:source name="list_users" varname="users" filter="@{filter:settings}" pager="20,@{request:page}" _order_by="@{filter:sort}" />

<te:include name="/sk/components/pager" pager="@{pager}" doc="@{doc}" params="@{filter:params}" extra_params="&amp;skupina=@{request:skupina}" _order_by="@{filter:sort}" order_fields="@{users:_order_by_fields}" />

<te:include name="/sk/components/userlist" users="@@{users}" />

<te:include name="/sk/components/pager" pager="@{pager}" doc="@{doc}" params="@{filter:params}" extra_params="&amp;skupina=@{request:skupina}" _order_by="@{filter:sort}" order_fields="@{users:_order_by_fields}" />

<script type="text/javascript">
function prepareSelectorPouzivatelia()
{
	var sel = $('result_item_selector');
	selectAllOptions(sel);
	for(var i=0; i &lt; sel.options.length; i++) {
		$('users_add').value += ',' + sel.options[i].value;
	}
}
</script>

<te:content-set name="pdf_icon" value="@{REQUEST_URI}" />
</truEngine-document>
