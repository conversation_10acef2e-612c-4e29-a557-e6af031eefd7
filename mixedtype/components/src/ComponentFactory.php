<?php

namespace Mixedtype\Components;

class ComponentFactory
{
    private static array $componentsParamsForResolve = [];
    private static array $componentsAttribs = [];

    static public function createComponentFromUniqueId($uniqueComponentId)
    {
        $componentParamsForResolve = self::restoreComponentParamsForResolve($uniqueComponentId);
        if(empty($componentParamsForResolve)) {
            return null;
        }
        if($componentParamsForResolve['_component_id'] !== $uniqueComponentId) {
            return null;
        }

        $specialParams = [];
        $specialParams['_component_id'] = $uniqueComponentId;
        $specialParams['_component_class'] = $componentParamsForResolve['_component_class'];


        $componentClass = $componentParamsForResolve['_component_class'];
        unset($componentParamsForResolve['_component_class']);
        unset($componentParamsForResolve['_component_id']);

        $component = $componentClass::resolve($componentParamsForResolve);
        if($component) {
            $component->setComponentId($uniqueComponentId);
            $component->withAttributes(self::restoreComponentAttribs($uniqueComponentId));
        }
        return $component;
    }

    public static function storeComponentParamsForResolve($componentId, $addParams = []) : void
    {
        $componentKey = 'component_params_for_resolve_' . $componentId;

        self::$componentsParamsForResolve[$componentKey] = [
            ...self::$componentsParamsForResolve[$componentKey] ?? [],
            ...$addParams,
        ];

        session()->put($componentKey, self::$componentsParamsForResolve[$componentKey]);
    }
    public static function storeComponentAttribs($componentId, $addAttribs = []) : void
    {
        $componentKey = 'component_attribs_' . $componentId;

        self::$componentsAttribs[$componentKey] = [
            ...self::$componentsAttribs[$componentKey] ?? [],
            ...$addAttribs,
        ];

        session()->put($componentKey, self::$componentsAttribs[$componentKey]);
    }

    public static function restoreComponentParamsForResolve($componentId = null) : array
    {
        if($componentId === null) {
            return [];
        }

        $componentKey = 'component_params_for_resolve_' . $componentId;
        if(!isset(self::$componentsParamsForResolve[$componentKey])) {
            $data = session($componentKey, null);
            if(is_array($data)) {
                self::$componentsParamsForResolve[$componentKey] = $data;
            }
        }

        return self::$componentsParamsForResolve[$componentKey] ?? [];
    }

    public static function restoreComponentAttribs($componentId = null) : array
    {
        if($componentId === null) {
            return [];
        }

        $componentKey = 'component_attribs_' . $componentId;
        if(!isset(self::$componentsAttribs[$componentKey])) {
            $data = session($componentKey, null);
            if(is_array($data)) {
                self::$componentsAttribs[$componentKey] = $data;
            }
        }

        return self::$componentsAttribs[$componentKey] ?? [];
    }

    static public function routeAction($component_id, string $action = 'state')
    {
        $component = \Mixedtype\Components\ComponentFactory::createComponentFromUniqueId($component_id);
        if($component) {
            ComponentIdManager::pushComponentAndGetId($component, null, $component_id);

            // todo: spravit kontrolu na polovelne akcie ktore sa daju vykonat requestom (nemusim ju volat priamo ale cez nejaku metodu ktora to overi).
            $callAction = $action;
            if($action === 'render') {
                $callAction = 'html';
            }

            $result = $component->$callAction();
            ComponentIdManager::popComponent();

            // redirect returnem iba ak nie je ajaxovy request
            if(request()->ajax() === false) {
                if($result instanceof \Illuminate\Http\RedirectResponse) {
                    return $result;
                }
            }

            $jsonData = [
                'componentId' => $component_id,
                'action' => $action,
                'result' => $result,
            ];

            if($result instanceof \Illuminate\Http\RedirectResponse) {
                $jsonData['redirect'] = $result->getTargetUrl();
            }

            return response()->json($jsonData);
        }

        return response()->json(['error' => 'Component not found'], 500);
    }
}
