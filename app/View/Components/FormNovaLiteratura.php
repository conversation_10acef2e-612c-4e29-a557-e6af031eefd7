<?php

namespace App\View\Components;

use Closure;
use \Illuminate\Contracts\View\View;
use Illuminate\Support\Facades\DB;
use Mixedtype\FormsNew\FormComponent\Form;
use Mixedtype\Multisite\Multisite;


class FormNovaLiteratura extends Form
{
    function getDefaultData():array
    {
        $data = [
            'literatura_type' => 'kniha',
        ];

        $md5 = request()->input('book', '');
        if(!empty($md5)) {
            $data = (array)DB::table('kega_items_literatura__data')
                ->where('qmd5', $md5)
                ->first();
            $data['md5'] = $data['qmd5'];
        }

        $item_id = request()->input('item_id', '');
        if(!empty($item_id) && is_numeric($item_id)) {
            $data = (array)DB::table('kega_items_literatura__data')
                ->where('item_id', $item_id)
                ->first();
            $data['md5'] = $data['qmd5'];
        }

        if(isset($data['keywords'])) {
            $data['keywords'] = array_map('trim', explode(',', $data['keywords']));
        }

        return [
            ...$data,
        ];
    }

    function getRules():array
    {
        return [
            'item_id' => 'nullable|numeric',
            'md5' => 'nullable|string',

            'name' => 'required',
            'type' => 'required',
            'autor' => 'required',
            'vydavatelstvo' => 'required',
            'kapitola' => 'required',
            'link' => 'required',
            'year' => 'required|integer|min:1900|max:2099',
            'info' => 'required',
            'keywords' => '',
            'image' => '',

            'update_book' => 'nullable|numeric',
            'update_book_locked' => 'nullable|numeric',
            'update_chapter' => 'nullable|numeric',
            'update_chapter_locked' => 'nullable|numeric',
        ];
    }

    function suggest()
    {
        $field = request()->input('field');
        $query = request()->input('query');

        if($field === 'name') {
            $rows = DB::table('kega_items_literatura__data')
                ->where('search_name', 'like', '%' . $query . '%')
                ->select([...$this->getBaseFields(), 'qmd5 as md5', DB::raw('CONCAT_WS(", ", name, autor, vydavatelstvo, year, info) as value')])
                ->distinct()
                ->orderBy('name')
                ->limit(15)
                ->get();
            return [
                $field => $rows->toArray(),
            ];
        }
        if($field === 'kapitola' && request()->input('md5')) {
            $rows = DB::table('kega_items_literatura__data')
                ->where('qmd5', request()->input('md5'))
                ->where('kapitola', 'like', '%' . $query . '%')
                ->select([...$this->getExtendedFields(), 'item_id', DB::raw('CONCAT_WS(", ", kapitola, link) as value')])
                ->distinct()
                ->orderBy('kapitola')
                ->limit(15)
                ->get();
            foreach($rows as $row) {
                $row->keywords = array_map('trim', explode(',', $row->keywords));
            }
            return [
                $field => $rows->toArray(),
            ];
        }

        return [$field => []];
    }

    function suggestKeywords()
    {
        $query = request()->input('query');
        $rows = DB::table('kega_items_literatura__data')
            ->distinct()
            ->where('keywords', 'like', '%' . $query . '%')
            ->orderBy('keywords')
            ->limit(10)
            ->get();
        $keywords = [];
        foreach ($rows as $row) {
            $rowKeywords = array_map('trim', explode(',', $row->keywords));
            foreach ($rowKeywords as $keyword) {
                if (!empty($keyword) && str_contains(strtolower($keyword), strtolower($query))) {
                    $keywords[] = $keyword;
                }
            }
        }
        $keywords = array_unique($keywords);
        sort($keywords);
        $keywords = array_map(function($keyword) {
            return [
                'value' => $keyword,
            ];
        }, $keywords);
        return [
            'items' => $keywords,
        ];
    }

    private function getBaseFields()
    {
        return [
            'name',
            'type',
            'autor',
            'vydavatelstvo',
            'year',
            'info',
            'image',
        ];
    }

    private function getExtendedFields()
    {
        return [
            'kapitola',
            'link',
            'keywords',
        ];
    }

    function processSubmit(array $validated)
    {
        // decorate validated data
        $validated['name'] = trim($validated['name']);
        if(!isset($validated['keywords'])) {
            $validated['keywords'] = [];
        }
        $validated['keywords'] = implode(', ', array_map('trim', $validated['keywords']));

        // set bookKey and chapterKey
        if(!empty($validated['item_id']) && (int)$validated['update_chapter']) {
            $chapterKey = (int)$validated['item_id'];
            $message = 'Kapitola bola upravená.';
        }
        else {
            $chapterKey = 0;
            $message = 'Nová kapitola bola vložená.';
        }
        if(!empty($validated['md5']) && (int)$validated['update_book']) {
            $bookKey = $validated['md5'];
        }
        else {
            $bookKey = uniqid(true);
            $chapterKey = 0; // if new book, then must be new chapter
            $message = 'Nová literatúra bola vložená.';
        }

        $baseFields = $this->getBaseFields();
        $extendedFields = $this->getExtendedFields();;

        // prepare data
        $data = [];
        foreach([...$baseFields, ...$extendedFields] as $field) {
            if(!empty($validated[$field])) {
                $data[$field] = $validated[$field];
            }
        }
        $data['item_id'] = $chapterKey;
        $data['qmd5'] = $bookKey;
        $data['insert_time'] = date('Y-m-d H:i:s');

        // todo: nastavit obrazok z uploadnutych suborov. - mal by som skopirovat? + nastavit field do $data['image'].

        if($chapterKey) { // update chapter
            DB::table('kega_items_literatura__data')
                ->where('item_id', $chapterKey)
                ->update($data);
        }
        else { // insert new chapter to existing or new book (depends on bookKey)
            DB::table('kega_items_literatura__data')
                ->insert($data);
        }

        // fix book fields
        foreach ($data as $fieldName => $fieldValue) {
            if(!in_array($fieldName, $baseFields)) {
                unset($data[$fieldName]);
            }
        }
        DB::table('kega_items_literatura__data')
            ->where('qmd5', $bookKey)
            ->update($data);

        // fix search_name for all book chapters
        DB::table('kega_items_literatura__data')
            ->where('qmd5', $bookKey)
            ->update([
                'search_name' => DB::raw('CONCAT_WS(", ", name, autor, vydavatelstvo, year, info, kapitola, link, keywords)'),
            ]);

        return redirect()
            ->route(Multisite::getFullRouteName('literatura.zoznam'))
            ->with('page-message', $message);
    }

}
