<?php


class list_onlineusers_gClass extends source_gClass
{

    protected function getData()
    {
        $users_pVar = db_session_gClass::getOnlineusers_gFunc($this->params);
        $this->formatData_gFunc($users_pVar);
        return($users_pVar);
    }

    protected function formatData_gFunc(&$data_pVar)
    {
        foreach ($data_pVar as $k_pVar=>$v_pVar) {
            if(substr($k_pVar, 0, 1) === '_') {
                continue;
            }
            $data_pVar[$k_pVar]['onlinelist'] = true;

            if(isset($v_pVar['session_status'])) {
                $data_pVar[$k_pVar]['online.session_status'] = array($v_pVar['session_status']);
            }

            switch ($data_pVar[$k_pVar]['online.session_status'][0]) {
                case 'online':
                    $data_pVar[$k_pVar]['session_status_text'] = string_gClass::get(str__session_status_online_sVar);
                    break;
                case 'timeout':
                    $data_pVar[$k_pVar]['session_status_text'] = string_gClass::get(str__session_status_offline_sVar);
                    break;
                case 'timeouted':
                    $data_pVar[$k_pVar]['session_status_text'] = string_gClass::get(str__session_status_timeout_sVar);
                    break;
                case 'restoring':
                    $data_pVar[$k_pVar]['session_status_text'] = string_gClass::get(str__session_status_offline_sVar);
                    break;
                default:
                    $data_pVar[$k_pVar]['session_status_text'] = string_gClass::get(str__session_status_offline_sVar);
                    break;
            }
        }
    }
}

class list_onlineusers extends list_onlineusers_gClass {}
