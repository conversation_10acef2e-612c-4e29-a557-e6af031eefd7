class AlpineFormComponent extends AlpineComponent {
    constructor(componentId) {
        super(componentId);
    }

    getFullUrl(action)
    {
        return this.url.replace('formHandler', action)
    }

    suggestTomInit(fieldName) {
        if (!window.TomSelect) {
            console.error('TomSelect is not available on window object');
            return;
        }

    // @php
    //     $opt = [
    //         [ 'value' => 'test1', 'text' => 'test1' ],
    //     [ 'value' => 'random', 'text' => 'random' ],
    //
    // ];
    //     @endphp

//        const options = @json($opt);

        const options = [];
        this.fields[fieldName].tomSelect = new TomSelect("#" + fieldName, {
            options: options,
            create: true
        });
    }

    suggestRawInit(fieldName) {
        // do nothing
    }
    suggestRaw(fieldName) {
        const component = this;
        axios.get(this.getFullUrl('suggest'), {
            params: {
                field: fieldName,
                query: this.fields[fieldName].value
            }
        }).then(function (response) {
            component.fields[fieldName].suggest.data = response.data.result[fieldName];

            if(component.fields[fieldName].suggest.data.length === 0) {
                component.fields[fieldName].suggestVisible = false
                return
            }
            component.fields[fieldName].suggestVisible = true
        })
    }
    suggestRawSelect(fieldName, $value) {
        this.fields[fieldName].value = $value
        this.fields[fieldName].suggestVisible = false
    }
    suggestRawClickedOutside(fieldName, event) {
        if (!event.target.closest('.dropdown')) {
            this.fields[fieldName].suggestVisible = false
        }
    }

    /**
     * Initializes a tag input field with the specified configuration, using TomSelect library.
     * Allows for local or remote options, with a minimum query length for remote data fetching.
     *
     * @param {string} fieldName - The ID of the input field to be initialized.
     * @param {Array} options - An array of objects representing the local options for the tag input. Each object should have a "value" property.
     * @param {boolean} [remote=false] - Determines whether to fetch options remotely. If true, a remote call will be made when the query length exceeds 2.
     * @return {void} This method does not return a value.
     */
    tagsInit(fieldName, options, remote = false, defaultValues = []) {
        const component = this;

        if (!Array.isArray(defaultValues)) {
            defaultValues = [];
        }

        defaultValues.forEach(function(value) {
            if (!options.some(option => option.value === value)) {
                options.push({ value: value, text: value });
            }
        });

        new TomSelect("#" + fieldName, {
            create: true,
            closeAfterSelect: true,
            valueField: 'value',
            labelField: 'value',
            searchField: 'value',
            options: options,
            items: defaultValues,
            shouldLoad : function(query) {
                if(!remote) return false;
                return query.length > 2;
            },
            load: function(query, callback) {
                const capitalizedFieldName = fieldName.charAt(0).toUpperCase() + fieldName.slice(1);
                var url = component.getFullUrl('suggest' + capitalizedFieldName) + '?query=' + encodeURIComponent(query);

                fetch(url)
                    .then(response => response.json())
                    .then(json => {
                        callback(json.result.items);
                    }).catch(()=>{
                    callback();
                });
            },
        });
    }
}

export default AlpineFormComponent;
window.AlpineFormComponent = AlpineFormComponent;

