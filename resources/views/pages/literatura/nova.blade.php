<x-layout>


- editovanie literatury
        - [ ] linka zo zoznamu na md5 a linka na item_id (kapitola). Rovnaky formular ako v<PERSON>ci, len s prednastavenymi ideckami.
- editovanie otazky
        - [ ] prerobim formular na nove formulare
        - [ ] pridam stlpec KAPITOLA
        - [ ] integrujem formular na editaciu/vkladanie knihy ------- skusim to ako child komponentu? to by mi usetrilo pracu? Bude to formular vo formulari. Ak budem submitovat cez ajax podla struktury fieldov v modeli, tak si to mozem dovolit... na child komponente nezobrazim submit. Z parent komponenty submitnem najskor child (vratane validacie), ak je vsetko ok, tak mi bude vratene id kapitoly. A nasledne mozem submitnut parent formular a cele to ulozit.
            - [ ] child formular budem submitovat a validovat iba v pripade, ze som na nom nieco zmenil, alebo ak nema nastavene ID kapitoly (ktore potrebujem)
            - [ ] do formulara vlozim standardne field item_id
- zoznam
        - [ ] ozivit akcie a linky... v zozname
        - [ ] filtrovanie podla typu (facet)
        - [ ] filtrovanie podla textu (search_name) field. (facet)
        - [ ] zobrazit pocet otazok v zozname --- to tiez nejaky akoby facet, musim to indexovat. - pri kazdom zaradeni otazky + este cez cron (command) vsetko.

    <h1>Nová literatúra</h1>
    <x-form-nova-literatura flags="horizontal">
        <x-xfield type="hidden" name="md5" />

        <x-xfield type="hidden" name="update_book" value="1" />
        <x-xfield type="hidden" name="update_book_locked" />
        <x-xfield type="hidden" name="update_chapter" value="1" />
        <x-xfield type="hidden" name="update_chapter_locked" />

        <x-xfield type="text" name="name" label="Názov literatúry" suggest="raw"
                  config="{'suggestCallback': 'suggestNameCallback'}"
                  x-on:change="$store.{{ \Mixedtype\Components\ComponentIdManager::getComponentId() }}.onChangeBaseField('name')"
        />
        <x-xfield type="select" name="type" label="Typ literatúry"
                  x-on:change="$store.{{ \Mixedtype\Components\ComponentIdManager::getComponentId() }}.onChangeBaseField('type')"
                  :options="['kniha' => 'Kniha', 'zurnal' => 'Žurnál', 'prednaska' => 'Prednáška', 'ine' => 'Iné']" />
        <x-xfield type="text" name="autor" label="Autori"
                  x-on:change="$store.{{ \Mixedtype\Components\ComponentIdManager::getComponentId() }}.onChangeBaseField('autor')"
        />
        <x-xfield type="text" name="vydavatelstvo" label="Vydavateľstvo"
                  x-on:change="$store.{{ \Mixedtype\Components\ComponentIdManager::getComponentId() }}.onChangeBaseField('vydavatelstvo')"
        />
        <x-xfield type="text" name="year" label="Rok vydania"
                  x-on:change="$store.{{ \Mixedtype\Components\ComponentIdManager::getComponentId() }}.onChangeBaseField('year')"
        />
        <x-xfield type="text" name="info" label="ISBN"
                  x-on:change="$store.{{ \Mixedtype\Components\ComponentIdManager::getComponentId() }}.onChangeBaseField('info')"
        />
        <x-xfield type="file" name="image" label="Obrázok"
                  x-on:change="$store.{{ \Mixedtype\Components\ComponentIdManager::getComponentId() }}.onChangeBaseField('image')"
        />

        <div x-show="!$store.{{ \Mixedtype\Components\ComponentIdManager::getComponentId() }}.fields.update_book.value" class="alert alert-danger">
            Bude vložená nová literatúra. <a class="alert-action" href="#" x-on:click.prevent="$store.{{ \Mixedtype\Components\ComponentIdManager::getComponentId() }}.fields.update_book.value = 1; $store.{{ \Mixedtype\Components\ComponentIdManager::getComponentId() }}.updateSubmitButton()">Chcem aktualizovať existujúcu.</a>
        </div>

        <hr />

        <x-xfield type="text" name="kapitola" label="Kapitola" suggest="raw"
                  config="{'suggestCallback': 'suggestKapitolaCallback', 'suggestRequestParamsCallback': 'suggestKapitolaRequestParamsCallback'}"
                  x-on:change="$store.{{ \Mixedtype\Components\ComponentIdManager::getComponentId() }}.onChangeExtendedField('kapitola')"
        />

        <x-xfield type="text" name="link" label="Strany od - do"
                  x-on:change="$store.{{ \Mixedtype\Components\ComponentIdManager::getComponentId() }}.onChangeExtendedField('link')"
        />
        <x-xfield type="tags" name="keywords" label="Kľúčové slová"
                  x-on:change="$store.{{ \Mixedtype\Components\ComponentIdManager::getComponentId() }}.onChangeExtendedField('keywords')"
        />

        <div x-show="!$store.{{ \Mixedtype\Components\ComponentIdManager::getComponentId() }}.fields.update_chapter.value && $store.{{ \Mixedtype\Components\ComponentIdManager::getComponentId() }}.fields.update_book.value" class="alert alert-danger">
            Bude vložená nová kapitola. <a class="alert-action" href="#" x-on:click.prevent="$store.{{ \Mixedtype\Components\ComponentIdManager::getComponentId() }}.fields.update_chapter.value = 1; $store.{{ \Mixedtype\Components\ComponentIdManager::getComponentId() }}.updateSubmitButton()">Chcem aktualizovať existujúcu.</a>
        </div>

        <x-xfield type="submit" name="submit" label="Vytvoriť" />

        <script>
            class @alpineComponentClass {
                initCustom() {
                    // this.suggestTomInit('name');
                    this.initLabels();
                }

                onChangeBaseField(fieldName) {
                    this.initLabels();
                    if(!this.fields.md5.value) {
                        this.fields.update_book_locked.value = 1;
                        return;
                    }
                    if(!this.fields.update_book_locked.value) {
                        this.fields.update_book.value = 0;
                        this.fields.update_book_locked.value = 1;
                    }
                    this.updateSubmitButton();
                }

                onChangeExtendedField(fieldName) {
                    if(!this.fields.item_id.value) {
                        this.fields.update_chapter_locked.value = 1;
                        return;
                    }
                    if(!this.fields.update_chapter_locked.value) {
                        this.fields.update_chapter.value = 0;
                        this.fields.update_chapter_locked.value = 1;
                    }
                    this.updateSubmitButton();
                }

                suggestNameCallback(fieldName, value) {
                    if(fieldName === 'name') {
                        this.initLabels();
                        this.fields.kapitola.value = '';
                        this.fields.link.value = '';
                        this.fields['keywords'].value = [];
                        this.fields['keywords'].tomSelect.clear();
                        this.fields.update_book.value = 1;
                        this.fields.update_book_locked.value = 0;
                        this.fields.update_chapter.value = 1;
                        this.fields.update_chapter_locked.value = 0;
                        this.fields.item_id.value = 0;
                    }
                    this.updateSubmitButton();
                }

                suggestKapitolaCallback(fieldName, value) {
                    this.fields.update_chapter.value = 1;
                    this.fields.update_chapter_locked.value = 0;
                    this.updateSubmitButton();
                }

                suggestKapitolaRequestParamsCallback(fieldName, params) {
                    params.md5 = this.fields.md5.value;
                    return params;
                }

                initLabels()
                {
                    const type = this.fields.type.value;
                    if(type === 'kniha') {
                        this.fields.kapitola.label = 'Kapitola';
                        this.fields.vydavatelstvo.label = 'Vydavateľstvo';
                        this.fields.year.label = 'Rok vydania';
                        this.fields.info.label = 'ISBN';
                        this.fields.image.label = 'Obrázok (Obálka knihy)';
                    }
                    else if(type === 'zurnal') {
                        this.fields.kapitola.label = 'Special Issue';
                        this.fields.vydavatelstvo.label = 'Žurnál';
                        this.fields.year.label = 'Rok publikácie';
                        this.fields.info.label = 'DOI';
                        this.fields.image.label = 'Obrázok';
                    }
                    else if(type === 'prednaska') {
                        this.fields.kapitola.label = 'Problematika prednášky';
                        this.fields.vydavatelstvo.label = 'Názov predmetu';
                        this.fields.year.label = 'Rok prednášky';
                        this.fields.info.label = 'Garant predmetu';
                        this.fields.image.label = 'Obrázok (Screenshot z prednášky)';
                    }
                    else if(type === 'ine') {
                        this.fields.kapitola.label = 'Problematika';
                        this.fields.vydavatelstvo.label = 'Online zdroj';
                        this.fields.year.label = 'Aktuálny rok';
                        this.fields.info.label = 'Hyperlink';
                        this.fields.image.label = 'Obrázok';
                    }
                    this.updateSubmitButton();
                }

                updateSubmitButton()
                {
                    this.fields.submit.label = 'Vytvoriť';
                    if(this.fields.md5.value && this.fields.item_id.value && this.fields.update_book.value && this.fields.update_chapter.value) {
                        this.fields.submit.label = 'Aktualizovať';
                    }
                }
            }

        </script>

    </x-form-nova-literatura>



</x-layout>
