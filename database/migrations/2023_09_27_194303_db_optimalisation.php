<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('kega_items_test_questions__data', function (Blueprint $table) {
            $table->index('status', 'idx_status');

        });
        Schema::table('kega_items_test_questions__values', function (Blueprint $table) {
            $table->index(['enum_field_id', 'enum_field_value'], 'idx_enum');
        });

        \Illuminate\Support\Facades\DB::table('kega_items_users__data')->where('date_of_birth', '=', '0000-00-00')->update(['date_of_birth'=>null]);


        Schema::table('kega_items_users__data', function (Blueprint $table) {
            $table->date('date_of_birth')->nullable()->default(null)->change();
            $table->string('join_users_onlineusers', 255)->nullable()->default(null)->change();
            $table->text('session_settings')->nullable()->default(null)->change();
        });

        Schema::table('kega_items_test_questions__data', function (Blueprint $table) {
            $table->integer('nekorektnost')->nullable()->default(null)->change();
        });
        Schema::table('kega_items_test_answers__data', function (Blueprint $table) {
            $table->integer('nekorektnost')->nullable()->default(null)->change();
        });

    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('kega_items_test_questions__data', function (Blueprint $table) {
            $table->dropIndex('idx_status');
        });
        Schema::table('kega_items_test_questions__values', function (Blueprint $table) {
            $table->dropIndex('idx_enum');
        });
    }
};
