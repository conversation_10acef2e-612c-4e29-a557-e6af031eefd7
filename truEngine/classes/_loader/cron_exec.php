<?php


class cron_exec_gClass
{
    protected $max_exec_time_pVar;
    protected $name_pVar;

    function __construct()
    {
        $this->max_exec_time_pVar = 120; // 120s
        $this->name_pVar = 'cron_';
        session_gClass::giveMeFullAccess_gFunc();
    }

    function _run_gFunc()
    {
        set_time_limit(min($this->max_exec_time_pVar + 120, 300));
        log_gClass::logQueriesOff_gFunc();

        log_gClass::write_gFunc('SYSTEM_DOCUMENT', '1');
        $ret_pVar = $this->run_gFunc();

        echo 'done('.intval($ret_pVar).')';

        $xlevel_pVar = main_gClass::getInputInteger_gFunc('redirect', main_gClass::SRC_REQUEST_pVar, 0);
        if(!$ret_pVar && $xlevel_pVar > 0) {
            $xlevel_pVar--;
            echo '<script type="text/javascript">document.location=\'http://' . main_gClass::getServerVar_gFunc('SERVER_NAME') . main_gClass::getConfigVar_gFunc('') . main_gClass::getConfigVar_gFunc('web_dir_gVar', 'runtime_pVar') . 'sk/?' . $this->name_pVar . '=1&redirect=' . $xlevel_pVar . '\';</script>';
        }
        log_gClass::logQueriesOn_gFunc();

        return($ret_pVar);
    }

    protected function run_gFunc()
    {
        return(true);
    }

    protected function timeExceeded_gFunc()
    {
        return(main_gClass::timeExceeded_gFunc($this->max_exec_time_pVar));
    }
}
