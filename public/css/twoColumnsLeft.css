@charset "iso-8859-1";

/*******************************************************************************
*  twoColumnsLeft.css : 2004-08-17 : Ruthsar<PERSON> Layouts
* ------------------------------------------------------------------------------
*  This stylesheet removes the right-hand column from the 3 column layout
*******************************************************************************/

html>body #rightColumn
{
	display: none;	/* for compliant browsers, hide this column entirely */
}
* html #rightColumn *
{
	display: none;	/* ie's ghost-text bug is triggered by key elements
			   having display:none set. #rightColumn is one of those
			   key elements. so we display:none all child
			   elements (doesn't trigger the bug) and try to
			   make #rightColumn have no chance to affect
			   document flow. */
}
#rightColumn
{
	position: absolute;
	margin: 0;
	padding: 0;
	width: 0;
	height: 0;
	border: none;	/* remove anything that'll give this elemnt width. */
}
#outerColumnContainer, #innerColumnContainer
{
	border-right-width: 0;	/* to get from 3 to 2 columns, we must hide the
				   right-side border */
}
#innerColumnContainer
{
	margin-right: 0;
}

/******************************************************************************/