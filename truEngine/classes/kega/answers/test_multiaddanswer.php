<?php

class test_multiaddanswer_gClass extends additem_gClass
{

    protected function initParams_gFunc()
    {
        $this->setParam_gFunc('formtype-add','add_item');
        $this->setParam_gFunc('formtype-edit', 'edit_item');
        $this->setParam_gFunc('itemtype', 'test_answers');
    }

    protected function initForm_gFunc($multiedit_pVar = false, $initFormRef_pVar = true)
    {

//        if(isset($this->params['otazka']) && $this->params['otazka']) {
//            kega_gClass::recountQuestionStats_gFunc($this->params['otazka']);
//        }

        // id otazky beriem z formulara.. Pretoze po jeho odoslani uz nemam nastavene params..

        if(isset($this->params['otazka']) && $this->params['otazka']) {
            $otazka_pVar = $this->params['otazka'];
            $this->addHiddenField_gFunc('question_id', $this->params['otazka'], '/[0-9]+/');
        }
        else {
            $this->addHiddenField_gFunc('question_id', 0, '/[0-9]+/');
            $otazka_pVar = $this->getFieldValueFromRequest_gFunc(false, 'question_id');
        }
        if(empty($otazka_pVar)) {
            error_gClass::error_gFunc(__FILE__, __LINE__);
            return;
        }

        $fields_pVar = db_items_gClass::getItemsFields_gFunc('test_questions');
        $max_pVar = intval($fields_pVar['moznosti']['max_value']);
        if($max_pVar <= 0) {
            $max_pVar = 3;
        }

        $this->setVar_gFunc('question_id', $otazka_pVar);

        $item_ids_pVar = items_gClass::getItemsIds_gFunc($this->params['itemtype'], array('test_question'=>$otazka_pVar));
        for($i_pVar = 0; $i_pVar < $max_pVar; $i_pVar++) {
            $this->setParam_gFunc('item_id', array_shift($item_ids_pVar));
            $this->setHiddenFieldDefaultValue_gFunc('a' . $i_pVar . '_test_answers_test_question', $otazka_pVar, true); // overwrite, aby sa nedala prepisat requestom
            parent::initForm_gFunc(true);
        }



        /*
                if(parent::initForm_gFunc()) {
                    $this->addHiddenField_gFunc('otazka', $this->params['otazka']);
                    $this->setHiddenFieldDefaultValue_gFunc('test_answers_test_question', $this->params['otazka']);
                    $otazka_pVar = $this->getFieldValue_gFunc('otazka');
                    if(empty($otazka_pVar)) {
                        error_gClass::error_gFunc(__FILE__, __LINE__);
                        return;
                    }

                    // selectnem idcka otazok
                    $ids_pVar = items_gClass::getItemsIds_gFunc($this->params['itemtype'], array('test_question'=>$otazka_pVar));
                    print_r($ids_pVar);

                }
                */
    }

    protected function saveData_gFunc()
    {
        parent::saveData_gFunc();
        items_gClass::reorderItems_gFunc('test_answers', 'order', 1, '`test_question`=' . intval($this->getFieldValue_gFunc('otazka')));
        //este ulozim keywords pre otazku
        $info_pVar = db_items_gClass::getInfo_gFunc('test_questions');
        $data_pVar = array();

        $vars_pVar = main_gClass::getInputVarNames_gFunc(main_gClass::SRC_REQUEST_pVar);
        foreach($info_pVar['languages'] as $lng_pVar) {
            if(in_array('q-keywords-' . $lng_pVar, $vars_pVar)) {
                $data_pVar[$lng_pVar . '_keywords'] = main_gClass::getInputString_gFunc('q-keywords-' . $lng_pVar);
            }
        }
        if(count($data_pVar)) {
            $data_pVar['item_id'] = intval($this->getVar_gFunc('question_id'));
            items_gClass::saveOrUpdateItem_gFunc('test_questions', $data_pVar);
        }

        log_gClass::write_gFunc('TEST_QUESTION_UPDATED', $this->getVar_gFunc('question_id'), false, true);
    }
}

class test_multiaddanswer extends test_multiaddanswer_gClass {}
