<?php


class session_resend_email extends source_gClass
{
    protected function getData()
    {
        $user_id_pVar = $this->params['item_id'];
        $user_pVar = items_gClass::getItem_gFunc('users', intval($this->params['item_id']));

        // poslem overovaci mail...
        $time_limit = date('Y-m-d H:i:s', time() + 3600);
        $hash_pVar = sha1($user_id_pVar . $user_pVar['email'] . time());
        $subject_pVar = 'Overenie emailovej adresy';

        $template_pVar = '/.emails/check-email';
        $template_pVar = main_gClass::addLngPrefixToDocName_gFunc($template_pVar);

        $vars_pVar = array('mail'=>array());
        $vars_pVar['mail']['user'] = $user_pVar;
        $vars_pVar['mail']['url'] = main_gClass::getServerVar_gFunc('SERVER_NAME') . main_gClass::getConfigVar_gFunc('web_dir_gVar', 'runtime_pVar');
        $vars_pVar['mail']['time'] = $time_limit;

        $vars_pVar['mail']['link'] = 'http://' . main_gClass::getServerVar_gFunc('SERVER_NAME') . main_gClass::makeUrl_gFunc(main_gClass::addLngPrefixToDocName_gFunc('/log-in/overenie-emailu'));
        if(strpos($vars_pVar['mail']['link'], '?') !== false) {
            $vars_pVar['mail']['link'] .= '&';
        }
        else {
            $vars_pVar['mail']['link'] .= '?';
        }
        $vars_pVar['mail']['link'] .= 'email=' . $user_pVar['email'] . '&user_id=' . $user_id_pVar . '&hash=' . $hash_pVar;
        $vars_pVar['mail']['url_change_pass'] = 'http://' . main_gClass::getServerVar_gFunc('SERVER_NAME') . main_gClass::makeUrl_gFunc(main_gClass::addLngPrefixToDocName_gFunc('/log-in/nove-heslo'));

        $templateContent_pVar = callStack_gClass::getDocContent_gFunc($template_pVar, $vars_pVar);
        email_gClass::mailHtml_gFunc($user_pVar['email'], main_gClass::getConfigVar_gFunc('email', 'contacts'), $subject_pVar, $templateContent_pVar);

        // ulozim do DB
        $data_pVar = array(
            'email'=>$user_pVar['email'],
            'hash'=>$hash_pVar,
            'user_id'=>$user_id_pVar,
            'request_time'=>date('Y-m-d H:i:s'),
            'destroy_time'=>$time_limit
        );
        db_public_gClass::insertData_gFunc('%taccess__check_mail', '%s,%s,%d,%s,%s', $data_pVar, __FILE__, __LINE__);
        db_public_gClass::execute_gFunc('update %titems_users__data set email_check_status = \'changed\' where item_id = ' . intval($user_id_pVar), array(), __FILE__, __LINE__);
    }
}
