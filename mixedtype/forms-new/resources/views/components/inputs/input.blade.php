@props([
    'type' => 'text',
    'class' => 'form-control',
    'value' => null,
    'name' => null,
    'suggest' => null,
])

@php
    if($type !== 'text') {
        $suggest = null;
    }
    if($suggest === 'true' || $suggest === true) {
        $suggest = 'suggest';
    }

@endphp

<input
    class="{{ $class }}"
    type="{{ $type }}" x-bind:type="@xField($name).type"
    value="{{ $value }}" x-model="@xField($name).value"
    name="{{ $name }}"
    @if($suggest === 'raw') x-on:keyup="@xStore.suggest{{ ucfirst($suggest) }}('{{ $name }}')" autocomplete="off" @endif
    {{ $attributes }}
>

@if($suggest === 'raw')
        <style>
            .dropdown-visible {
                    display: inline-block;
                    width: 100%;
                    position: absolute;
                    top: 0;
                    margin-bottom: 1rem !important;
            }
        </style>

        <div class="dropdown">
            <div x-show="@xField($name).suggestVisible" class="dropdown-menu dropdown-visible">
                <template x-for="item in @xField($name).suggest.data">
                    <a href="#" class="dropdown-item" x-on:click.prevent="@xStore.suggestRawSelect('{{ $name }}', item)" x-text="item.value"></a>
                </template>
            </div>
        </div>
        <script>
            //document.addEventListener('alpine:init', () => {
                // hide if click outside
                document.addEventListener('click', (event) => {
                        Alpine.store('{{ \Mixedtype\Components\ComponentIdManager::getComponentId() }}').suggestRawClickedOutside('{{ $name }}', event)
                })
            //});
        </script>
@endif





