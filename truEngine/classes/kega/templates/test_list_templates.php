<?php


class test_list_templates_gClass extends source_gClass
{
    protected function copyTest($source_id)
    {
        $sql = 'select * from %titems_test_templates__data where item_id = %d';
        $test = db_public_gClass::getResult_gFunc($sql, __FILE__, __LINE__, $source_id);
        unset($test['item_id']);
        $test['parametric'] = 'no';
        $test['insert_time'] = date('Y-m-d H:i:s');
        $test['update_time'] = date('Y-m-d H:i:s');
        $new_id = db_public_gClass::insertData_gFunc('%titems_test_templates__data', false, $test, __FILE__, __LINE__, true);
    }

    protected function getData()
    {

        $copy_id = main_gClass::getInputString_gFunc('copy_id', main_gClass::SRC_GET_pVar);
        if($copy_id) {
            self::copyTest($copy_id);
        }

        /*********
        $sql_pVar = 'SELECT * FROM %ttests WHERE status = \'active\'';
        $x = db_public_gClass::getResultArray_gFunc($sql_pVar, __FILE__, __LINE__);
        $id = 5000;
        foreach($x as $test) {
        $data = array();
        $data['item_id'] = intval($test['id']) + 5000;
        $data['status'] = 'active';
        $data['owner_id'] = $test['creator_id'];
        $data['insert_time'] = $test['time_create'];
        $data['time_start'] = $test['time_start'];
        $data['time_stop'] = $test['time_stop'];
        $data['sk_name'] = $test['sk_name'].'';
        $data['en_name'] = $test['en_name'].'';
        $data['cz_name'] = $test['cz_name'].'';
        $data['sk_description'] = $test['sk_description'];
        $data['en_description'] = $test['en_description'];
        $data['cz_description'] = $test['cz_description'];
        $data['smer'] = $test['smer'];
        $data['rocnik'] = $test['rocnik'];
        $data['official'] = $test['official'];
        $data['parametric'] = 'yes';
        $data['access_key'] = $test['access_key'];

        $settings = explode("\n", $test['settings']);
        foreach($settings as $k=>$v) {
        $xxx = explode('=', $v);
        $xxx[0] = trim($xxx[0]);
        $xxx[1] = trim($xxx[1]);
        if(empty($xxx[0]) || empty($xxx[1])) {
        continue;
        }
        $data[$xxx[0]] = $xxx[1];
        }

        $format = array();
        for($i = 0; $i<count($data); $i++) {
        $format[] = '%s';
        }
        $format = implode(',', $format);
        echo db_public_gClass::insertData_gFunc('%titems_test_templates__data', $format, $data, __FILE__, __LINE__, true);
        echo '<pre>'; print_r($data); echo '</pre>';
        echo '<hr />';
        }
         */////////////////////

        $templates_pVar = items_gClass::getItems_gFunc('test_templates', $this->params);
        unset($templates_pVar['filter']);

        $this->formatData_gFunc($templates_pVar);
        return($templates_pVar);
    }

    protected function formatData_gFunc(&$data_pVar)
    {
        foreach ($data_pVar as $k_pVar=>$v_pVar) {
            if(substr($k_pVar, 0, 1) === '_') {
                continue;
            }
        }
    }
}

class test_list_templates extends test_list_templates_gClass {}
