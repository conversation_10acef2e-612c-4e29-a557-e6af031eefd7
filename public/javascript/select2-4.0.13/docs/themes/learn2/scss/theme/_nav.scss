#sidebar-toggle {
    display: none;

    @include breakpoint(mobile-only) {
         display: inline-block;
     }

}

#sidebar {
    @extend .default-animation;
    background-color: $sidebar-bg;
    position: fixed;
    top: 0;
    width: $sidebar-width;
    bottom: 0;
    left: 0;
    font-weight: $font-weight-medium;
    font-size: 15px;

    a {
        color: $sidebar-link;
        &:hover {
            color: lighten($sidebar-link, 10%);
        }
        &.subtitle {
            color: rgba($sidebar-link, 0.6);
        }
    }

    hr {
        border-bottom: 1px solid darken($sidebar-bg, 3%);
    }

    a.padding {
        padding: 0 1rem;
    }

    h5 {
        margin: 2rem 0 0;
        position: relative;
        line-height: 2;

        a {
            display: block;
            margin-left: 0;
            margin-right: 0;
            padding-left: 1rem;
            padding-right: 1rem;
        }

        i {
            color: rgba($sidebar-link, 0.6);
            position: absolute;
            right: 0.6rem;
            top: 0.7rem;
            font-size: 80%;
        }

        &.parent {
            a {
                background: darken($sidebar-bg, 7%);
                color: lighten($sidebar-link, 5%) !important;
            }
        }

        &.active {
            a {
                background: $white;
                color: $core-text !important;
            }

            i {
                color: $core-text !important;
            }
        }


    }

    h5 + ul.topics {
        display: none;
        margin-top: 0;
    }

    h5.parent, h5.active {
        + ul.topics {
            display: block;
        }
    }


    ul {
        @extend .default-animation;
        list-style: none;
        padding: 0;
        margin: 0;

        &.searched {
            a {
                color: darken($sidebar-link, 20%);
            }

            .search-match {
                a {
                    color: lighten($sidebar-link, 10%);
                    &:hover {
                        color: lighten($sidebar-link, 20%);
                    }
                }
            }
        }

        &.topics {
            margin: 0 1rem;

            &.searched {
                ul {
                    display: block;
                }
            }

            ul {
                display: none;
                padding-bottom: 1rem;

                ul {
                    padding-bottom: 0;
                }
            }

            li.parent ul, > li.active ul {
                display: block;
            }

            > li {
                > a {
                    line-height: 2rem;
                    font-size: 1.1rem;

                    b {
                        opacity: 0.5;
                        font-weight: normal;
                    }

                    .fa {
                        margin-top: 9px;
                    }
                }

                &.parent, &.active {
                    background: darken($sidebar-bg, 5%);
                    margin-left: -1rem;
                    margin-right: -1rem;
                    padding-left: 1rem;
                    padding-right: 1rem;
                }
            }
        }

        li.active > a {
            background: $white;
            color: $core-text !important;
            margin-left: -1rem;
            margin-right: -1rem;
            padding-left: 1rem;
            padding-right: 1rem;
        }

        li {
            padding: 0;
            &.visited + span {
                margin-right: 16px;
            }
            a {
                display: block;
                padding: 2px 0;
                span {
                    text-overflow: ellipsis;
                    overflow: hidden;
                    white-space: nowrap;
                    display: block;
                }
            }
            > a {
                padding: 4px 0;
            }

            .fa {
                display: none;
                float: right;
                font-size: 13px;
                min-width: 16px;
                margin: 4px 0 0 0;
                text-align: right;
            }

            &.visited {
                > a .read-icon {
                    color: $core-accent;
                    display: inline;
                }
            }

            li {
                padding-left: 1rem;
                text-indent: 0.2rem;
            }
        }

    }
}
