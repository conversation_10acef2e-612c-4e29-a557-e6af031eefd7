<?xml version="1.0" encoding="utf-8" ?>
<truEngine-document xmlns:te="http://www.truengine.sk" te:access="s_test_show_comments">

<te:content-set name="title" value="@L{Komentáre k otázke}" />

<te:source name="test_question_addcomment" text="@{request:addcomment_text}" item_id="@{request:item_id}" />
<te:include name="/sk/components/question_details" question_id="@{request:item_id}" edit_enabled="0" />

<form method="post" action="/@{doc}">
<textarea name="addcomment_text" style="width:100%"></textarea><br /><input type="submit" value="@L{Pridať komentár}" />
<input type="hidden" name="item_id" value="@{request:item_id}" />
</form>

<te:source name="test_question_comments" varname="comments" item_id="@{request:item_id}" columns="&amp;nbsp;,comment_datetime,nick,comment_text" delete_id="@{request:delete_id}" />

<te:include name="/sk/components/table" table="@@{comments}">
	<te:include-param name="&amp;nbsp;">
		<a href="/otazky/komentare-k-otazke?item_id=%%item_id%%&amp;delete_id=%%comment_id%%" te:access="s_test_delete_comment" onclick="return(confirm('@L{Naozaj chcete zmazať komentár?}'));">@L{Zmazať komentár}</a>
	</te:include-param>
</te:include>

</truEngine-document>