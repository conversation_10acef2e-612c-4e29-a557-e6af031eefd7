<x-layout layout="auth">

    <x-mixedtype-auth::forgot-password-form autocomplete="off" novalidate>
        @include('mixedtype-auth::logo')

        <div class="card card-md">
            <div class="card-body">

                <h2 class="card-title text-center mb-4">Forgot your password?</h2>

                {{ __('No problem. Just let us know your email address and we will email you a password reset link that will allow you to choose a new one.') }}

                <x-xfield type="email" name="email" label="Email address" placeholder="<EMAIL>" autocomplete="off" />

                @if (session('status'))
                    <div class="alert alert-important alert-success alert-dismissible" role="alert">
                        <div class="alert-icon">
                            <!-- Download SVG icon from http://tabler.io/icons/icon/check -->
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="icon alert-icon icon-2">
                                <path d="M5 12l5 5l10 -10"></path>
                            </svg>
                        </div>
                        <a class="btn-close" data-bs-dismiss="alert" aria-label="close"></a>
                    </div>
                @endif

                <x-xfield type="submit" name="submit" label="{{ __('Email Password Reset Link') }}" icon="mail" />

            </div>
        </div>
    </x-mixedtype-auth::forgot-password-form>
</x-layout>
