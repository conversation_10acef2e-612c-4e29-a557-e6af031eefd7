<?php

class email_gClass {

    static function mailHtml_gFunc($to_pVar, $from_pVar, $subject_pVar, $htmlMessage_pVar, $additionalHeaders_pVar=array())
    {
        $timestamp = sprintf('%0.6f', debug_gClass::getMicrotime_gFunc());
        $headers_pVar=array('MIME-Version' => '1.0',
            'Content-Type' => 'multipart/alternative; boundary="www.truEngine.sk.'.$timestamp.'.mime"'
        );
        // To send HTML mail, the Content-type header must be set
        // $headers  = 'MIME-Version: 1.0' . "\r\n";
        // $headers .= 'Content-type: text/html; charset=iso-8859-1' . "\r\n";

        foreach ($additionalHeaders_pVar as $k_pVar=>$v_pVar) {
            $headers_pVar[$k_pVar] = $v_pVar;
        }

        // $headers_pVar['To']=$to_pVar;
        $headers_pVar['From']=$from_pVar;

        $headersStr_pVar='';
        reset($headers_pVar);
        foreach ($headers_pVar as $k_pVar=>$v_pVar) {
            $headersStr_pVar .= $k_pVar . ': ' . $v_pVar . CR . LF;
        }

        $subject7bit_pVar = self::encode7bit_gFunc($subject_pVar);

        $subject_pVar = '=?utf-8?q?' . $subject7bit_pVar . "?=";

        $message_pVar = '';
        $message_pVar .= '--www.truEngine.sk.'.$timestamp.'.mime' . CR . LF;
        $message_pVar .= 'Content-Type: text/plain; charset="utf-8"' . CR . LF;
        $message_pVar .= 'Content-Transfer-Encoding: 7bit' . CR . LF;
        $message_pVar .= CR . LF;
        $message_pVar .= self::htmlToPlaintext_gFunc($htmlMessage_pVar);
        $message_pVar .= CR . LF;
        $message_pVar .= '--www.truEngine.sk.'.$timestamp.'.mime' . CR . LF;
        $message_pVar .= 'Content-Type: text/html; charset="utf-8"' . CR . LF;
        $message_pVar .= 'Content-Transfer-Encoding: base64' . CR . LF;
        $message_pVar .= CR . LF;
        $message_pVar .= base64_encode($htmlMessage_pVar);
        $message_pVar .= CR . LF;
        $message_pVar .= '--www.truEngine.sk.'.$timestamp.'.mime--' . CR . LF;

        // Mail it
        log_gClass::write_gFunc('SEND_MAIL', 'to:'.$to_pVar . '|Subject:'. $subject_pVar);
        //23.3.2014 Kanitra -- zakomentovanie kvoli nefunkcnosti mail servera
        mail($to_pVar, $subject_pVar, $message_pVar, $headersStr_pVar);
    }

    private static function encode7bit_gFunc($str_pVar)
    {
        $str2_pVar = '';
        $i_pVar = 0;

        while(isset($str_pVar[$i_pVar])) {
            if(ord($str_pVar[$i_pVar]) > 127 || ord($str_pVar[$i_pVar]) == 32 || $str_pVar[$i_pVar] == '=') {
                $str2_pVar .= '=' . sprintf('%02x',ord($str_pVar[$i_pVar]));
            }
            else {
                $str2_pVar .= $str_pVar[$i_pVar];
            }
            $i_pVar++;
        }

        return($str2_pVar);
    }

    private static function htmlToPlaintext_gFunc($html_pVar)
    {;
        $html_pVar = string_gClass::removeDiacritic_gFunc($html_pVar);

        $html_pVar = preg_replace('/<h[123456][^>]?>/i', CR . LF . ' + ', $html_pVar);
        $html_pVar = preg_replace('/<\/h[123456]>/i', ' + '. CR . LF, $html_pVar);

        $html_pVar = preg_replace('/	/i', ' ', $html_pVar);

        $html_pVar = preg_replace('/<table[^>]?>/i', CR . LF, $html_pVar);
        $html_pVar = preg_replace('/<\/table>/i', CR . LF, $html_pVar);

        $html_pVar = preg_replace('/<head[^>]?>.*<\/head>/i', '', $html_pVar);
        $html_pVar = preg_replace('/<style[^>]?>.*<\/style>/i', '', $html_pVar);
        $html_pVar = preg_replace('/<script[^>]?>.*<\/style>/i', '', $html_pVar);

        $html_pVar = preg_replace('/<t(d|h)[^>]?>/i', TAB, $html_pVar);
        $html_pVar = preg_replace('/<\/t(d|h)>/i', ' ', $html_pVar);

        $html_pVar = preg_replace('/<tr[^>]?>/i', CR . LF, $html_pVar);
        $html_pVar = preg_replace('/<\/tr>/i', '', $html_pVar);

        $html_pVar = preg_replace('/<strong[^>]?>/i', ' * ', $html_pVar);
        $html_pVar = preg_replace('/<\/strong>/i', ' * ', $html_pVar);


        /*
                   $tags = array (
                0 => '~<h[123][^>]+>~si',
                1 => '~<h[456][^>]+>~si',
                2 => '~<table[^>]+>~si',
                3 => '~<tr[^>]+>~si',
                4 => '~<li[^>]+>~si',
                5 => '~<br[^>]+>~si',
                6 => '~<p[^>]+>~si',
                7 => '~<div[^>]+>~si',
                );
                $html_pVar = preg_replace($tags,"\n",$html_pVar);
                $html_pVar = preg_replace('~</t(d|h)>\s*<t(d|h)[^>]+>~si',' - ',$html_pVar);
                $html_pVar = preg_replace('~<[^>]+>~s','',$html_pVar);
                // reducing spaces
                $html_pVar = preg_replace('~ +~s',' ',$html_pVar);
                $html_pVar = preg_replace('~^\s+~m','',$html_pVar);
                $html_pVar = preg_replace('~\s+$~m','',$html_pVar);
                // reducing newlines
                $html_pVar = preg_replace('~\n+~s',"\n",$html_pVar);
                */

        $html_pVar = strip_tags($html_pVar);
        return($html_pVar);
    }

}
