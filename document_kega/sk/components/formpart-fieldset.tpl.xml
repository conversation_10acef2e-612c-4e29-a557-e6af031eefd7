<?xml version="1.0" encoding="utf-8" ?>
<truEngine-document xmlns:te="http://www.truengine.sk">
	<?php
		@{fieldset} = isset(@{form:fieldsets}[@{fieldset}]) ? @{form:fieldsets}[@{fieldset}] : false;
        @{QFORM} = isset(@{form:hidden_fields:test_questions_status});

	?>
		<te:if test="count(@{fieldset:fields}) &amp;&amp; @{fieldset} !== false">
			<fieldset class="form_fieldset">
				<te:if test="isset(@{fieldset:legend})"><legend>@{fieldset:legend}@{form:workspace:nSUFFIX}</legend></te:if>
				<te:if test="isset(@{fieldset:comment})">@{fieldset:comment}</te:if>

				<?php @{use_class} = isset(@{form:workspace:classes}[@{fieldset:name}])?@{form:workspace:classes}[@{fieldset:name}]:@{form:workspace:classes}['main']; ?>

                <?php if(@{QFORM} && @{fieldset:name} === 'main'): ?>
                    <te:include name="/sk/components/questionform" form="@@{form}" fieldset="@@{fieldset}" />
                <?php else: ?>

                    <div class="@{use_class}">
                        <table class="form_table">
                            <tbody>
                                <?php $n=0; ?>
                                <te:for each="@{fieldset:fields} as @{field}">
                                    <?php
                                        if($n%2) { @{attrs}=array('class'=>'even'); } else { @{attrs} = array(); }

                                        if(@{field:error_code} != 'ok') { @{input_attrs}=array('style'=>'background-color:#FFAAAA;'); } else { @{input_attrs} = array(); }
                                        @{input_attrs} = array_merge(@{input_attrs}, @{field:events});

                                        if(isset(@{field:lngs}) && is_array(@{field:lngs})) {
                                            $lngs = array();
                                            foreach(@{field:lngs} as $tmp_lng) {
                                                @{tmp_lng} = $tmp_lng;
                                                $lngs[] = '<a href="#" onclick="showFieldLng(\'tr_\' , \''.@{field:name}.'\', \''.@{tmp_lng}.'\'); return(false);">' . $tmp_lng . '</a>';
                                            }
                                            $lngs = implode(' ', $lngs);
                                            if(!empty($lngs)) {
                                                $lngs = '<small class="lngs">[' . $lngs . ']</small> ';
                                            }
                                            @{field:label} = @{field:label} . '<br />' . $lngs;
                                        }

                                        @{attrs:id} = 'tr_' . @{field:name};
                                        $class = @{attrs:id};
                                        $class = str_replace('_sk_' , '_XX_', $class);
                                        $class = str_replace('_en_' , '_XX_', $class);
                                        $class = str_replace('_cz_' , '_XX_', $class);
                                        if(isset(@{attrs:class})) {
                                            @{attrs:class} .= ' ' . $class;
                                        }
                                        else {
                                            @{attrs:class} = $class;
                                        }

                                        @{label} = @{field:label};
                                        @{label} = str_replace('[sk]', '<img alt="SK" title="SK" src="/sk/images/icons/sk.jpg" />', @{label});
                                        @{label} = str_replace('[en]', '<img alt="EN" title="EN" src="/sk/images/icons/en.jpg" />', @{label});
                                        @{label} = str_replace('[cz]', '<img alt="CZ" title="CZ" src="/sk/images/icons/cz.jpg" />', @{label});

                                ?>
                                    <tr te:attrs="@{attrs}">
                                        <td class="td_label" valign="top">
                                                <?php
                                                    $x = false;
                                                    $p = strpos('@{field:name}', '_sk_');
                                                    if($p !== false) { $x = substr('@{field:name}', $p+4); }
                                                    $p = strpos('@{field:name}', '_en_');
                                                    if($p !== false) { $x = substr('@{field:name}', $p+4); }
                                                    $p = strpos('@{field:name}', '_cz_');
                                                    if($p !== false) { $x = substr('@{field:name}', $p+4); }
                                                    if(!empty($x)) {
                                                            echo '<script type="text/javascript">';
                                                            echo 'lng_fields.'.$x.' = true;';
                                                            echo '</script>';
                                                    }
                                                ?>
                                            <label for="@{field:name}">@{label}</label>
                                                    <te:if test="@{field:required}"><span title="@L{Pole je povinne}">*</span></te:if>
                                                    <te:if test="!empty(@{field:info})">
                                                        <img src="/images/icons/info_small.png" class="info_icon tipz" title="@{field:info}" />
                                                    </te:if>

                                                    <te:if test="@{form:error_code} == 'error' &amp;&amp; @{field:error_code}!='ok'">
                                                        <te:if test="!empty(@{field:error_message})">
                                                            <img src="/images/icons/error_small.png" class="info_icon tipz" title="::@{field:error_message}" />
                                                        <te:else />
                                                            <img src="/images/icons/error_small.png" />
                                                        </te:if>
                                                    </te:if>

                                                    <te:if test="@{field:type} == 'set' &amp;&amp; count(@{field:options}) > 10">
                                                        <br />
                                                        <span style="color:#aaa;font-size:9px;">@L{Maska}:</span> <input type="input" onkeyup="formSetApplyMask(this.value, this);" style="font-size:9px;" /><br />
                                                    </te:if>

                                            </td>
                                        <td class="td_input">
                                            <te:if test="@{field:editable}">
                                                <te:choose test="@{field:type}">
                                                    <te:when case="varchar">
                                                        <input class="input_text" name="@{field:name}" id="@{field:name}" value="@{field:value}" type="text" te:attrs="@{input_attrs}" />
                                                    </te:when>
                                                    <te:when case="password">
                                                        <input class="input_text" name="@{field:name}" id="@{field:name}" value="" type="password" te:attrs="@{input_attrs}" />
                                                    </te:when>
                                                    <te:when case="email">
                                                        <input class="input_text" name="@{field:name}" id="@{field:name}" value="@{field:value}" type="text" te:attrs="@{input_attrs}" />
                                                    </te:when>
                                                    <te:when case="int">
                                                        <input class="input_number" name="@{field:name}" id="@{field:name}" value="@{field:value}" type="text" te:attrs="@{input_attrs}" />
                                                    </te:when>
                                                    <te:when case="checkbox">
                                                        <input class="input_checkbox" name="@{field:name}" id="@{field:name}" type="checkbox" te:attrs="@{input_attrs}" />
                                                    </te:when>
                                                    <te:when case="float">
                                                        <input class="input_number" name="@{field:name}" id="@{field:name}" value="@{field:value}" type="text" te:attrs="@{input_attrs}" />
                                                    </te:when>
                                                    <te:when case="date">
                                                        <span class="input_day"><input name="@{field:name}_d" id="@{field:name}_d" value="@{field:multi_value:d}" type="text" te:attrs="@{input_attrs}" /></span>.<span class="input_month"><input name="@{field:name}_m" id="@{field:name}_m" value="@{field:multi_value:m}" type="text" te:attrs="@{input_attrs}" /></span>.<span class="input_year"><input name="@{field:name}_y" id="@{field:name}_y" value="@{field:multi_value:y}" type="text" te:attrs="@{input_attrs}" /></span>
                                                    </te:when>
                                                    <te:when case="datetime">
                                                        <span class="input_day"><input name="@{field:name}_d" id="@{field:name}_d" value="@{field:multi_value:d}" type="text" te:attrs="@{input_attrs}" /></span>.<span class="input_month"><input name="@{field:name}_m" id="@{field:name}_m" value="@{field:multi_value:m}" type="text" te:attrs="@{input_attrs}" /></span>.<span class="input_year"><input name="@{field:name}_y" id="@{field:name}_y" value="@{field:multi_value:y}" type="text" te:attrs="@{input_attrs}" /></span>
                                                        <span class="input_hour"><input name="@{field:name}_h" id="@{field:name}_h" value="@{field:multi_value:h}" type="text" te:attrs="@{input_attrs}" /></span>:<span class="input_minute"><input name="@{field:name}_i" id="@{field:name}_i" value="@{field:multi_value:i}" type="text" te:attrs="@{input_attrs}" /></span>:<span class="input_second"><input name="@{field:name}_s" id="@{field:name}_s" value="@{field:multi_value:s}" type="text" te:attrs="@{input_attrs}" /></span>
                                                    </te:when>
                                                    <te:when case="textarea">
                                                        <textarea class="input_textarea" name="@{field:name}" id="@{field:name}" te:attrs="@{input_attrs}">@{field:value}</textarea>
                                                    </te:when>
                                                    <te:when case="text">
                                                        <textarea class="input_textarea" name="@{field:name}" id="@{field:name}" te:attrs="@{input_attrs}">@{field:value}</textarea>
                                                    </te:when>

                                                    <te:when case="enum">
                                                        <te:if test="!is_array(reset(@{field:options}))">
                                                            <select class="input_select" name="@{field:name}" id="@{field:name}" onclick="selectbox_confirm_click(this);" onchange="selectbox_confirm_change(this);" te:attrs="@{input_attrs}">
                                                                <te:for each="@{field:options} as @{option_key}=>@{option_value}">
                                                                    <te:if test="@{option_key}==@{field:value}">
                                                                        <option value="@{option_key}" selected="selected">@{option_value}</option>
                                                                    <te:else />
                                                                        <option value="@{option_key}">@{option_value}</option>
                                                                    </te:if>
                                                                </te:for>
                                                            </select>
                                                        <te:else />
                                                            <select class="input_select" name="@{field:name}" id="@{field:name}" te:attrs="@{input_attrs}">
                                                                <option></option>
                                                                <te:for each="@{field:options} as @{optiongroup_key}=>@{optiongroup_value}">
                                                                    <optgroup label="@{optiongroup_value:optgroup}">
                                                                    <te:for each="@{optiongroup_value} as @{option_key}=>@{option_value}">
                                                                        <te:if test="@{option_key}!='optgroup'">
                                                                            <te:if test="@{option_key}==@{field:value}">
                                                                                <option value="@{option_key}" selected="selected">@{option_value}</option>
                                                                            <te:else />
                                                                                <option value="@{option_key}">@{option_value}</option>
                                                                            </te:if>
                                                                        </te:if>
                                                                    </te:for>
                                                                    </optgroup>
                                                                </te:for>
                                                            </select>
                                                        </te:if>
                                                        <te:if test="count(@{field:confirm_set})">
                                                            <script type="text/javascript">
                                                                tmpx = document.getElementById('@{field:name}');
                                                                if(tmpx) {
                                                                    <?php @{n} = 0; ?>
                                                                    <te:for each="@{field:options} as @{k}=>@{v}">
                                                                        <te:if test="isset(@{field:confirm_set}[@{k}])">
                                                                            tmpx.options[@{n}].confirm_set = '<?php echo addslashes(@{field:confirm_set}[@{k}]); ?>';
                                                                        </te:if>
                                                                        <?php @{n}++; ?>
                                                                    </te:for>
                                                                }
                                                            </script>
                                                        </te:if>
                                                        <te:if test="count(@{field:confirm_unset})">
                                                            <script type="text/javascript">
                                                                tmpx = document.getElementById('@{field:name}');
                                                                if(tmpx) {
                                                                    <?php @{n} = 0; ?>
                                                                    <te:for each="@{field:options} as @{k}=>@{v}">
                                                                        <te:if test="isset(@{field:confirm_unset}[@{k}])">
                                                                            tmpx.options[@{n}].confirm_unset = '<?php echo addslashes(@{field:confirm_unset}[@{k}]); ?>';
                                                                        </te:if>
                                                                        <?php @{n}++; ?>
                                                                    </te:for>
                                                                }
                                                            </script>
                                                        </te:if>
                                                    </te:when>

                                                    <te:when case="set">
                                                        <te:if test="!is_array(reset(@{field:options}))">

                                                            <te:for each="@{field:options} as @{option_key}=>@{option_value}">
                                                                <te:if test="array_search(@{option_key}, @{field:multi_value}) !== false">
                                                                    <div style="white-space: nowrap" class="set-checkbox"><input type="checkbox" name="@{field:name}_@{option_key}" id="@{field:name}_@{option_key}" checked="checked" /><label for="@{field:name}_@{option_key}">@{option_value}</label></div>
                                                                </te:if>
                                                            </te:for>
                                                            <te:for each="@{field:options} as @{option_key}=>@{option_value}">
                                                                <te:if test="array_search(@{option_key}, @{field:multi_value}) !== false">
                                                                <te:else />
                                                                    <div style="white-space: nowrap" class="set-checkbox"><input type="checkbox" name="@{field:name}_@{option_key}" id="@{field:name}_@{option_key}" /><label for="@{field:name}_@{option_key}">@{option_value}</label></div>
                                                                </te:if>
                                                            </te:for>

                                                            <te:if test="count(@{field:options})>1">
                                                                <span style="white-space: nowrap"><input type="checkbox" id="@{field:name}_checkall" onclick="checkboxes_@{field:name}(this);" /><label for="@{field:name}_checkall">označiť všetky</label></span>
                                                                <script type="text/javascript">
                                                                    function checkboxes_@{field:name}(checkbox) {
                                                                        var y = $j('.set-checkbox:visible input', $j(checkbox).parent().parent());
                                                                        y.each(function (index) { y[index].checked = checkbox.checked; });
                                                                    }
                                                                </script>
                                                            </te:if>
                                                        <te:else />
                                                            <!-- zatial neimplementovane -->
                                                        </te:if>
                                                    </te:when>

                                                    <te:when case="intInterval">
                                                        <label for="@{field:name}_min">@L{od}:</label><input name="@{field:name}_min" id="@{field:name}_min" value="@{field:multi_value:min}" type="text" te:attrs="@{input_attrs}" />
                                                        <label for="@{field:name}_max">@L{do}:</label><input name="@{field:name}_max" id="@{field:name}_max" value="@{field:multi_value:max}" type="text" te:attrs="@{input_attrs}" />
                                                    </te:when>

                                                    <te:when case="floatInterval">
                                                        <label for="@{field:name}_min">@L{od}:</label><input name="@{field:name}_min" id="@{field:name}_min" value="@{field:multi_value:min}" type="text" te:attrs="@{input_attrs}" />
                                                        <label for="@{field:name}_max">@L{do}:</label><input name="@{field:name}_max" id="@{field:name}_max" value="@{field:multi_value:max}" type="text" te:attrs="@{input_attrs}" />
                                                    </te:when>

                                                    <te:when case="imagelist">
                                                        <te:string id="Súbor" />: <input type="file" name="@{field:name}" />

                                                        <a href="#" onclick="showBlock('@{field:name}_copyright_id', 'block'); return(false);">
                                                            <te:string id="zdroj" />
                                                        </a>

                                                        <div style="display:none;" id="@{field:name}_copyright_id">@L{Zdroj}:&amp;nbsp;&amp;nbsp; <input type="text" name="@{field:name}_copyright" /></div>
                                                        <te:if test="is_array(@{field:value}) &amp;&amp; count(@{field:value})" >
                                                            <br />
                                                            <te:for each="@{field:value} as @{kimg}=>@{img}">
                                                                <div style="float:left; margin:3px; position:relative;" id="@{field:name}_img_@{kimg}">
                                                                    <te:if test="!empty(@{img:copyright})">
                                                                        <a href="@{img:0:src}" target="_blank"><img src="@{img:th1_:src}" style="width:50px;" title="Zdroj: @{img:copyright}" /></a>
                                                                    <te:else />
                                                                        <a href="@{img:0:src}" target="_blank"><img src="@{img:th1_:src}" style="width:50px;" /></a>
                                                                    </te:if>
                                                                    <br />
                                                                    <a href="#" style="position:absolute; left:0px; top:0px; background-color:white; text-decoration:none;" onclick="if(confirm('@L{Naozaj chcete zmazať obrázok?}')) { document.getElementById('@{field:name}_img_@{kimg}').style.display='none'; document.getElementById('@{field:name}_delete_@{kimg}').value=@{img:file_id}; } return(false);">X</a>
                                                                </div>
                                                            </te:for>
                                                        </te:if>
                                                    </te:when>

                                                    <te:when case="filelist">
                                                        <te:string id="Súbor" />: <input type="file" name="@{field:name}" /> <a href="#" onclick="showBlock('@{field:name}_copyright_id', 'block'); return(false);"><te:string id="zdroj" /></a>
                                                        <div style="display:none;" id="@{field:name}_copyright_id">@L{Zdroj}:&amp;nbsp;&amp;nbsp; <input type="text" name="@{field:name}_copyright" /></div>
                                                        <te:if test="is_array(@{field:value}) &amp;&amp; count(@{field:value})" >
                                                            <br />
                                                            <te:for each="@{field:value} as @{kfile}=>@{file}">
                                                                <div style="margin:3px;" id="@{field:name}_file_@{kfile}">
                                                                <a href="#" style="background-color:white; text-decoration:none;" onclick="if(confirm('@L{Naozaj chcete zmazať súbor?}')) { document.getElementById('@{field:name}_file_@{kfile}').style.display='none'; document.getElementById('@{field:name}_delete_@{kfile}').value=@{file:file_id}; } return(false);">X</a>
                                                                    <?php echo basename(@{file:src}); ?>
                                                                    <te:if test="!empty(@{file:copyright})">
                                                                        <te:string id="Zdroj" />: @{file:copyright}
                                                                    </te:if>
                                                                </div>
                                                            </te:for>
                                                        </te:if>
                                                    </te:when>


                                                    <te:when case="ximagelist">
                                                        <te:string id="Súbor" />: <input type="file" name="@{field:name}" /> <a href="#" onclick="showBlock('@{field:name}_copyright_id', 'block'); return(false);"><te:string id="zdroj" /></a>
                                                        <div style="display:none;" id="@{field:name}_copyright_id"><te:string id="Zdroj:&amp;nbsp;&amp;nbsp;" /> <input type="text" name="@{field:name}_copyright" /></div>
                                                        <te:if test="is_array(@{field:value}) &amp;&amp; count(@{field:value})" >
                                                            <br />
                                                            <te:for each="@{field:value} as @{kimg}=>@{img}">
                                                                <div style="float:left; margin:3px; position:relative;" id="@{field:name}_img_@{kimg}">
                                                                    <te:if test="!empty(@{img:copyright})">
                                                                        <a href="@{img:0:src}" target="_blank"><img src="@{img:th1_:src}" style="width:50px;" title="@L{Zdroj}: @{img:copyright}" /></a>
                                                                    <te:else />
                                                                        <a href="@{img:0:src}" target="_blank"><img src="@{img:th1_:src}" style="width:50px;" /></a>
                                                                    </te:if>
                                                                    <br />
                                                                    <a href="#" style="position:absolute; left:0px; top:0px; background-color:white; text-decoration:none;" onclick="if(confirm('@L{Naozaj chcete zmazať obrázok?}')) { document.getElementById('@{field:name}_img_@{kimg}').style.display='none'; document.getElementById('@{field:name}_delete_@{kimg}').value=@{img:file_id}; } return(false);">X</a>
                                                                </div>
                                                            </te:for>
                                                        </te:if>
                                                    </te:when>

                                                    <te:when case="xfilelist">
                                                        <te:string id="Súbor" />: <input type="file" name="@{field:name}" /> <a href="#" onclick="showBlock('@{field:name}_copyright_id', 'block'); return(false);"><te:string id="zdroj" /></a>
                                                        <div style="display:none;" id="@{field:name}_copyright_id"><te:string id="Zdroj:&amp;nbsp;&amp;nbsp;" /> <input type="text" name="@{field:name}_copyright" /></div>
                                                        <te:if test="is_array(@{field:value}) &amp;&amp; count(@{field:value})" >
                                                            <br />
                                                            <te:for each="@{field:value} as @{kfile}=>@{file}">
                                                                <div style="margin:3px;" id="@{field:name}_file_@{kfile}">
                                                                <a href="#" style="background-color:white; text-decoration:none;" onclick="if(confirm('@L{Naozaj chcete zmazať súbor?}')) { document.getElementById('@{field:name}_file_@{kfile}').style.display='none'; document.getElementById('@{field:name}_delete_@{kfile}').value=@{file:file_id}; } return(false);">X</a>
                                                                    <?php echo basename(@{file:src}); ?>
                                                                    <te:if test="!empty(@{file:copyright})">
                                                                        <te:string id="Zdroj" />: @{file:copyright}
                                                                    </te:if>
                                                                </div>
                                                            </te:for>
                                                        </te:if>
                                                    </te:when>

                                                    <te:when case="itemlist">
                                                        <te:if test="!is_array(reset(@{field:options}))">
                                                            <select name="@{field:name}[]" id="@{field:name}" te:attrs="@{input_attrs}" multiple="multiple" size="10" style="width:250px;">
                                                                <te:for each="@{field:options} as @{option_key}=>@{option_value}">
                                                                    <te:if test="array_search(@{option_key}, @{field:multi_value}) !== false">
                                                                        <option value="@{option_key}" selected="selected">@{option_value}</option>
                                                                    <te:else />
                                                                        <option value="@{option_key}">@{option_value}</option>
                                                                    </te:if>
                                                                </te:for>
                                                            </select>
                                                        <te:else />
                                                            <select name="@{field:name}[]" id="@{field:name}" te:attrs="@{input_attrs}" multiple="multiple" size="10" style="width:250px;">
                                                                <option></option>
                                                                <te:for each="@{field:options} as @{optiongroup_key}=>@{optiongroup_value}">
                                                                    <optgroup label="@{optiongroup_value:optgroup}">
                                                                    <te:for each="@{optiongroup_value} as @{option_key}=>@{option_value}">
                                                                        <te:if test="@{option_key}!='optgroup'">
                                                                            <te:if test="array_search(@{option_key}, @{field:multi_value}) !== false">
                                                                                <option value="@{option_key}" selected="selected">@{option_value}</option>
                                                                            <te:else />
                                                                                <option value="@{option_key}">@{option_value}</option>
                                                                            </te:if>
                                                                        </te:if>
                                                                    </te:for>
                                                                    </optgroup>
                                                                </te:for>
                                                            </select>
                                                        </te:if>
                                                        <br /><te:string id="Viacero možností môžete označiť pridržaním klávesy CTRL alebo SHIFT." />
                                                    </te:when>

                                                </te:choose>
                                            <te:else />
                                                <te:choose test="@{field:type}">
                                                    <te:when case="varchar">
                                                        @{field:value}
                                                    </te:when>
                                                    <te:when case="password">
                                                    </te:when>
                                                    <te:when case="email">
                                                        @{field:value}
                                                    </te:when>
                                                    <te:when case="int">
                                                        @{field:value}
                                                    </te:when>
                                                    <te:when case="checkbox">
                                                        @{field:value}
                                                    </te:when>
                                                    <te:when case="float">
                                                        @{field:value}
                                                    </te:when>
                                                    <te:when case="date">
                                                        @{field:value}
                                                    </te:when>
                                                    <te:when case="datetime">
                                                        @{field:value}
                                                    </te:when>
                                                    <te:when case="textarea">
                                                        @{field:value}
                                                    </te:when>
                                                    <te:when case="text">
                                                        @{field:value}
                                                    </te:when>

                                                    <te:when case="enum">
                                                        @{field:value}
                                                    </te:when>

                                                    <te:when case="set">
                                                        @{field:value}
                                                    </te:when>

                                                    <te:when case="intInterval">
                                                        @{field:value}
                                                    </te:when>

                                                    <te:when case="floatInterval">
                                                        @{field:value}
                                                    </te:when>

                                                    <te:when case="imagelist">
                                                        <te:if test="is_array(@{field:value}) &amp;&amp; count(@{field:value})" >
                                                            <br />
                                                            <te:for each="@{field:value} as @{kimg}=>@{img}">
                                                                <div style="float:left; margin:3px; position:relative;" id="@{field:name}_img_@{kimg}">
                                                                    <te:if test="!empty(@{img:copyright})">
                                                                        <a href="@{img:0:src}" target="_blank"><img src="@{img:th1_:src}" style="width:50px;" title="Zdroj: @{img:copyright}" /></a>
                                                                    <te:else />
                                                                        <a href="@{img:0:src}" target="_blank"><img src="@{img:th1_:src}" style="width:50px;" /></a>
                                                                    </te:if>
                                                                </div>
                                                            </te:for>
                                                        </te:if>
                                                    </te:when>

                                                    <te:when case="filelist">
                                                        @{field:value}
                                                    </te:when>


                                                    <te:when case="ximagelist">
                                                        <te:if test="is_array(@{field:value}) &amp;&amp; count(@{field:value})" >
                                                            <br />
                                                            <te:for each="@{field:value} as @{kimg}=>@{img}">
                                                                <div style="float:left; margin:3px; position:relative;" id="@{field:name}_img_@{kimg}">
                                                                    <te:if test="!empty(@{img:copyright})">
                                                                        <a href="@{img:0:src}" target="_blank"><img src="@{img:th1_:src}" style="width:50px;" title="@L{Zdroj}: @{img:copyright}" /></a>
                                                                    <te:else />
                                                                        <a href="@{img:0:src}" target="_blank"><img src="@{img:th1_:src}" style="width:50px;" /></a>
                                                                    </te:if>
                                                                </div>
                                                            </te:for>
                                                        </te:if>
                                                    </te:when>

                                                    <te:when case="xfilelist">
                                                        @{field:value}
                                                    </te:when>

                                                    <te:when case="itemlist">
                                                        @{field:value}
                                                    </te:when>
                                                </te:choose>

                                            </te:if>

                                        </td>
                                    </tr>
                                    <?php $n++; ?>
                                </te:for>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
			</fieldset>
		</te:if>
</truEngine-document>
