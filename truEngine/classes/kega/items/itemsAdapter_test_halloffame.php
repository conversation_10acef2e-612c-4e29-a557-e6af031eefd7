<?php


class itemsAdapter_test_halloffame extends itemsAdapter_gClass
{
    function getItemsFields_gFunc()
    {
        $fields_pVar = array(
            array('tag' => 'user_id',
                'type' => 'user_id',
                'sk_name' => 'Používateľ'),
            array('tag' => 'join_date',
                'type' => 'datetime',
                'sk_name' => 'Dátum, čas'),
            array('tag' => 'score',
                'type' => 'int',
                'sk_name' => 'Počet bodov')

        );
        return ($fields_pVar);
    }

    function getEnumFieldsValues_gFunc($where_str_pVar = '', $where_data_pVar = array(), $columns_pVar = '*', $index_by_pVar = false)
    {
        return (array());
    }

    function getItems_checkRights_gFunc($filter_pVar)
    {
        return (session_gClass::userHasRightsAccess_gFunc(s_test_hall_of_fame));
    }

    protected function _pagerData_gFunc($where_str_pVar, $where_data_pVar, $order_by_pVar, $offset_pVar, $pageLen_pVar)
    {
        return (array());
    }

    protected function _nonPagerData_gFunc($where_str_pVar, $where_data_pVar, $order_by_pVar)
    {
        $sql_pVar = 'SELECT * FROM `%ttest_hall_of_fame` as `D` ';

        $sql_pVar = '
						SELECT
								`D`.*,
								CONCAT_WS(\' \', `u1`.`first_name`, `u1`.`last_name`) as `user`,
								`u1`.`last_name` as `user_last_name`
							FROM `%ttest_hall_of_fame` as `D`
							LEFT JOIN `%titems_users__data` as `u1` ON `D`.`user_id` = `u1`.`item_id`
		';

        $where_str_pVar .= ' AND `D`.`status` = \'active\'';
        $sql_pVar .= $where_str_pVar;

        $sql_pVar .= ' ORDER BY `score`/`total_score` DESC, `join_date` DESC
					   LIMIT 0, 10';

        $data_pVar = db_public_gClass::getResultArray_gFunc($sql_pVar, __FILE__, __LINE__, $where_data_pVar);

        $itemIds_pVar = array();
        foreach ($data_pVar as $v_pVar) {
            if ($v_pVar['user_id']) {
                $itemIds_pVar[] = $v_pVar['user_id'];
            }
        }
        $filesTmp_pVar = main_gClass::getFiles_gFunc('items_users_foto', $itemIds_pVar, false);

        if (count($filesTmp_pVar)) {
            foreach ($data_pVar as $k_pVar => $v_pVar) {
                if (isset($filesTmp_pVar['items_users_foto_' . $v_pVar['user_id']])) {
                    $data_pVar[$k_pVar]['user_foto'] = $filesTmp_pVar['items_users_foto_' . $v_pVar['user_id']];
                    unset($itemIds_pVar[array_search($v_pVar['user_id'], $itemIds_pVar)]);
                }
            }
        }
        if (count($itemIds_pVar)) {
            $filesTmp_pVar = main_gClass::getFiles_gFunc('items_users_foto_rec', $itemIds_pVar, false);
            if (count($filesTmp_pVar)) {
                foreach ($data_pVar as $k_pVar => $v_pVar) {
                    if (isset($filesTmp_pVar['items_users_foto_rec_' . $v_pVar['user_id']])) {
                        $data_pVar[$k_pVar]['user_foto'] = $filesTmp_pVar['items_users_foto_rec_' . $v_pVar['user_id']];
                        unset($itemIds_pVar[array_search($v_pVar['user_id'], $itemIds_pVar)]);
                    }
                }
            }
        }

        $web_dir_pVar = main_gClass::getConfigVar_gFunc('web_dir', 'runtime');
        if (substr($web_dir_pVar, -1) == '/') {
            $web_dir_pVar = substr($web_dir_pVar, 0, -1);
        }

        foreach ($data_pVar as $k_pVar => $v_pVar) {
            if ($v_pVar['user_id'] && session_gClass::userHasRightsInfo_gFunc(s_users_edit_user)) {
                $data_pVar[$k_pVar]['user'] = '<a href="' . main_gClass::makeUrl_gFunc('/pouzivatelia/editovat-pouzivatela?item_id=' . $v_pVar['user_id']) . '">' . $data_pVar[$k_pVar]['user'] . '</a>';
            }

            if (isset($v_pVar['user_foto'])) {
                $data_pVar[$k_pVar]['user_foto'] = '<img src="' . $web_dir_pVar . $v_pVar['user_foto']['th1_']['src'] . '" />';
            } else {
                $data_pVar[$k_pVar]['user_foto'] = '<img src="/sk/images/icons/nofoto.jpg" />';
            }

            if (!empty($data_pVar[$k_pVar]['user'])) {
                $data_pVar[$k_pVar]['user'] .= '<br />' . $data_pVar[$k_pVar]['user_foto'];
            }

            if (!empty($data_pVar[$k_pVar]['total_score'])) {
                $data_pVar[$k_pVar]['score'] = sprintf('%0.2f%%', ($data_pVar[$k_pVar]['score'] / $data_pVar[$k_pVar]['total_score']) * 100)
                    . '<span style="color:#666;"> (' . $data_pVar[$k_pVar]['score'] . ' / ' . $data_pVar[$k_pVar]['total_score'] . ')</span>';
                $data_pVar[$k_pVar]['score'] = str_replace('.', ',', $data_pVar[$k_pVar]['score']);
            }
        }


        foreach ($data_pVar as $k => $v) {
            if (!empty($v['join_date'])) {
                $data_pVar[$k]['join_date'] = timeFormat_gClass::time_gFunc(strtotime($v['join_date']));
            }
        }

        return ($data_pVar);
    }

    protected function _getCountData_gFunc($where_str_pVar, $where_data_pVar)
    {
        return (0);
    }

    protected function _getOrderByFields_gFunc()
    {
        return (array());
    }
}

