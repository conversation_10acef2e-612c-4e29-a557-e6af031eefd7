@props([
    'type' => 'select',
    'class' => 'form-control',
    'value' => null,
    'name' => null,
    'options' => [],
])



<select
    name="{{ $name }}"
    id="{{ $name }}"
    class="form-control js--select2-select"
    x-model="@xField($name).value"
    {{ $attributes }}
>
    <template x-for="(value, key) in @xField($name).options">
        <option x-bind:value="key" x-text="value" x-bind:selected="key == @xField($name).value"></option>
    </template>
</select>
