<?php
namespace Mixedtype\Layouts;
use Illuminate\Support\Facades\Blade;

class LayoutsServiceProvider extends \Illuminate\Support\ServiceProvider
{
    public function boot()
    {
        // register views in package namespace
        $this->loadViewsFrom(__DIR__.'/../resources/views', 'mixedtype-layouts');

        // register views in project namespace
        \Illuminate\Support\Facades\View::addLocation(__DIR__.'/../resources/views');

        Blade::component(LayoutComponent::class, 'layout');
    }

    public function register()
    {
        $this->mergeConfigFrom(
            __DIR__.'/../config/layouts.php', 'mixedtype.layouts'
        );
    }
}
