<?xml version="1.0" encoding="utf-8" ?>
<truEngine-document xmlns:te="http://www.truengine.sk">

	<te:source name="session" varname="session" />

	<?php
		if(!isset(@{_login_follow})) {
			@{_login_follow} = '/sk/log-in/oficialne-testy';
		}
	?>

	<te:include name="/sk/log-in/_default" _login_form_disabled="1" _login_msg="@{_login_msg}" />

	<te:xaccess xaccess="s_logged_on">
	  <!--x formular zobrazim iba ak nie je disabled -->
	  <div te:if="empty(@{_login_form_disabled})" class="msg_info">
	  	@L{Vyberte si jednu z možností prihlásenia sa do oficiálnych testov:}
	  	<ol>
	  		<li>@L{<strong>pomocou prihlasovacieho mena a hesla</strong> - použite svoje prihlasovacie meno a heslo, ktorým sa prihlasujete do systému. Ak môž<PERSON>, použite túto možnosť.}</li>
	  		<li>@L{<strong>pomocou čísla ISIC/ITIC preukazu</strong> - ak nemáte prístup do systému cez prihlasovacie meno, alebo si nepamätáte heslo, prihláste sa pomocou sériového čísla Vášho ISIC preukazu. Vyplnte aj meno a priezvisko, aby Vás bolo možné správne identifikovať v prípade, že sa nepodarí identifikovať Váš preukaz.}<br />
	  		@L{Ak nevlastníte ISIC/ITIC preukaz, pole ISIC/ITIC SNR nechajte prázdne.}</li>
	  	</ol>
	  </div>

      <form action="@{_login_follow}" method="POST" te:if="empty(@{_login_form_disabled})" id="form_login" onsubmit="return(validateAndSubmit(loginFormItems));">
      	<script type="text/javascript">
      		var loginFormItems = new Array();
      		loginFormItems[0] = new Array();
//      		loginFormItems[0]['err_bgcolor'] = '#FF0000';
//      		loginFormItems[0]['err_color'] = '#aa0000';
      		loginFormItems[1] = new Array();
      		loginFormItems[1]['id'] = 'form_login_login';
      		loginFormItems[1]['match'] = /^[a-z0-9\.]+(@[a-z0-9\.]+)?$/i;
      		loginFormItems[1]['message'] = '@L{Musíte zadať prihlasovacie meno.\nMôže obsahovať malé a veľké písmená, číslice, bodku, alebo to môže byť e-mailová adresa.}';
      		loginFormItems[2] = new Array();
      		loginFormItems[2]['id'] = 'form_login_passwd';
      		loginFormItems[2]['match'] = /^.{@{_login_pass_min_len},200}$/i;
      		loginFormItems[2]['message'] = 'Musíte zadať heslo.\nMinimálne @{_login_pass_min_len} znakov.';
      		loginFormItems[3] = new Array();
      		loginFormItems[3]['id'] = 'form_login_submit';
      		loginFormItems[3]['disable'] = true;
      		loginFormItems[3]['value'] = 'Prihlasujem...';
      	</script>
      	<fieldset>
      	  <legend>@L{Prihlásenie pomocou prihlasovacieho mena a hesla}</legend>
          <table class="form_table">
            <tbody>
	            <tr>
	              <td class="td_label"><label for="form_login_login">@L{Prihlasovacie meno}:</label></td>
	              <td><input name="login_kega" id="form_login_login" type="text" class="input_text" /><img src="/images/icons/info_small.png" class="info_icon tipz" title="@L{Zadajte prihlasovacie meno.::Môže obsahovať malé a veľké písmená, číslice, bodku,&lt;br /&gt;alebo to môže byť e-mailová adresa.}" /></td>
	            </tr>
	            <tr>
	              <td class="td_label"><label for="form_login_passwd">@L{Heslo}:</label></td>
	              <td><input name="password" id="form_login_passwd" type="password" class="input_text" /><img src="/images/icons/info_small.png" class="info_icon tipz" title="Zadajte heslo.::Minimálne @{_login_pass_min_len} znaky." /></td>
	            </tr>
	            <tr>
	              <td></td>
	              <td class="td_submit">
	              	<div>
	                	<input type="submit" value="@L{Prihlásiť}" id="form_login_submit" class="input_submit" />
	                </div>
	              </td>
	            </tr>
          </tbody></table>
        </fieldset>
      </form>

      <form action="@{_login_follow}" method="POST" te:if="empty(@{_login_form_disabled})" id="form_login" onsubmit="return(validateIsicForm());">

      	<fieldset>
      		<legend>@L{Prihlásenie pomocou čísla ISIC/ITIC preukazu}</legend>
			<te:if test="isset(@{_login_timeouted}) &amp;&amp; @{_login_timeouted}">
			</te:if>

          <table class="form_table">
            <tbody>
	            <tr>
	              <td class="td_label"><label for="form_login_isic">@L{ISIC/ITIC SNR}:</label></td>
	              <td><input name="login_isic" id="form_login_isic" type="text" class="input_text" /><img src="/images/icons/info_small.png" class="info_icon tipz" title="@L{::Zadajte sériové číslo Vášho ISIC preukazu.}" /></td>
	            </tr>
	            <tr>
	              <td class="td_label"><label for="form_login_last_name">@L{Priezvisko}:</label></td>
	              <td><input name="login_last_name" id="form_login_last_name" type="text" class="input_text" /></td>
	            </tr>
	            <tr>
	              <td class="td_label"><label for="form_login_first_name">@L{Meno}:</label></td>
	              <td><input name="login_first_name" id="form_login_first_name" type="text" class="input_text" /></td>
	            </tr>
	            <tr>
	              <td></td>
	              <td class="td_submit">
	              	<div>
	                	<input type="submit" value="@L{Prihlásiť}" id="form_login_submit" class="input_submit" />
	                </div>
	              </td>
	            </tr>
          </tbody></table>
        </fieldset>
      </form>

	</te:xaccess>
	<script type="text/javascript">
		function validateIsicForm()
		{
			var isic = $j('#form_login_isic').val();
			var last = $j('#form_login_last_name').val();
			var first = $j('#form_login_first_name').val();

			if(!isic.match(/[0-9]{10}/)) {
				alert('@L{Musíte zadať desaťmiestne sériové číslo Vašej ISIC/ITIC kartičky. Číslo zadávajte bez medzier.}');
				$j('#form_login_isic').focus();
				return(false);
			}
			if(!last.match(/^[A-Z][A-Za-z ]+/)) {
				alert('@L{Musíte zadať priezvisko. Prvé písmeno musí byť veľkým písmenom.}');
				$j('#form_login_last_name').focus();
				return(false);
			}
			if(!first.match(/^[A-Z][A-Za-z ]+/)) {
				alert('@L{Musíte zadať krstné meno. Prvé písmeno musí byť veľkým písmenom.}');
				$j('#form_login_first_name').focus();
				return(false);
			}

			return(true);
		}
	</script>

	<te:content-set name="title" value="@L{Oficiálne testy - Prihlásiť sa}" />

</truEngine-document>
