---
title: Getting Help
metadata:
    description: How to get support, report a bug, or suggest a feature for Select2.
taxonomy:
    category: docs
---

## General support

Having trouble getting Select2 working on your website? Is it not working together with another plugin, even though you think it should? Select2 has a few communities that you can go to for help getting it all working together.

1. Join our [forums](https://forums.select2.org), graciously hosted by [NextGI](https://nextgi.com) and start a new topic.
2. Search [Stack Overflow](http://stackoverflow.com/questions/tagged/jquery-select2?sort=votes)  **carefully** for existing questions that might address your issue. If you need to open a new question, make sure to include the `jquery-select2` tag.
3. Ask in the `#select2` channel on `chat.freenode.net` or use the [web irc client.](https://webchat.freenode.net/?channels=select2) 

>>>> Do **NOT** use the GitHub issue tracker for general support and troubleshooting questions.  The issue tracker is **only** for bug reports with a [minimal, complete, and verifiable example](https://stackoverflow.com/help/mcve) and feature requests.  Use the forums instead.

## Reporting bugs

Found a problem with Select2? Feel free to open a ticket on the Select2 repository on GitHub, but you should keep a few things in mind:

1. Use the [GitHub issue search](https://github.com/select2/select2/search?q=&type=Issues) to check if your issue has already been reported.
2. Try to isolate your problem as much as possible.  Use [JS Bin](http://jsbin.com/goqagokoye/edit?html,js,output) to create a [minimal, verifiable, and complete](https://stackoverflow.com/help/mcve) example of the problem.
3. Once you are sure the issue is with Select2, and not a third party library, [open an issue](https://github.com/select2/select2/issues/new) with a description of the bug, and link to your jsbin example.

You can find more information on reporting bugs in the [contributing guide,](https://github.com/select2/select2/blob/master/CONTRIBUTING.md#reporting-bugs-with-select2) including tips on what information to include.

>>>>> If you are not conversationally proficient in English, do **NOT** post a machine translation (e.g. Google Translate) to GitHub. Get help in crafting your question, either via the [forums](https://forums.select2.org) or in [chat](https://webchat.freenode.net/?channels=select2).  If all else fails, you may post your bug report or feature request in your native language and we will tag it with `translation-needed` so that it can be properly translated.

## Requesting new features

New feature requests are usually requested by the [Select2 community on GitHub,](https://github.com/select2/select2/issues) and are often fulfilled by [fellow contributors.](https://github.com/select2/select2/blob/master/CONTRIBUTING.md)

1.  Use the [GitHub issue search](https://github.com/select2/select2/search?q=&type=Issues) to check if your feature has already been requested.
2.  Check if it hasn't already been implemented as a [third party plugin.](https://github.com/search?q=topic%3Aselect2&type=Repositories)
3.  Please make sure you are only requesting a single feature, and not a collection of smaller features.

You can find more information on requesting new features in the [contributing guide.](https://github.com/select2/select2/blob/master/CONTRIBUTING.md#requesting-features-in-select2)
