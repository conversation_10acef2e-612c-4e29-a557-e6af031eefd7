<?php

class db_session_gClass extends db_gClass {
    static private $cache_online_users = array();

    static public function getUserDataByEmail_gFunc($email_pVar, $cacheEnabled_pVar = true)
    {
        $cacheName_pVar = '_access2_session_userdataByEmail_pVar' . $email_pVar;
        if($cacheEnabled_pVar) {
            $ret_pVar = self::getCachedResult_gFunc($cacheName_pVar);
        }

        if($cacheEnabled_pVar && $ret_pVar !== false) {
            return($ret_pVar);
        }
        else {
            $query_string_pVar = 'SELECT `u`.* FROM `%titems_users__data` as `u` WHERE `u`.`email` = %s AND `u`.`status` <> \'deleted\'';
            $data_pVar = self::getResultArray_gFunc($query_string_pVar, __FILE__, __LINE__, $email_pVar);
            if(!is_array($data_pVar) || !count($data_pVar)) {
                return(false);
            }

            if($cacheEnabled_pVar === true) {
                self::cacheResult_gFunc($cacheName_pVar, $data_pVar);
            }
            return($data_pVar);
        }
    }

    static public function getUserDataByLogin_gFunc($loginName_pVar, $cacheEnabled_pVar = true)
    {

        $cacheName_pVar = '_access2_session_userdataByLogin_pVar' . $loginName_pVar;
        if($cacheEnabled_pVar) {
            $ret_pVar = self::getCachedResult_gFunc($cacheName_pVar);
        }

        if($cacheEnabled_pVar && $ret_pVar !== false) {
            return($ret_pVar);
        }
        else {
            // `u`.*,`us`.*,`u`.`user_id` musi byt takto, aby mi neprpisalo user_id
            $query_string_pVar = 'SELECT `u`.* FROM `%titems_users__data` as `u` WHERE `u`.`login` = %s AND `u`.`status` <> \'deleted\'';
            $data_pVar = self::getResult_gFunc($query_string_pVar, __FILE__, __LINE__, $loginName_pVar);
            if(!is_array($data_pVar)) {
                return(false);
            }
            $data_pVar['user_id'] = $data_pVar['item_id'];

            if($cacheEnabled_pVar === true) {
                self::cacheResult_gFunc($cacheName_pVar, $data_pVar);
            }
            return($data_pVar);
        }
    }

    static public function getUserIdByLogin_gFunc($loginName_pVar, $cacheEnabled_pVar = true)
    {

        $cacheName_pVar = '_access2_session_useridByLogin_pVar' . $loginName_pVar;
        if($cacheEnabled_pVar) {
            $ret_pVar = self::getCachedResult_gFunc($cacheName_pVar);
        }

        if($cacheEnabled_pVar && $ret_pVar !== false) {
            return($ret_pVar);
        }
        else {
            $query_string_pVar = 'SELECT `u`.`item_id` FROM `%titems_users__data` as `u` WHERE `u`.`login` = %s';
            $data_pVar = self::getResult_gFunc($query_string_pVar, __FILE__, __LINE__, $loginName_pVar);
            if(!is_array($data_pVar)) {
                return(false);
            }
            $data_pVar = $data_pVar['item_id'];

            if($cacheEnabled_pVar === true) {
                self::cacheResult_gFunc($cacheName_pVar, $data_pVar);
            }
            return($data_pVar);
        }
    }

    static public function getUserDataByID_gFunc($userId_pVar, $cacheEnabled_pVar = true)
    {

        $cacheName_pVar =  '_access2_session_userdataByID_pVar' . $userId_pVar;
        if($cacheEnabled_pVar) {
            $ret_pVar = self::getCachedResult_gFunc($cacheName_pVar);
        }

        if($cacheEnabled_pVar && $ret_pVar !== false) {
            return($ret_pVar);
        }
        else {
            $query_string_pVar = 'SELECT * FROM `%titems_users__data` as `u` WHERE `u`.`item_id` = %d AND `u`.`status` <> \'deleted\'';
            $data_pVar = self::getResult_gFunc($query_string_pVar, __FILE__, __LINE__, $userId_pVar);

            if($cacheEnabled_pVar === true) {
                self::cacheResult_gFunc($cacheName_pVar, $data_pVar);
            }
            return($data_pVar);
        }
    }

    static public function getUserDataBySessionId_gFunc($sesionIdString_pVar, $cacheEnabled_pVar = false, $userId_pVar = false)
    {
        if($cacheEnabled_pVar) {
            $ret_pVar = self::getCachedResult_gFunc('_access2_session_onlineuserdata_pVar');
        }

        if($cacheEnabled_pVar && $ret_pVar !== false) {
            return($ret_pVar);
        }
        else {
            $query_string_pVar = 'SELECT now() AS `now`, `u`.*, `ou`.* FROM `%tusers_onlineusers` as `ou` LEFT JOIN `%titems_users__data` as `u` ON `u`.`item_id` = `ou`.`user_id` WHERE `ou`.`session_str_id` = %s ' .($userId_pVar !== false ? (' AND `ou`.`user_id` = %d'):'');
            $data_pVar = self::getResult_gFunc($query_string_pVar, __FILE__, __LINE__, array($sesionIdString_pVar, $userId_pVar));
            if($cacheEnabled_pVar === true) {
                self::cacheResult_gFunc('_access2_session_onlineuserdata_pVar', $data_pVar);
            }
            return($data_pVar);
        }
    }

    static public function restoreSession_gFunc($userId_pVar, $online_user_id_pVar, $cacheEnabled_pVar = true)
    {
        return(self::logLastAccess_gFunc($userId_pVar, $online_user_id_pVar, $cacheEnabled_pVar, true));
    }

    static public function logLastAccess_gFunc($userId_pVar, $online_user_id_pVar, $cacheEnabled_pVar = true, $restore_pVar = false)
    {
        if($cacheEnabled_pVar) {
            if($restore_pVar) {
                $ret_pVar = self::getCachedResult_gFunc('_access2_session_lastaccess_restore_pVar');
            }
            else {
                $ret_pVar = self::getCachedResult_gFunc('_access2_session_lastaccess_pVar');
            }
        }

        if($restore_pVar) {
            $session_status_pVar = 'restoring';
        }
        else {
            $session_status_pVar = 'online';
        }
        $queryString_pVar = 'UPDATE `%tusers_onlineusers` SET `time_last_access` = now(), `time_next_timeout`= now() + INTERVAL `timeout` MINUTE WHERE `online_user_id`= %d AND `session_status` = %s';
        $n_pVar = self::execute_gFunc($queryString_pVar, __FILE__, __LINE__, array($online_user_id_pVar, $session_status_pVar), true);
        if($n_pVar) {
            $queryString_pVar = 'UPDATE `%titems_users__data` SET `time_last_access` = now(), `last_remote_ip` = %s, `time_update`=`time_update` WHERE `item_id`= %d';
            self::execute_gFunc($queryString_pVar, __FILE__, __LINE__, array(main_gClass::getServerVar_gFunc('REMOTE_ADDR'), $userId_pVar));
        }

        if($cacheEnabled_pVar === true) {
            $ret_pVar = true;
            if($restore_pVar) {
                self::cacheResult_gFunc('_access2_session_lastaccess_restore_pVar', $ret_pVar);
            }
            else {
                self::cacheResult_gFunc('_access2_session_lastaccess_pVar', $ret_pVar);
            }
        }
        return($ret_pVar);
    }

    /**
     * Funkcia vytvorisession v onlineusers (prihlasi pouzivatela), a vrati data tejto session zavolanim
     * metody getUserDataBySessionId_gFunc (aby to bolo univerzalne). Efektivnost az tak netreba, leboprihlasovanie
     * pouzivatela nie je tak frekventovane.
     *
     * @param unknown_type $userId_pVar
     * @param unknown_type $sessionId_pVar
     */
    static public function createSessionOnlineusers_gFunc($userId_pVar, $sessionId_pVar, $currentKey_pVar, $cacheEnabled_pVar = true)
    {
        return(self::createSessionOnlineusersRights_gFunc($userId_pVar, $sessionId_pVar, 'login', '', $currentKey_pVar, $cacheEnabled_pVar));
    }

    static public function createSessionOnlineusersRights_gFunc($userId_pVar, $sessionId_pVar, $accessType_pVar, $rights_pVar = '', $currentKey_pVar = null, $cacheEnabled_pVar = true)
    {
        $query_string_pVar = 'INSERT INTO `%tusers_onlineusers` '
            . '(`session_str_id`, `session_status`, `time_start`, `time_last_access`, `time_next_timeout`, `timeout`, '
            . '`user_id`, `pc_str_id`, `remote_ip`, `user_agent`, '
            . '`rights`, `current_key`) '
            . 'SELECT '
            . ' %s, \'online\', now(), now(), now()+interval `u`.`timeout` minute, `u`.`timeout`, '
            . '%d, %s, %s, %s, '
            . '%s, %xs'
            . ' FROM `%titems_users__data` as `u` WHERE `u`.`item_id` = %d AND `u`.`status` = \'active\'';

        $remote_addr = main_gClass::getServerVar_gFunc('REMOTE_ADDR', false, '/\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}/');
        if($remote_addr === null) {
            $remote_addr = 'unknown';
        }

        $online_user_id_pVar = self::insert_gFunc($query_string_pVar, __FILE__,__LINE__,
            array(
                $sessionId_pVar,
                $userId_pVar,
                session_session_gClass::getPcId_gFunc(), $remote_addr, main_gClass::getServerVar_gFunc('HTTP_USER_AGENT'), $rights_pVar, $currentKey_pVar, $userId_pVar), true);

        $query_string_pVar = 'INSERT INTO `%tusers_accesslog` (`user_id`, `access_time`, `access_type`) VALUES(%d, now(), %s)';
        self::execute_gFunc($query_string_pVar, __FILE__,__LINE__, array($userId_pVar, $accessType_pVar));

        $query_string_pVar = 'UPDATE `%titems_users__data` SET `time_last_login` = now() WHERE `item_id` = %d';
        self::execute_gFunc($query_string_pVar, __FILE__,__LINE__, array($userId_pVar));

        db_session_gClass::logLastAccess_gFunc($userId_pVar, $online_user_id_pVar);

        return(self::getUserDataBySessionId_gFunc($sessionId_pVar, $cacheEnabled_pVar, $userId_pVar));
    }

    static function logoutUser_gFunc($userId_pVar, $onlineUserId_pVar)
    {
        $query_string_pVar = 'INSERT INTO `%tusers_accesslog` (`user_id`, `access_time`, `access_type`) VALUES(%d, now(), \'logout\')';
        self::execute_gFunc($query_string_pVar, __FILE__,__LINE__, array($userId_pVar));
        $query_string_pVar = 'DELETE FROM `%tusers_onlineusers` WHERE `online_user_id` = %d';
        self::execute_gFunc($query_string_pVar, __FILE__,__LINE__, array($onlineUserId_pVar));

        // logout laravel user, if is logged on
        if (auth()->check()) {
            auth()->logout();
        }

    }

    /**
     * Oznaci timeoutovane sessiony.
     * Cache je tu len preto, aby sa prikaz pouzil max. 1x za request.
     */
    public static function setTimeoutedSessions_gFunc()
    {
        $ret_pVar = self::getCachedResult_gFunc('_access2_session_setTimeouted_pVar');
        if($ret_pVar !== false) {
            return($ret_pVar);
        }
        else {
            $query_string_pVar = 'UPDATE `%tusers_onlineusers` SET `session_status`=\'timeout\' WHERE `session_status`=\'online\' AND `time_next_timeout`<now()';
            $n_pVar = self::execute_gFunc($query_string_pVar, __FILE__, __LINE__, false, true);
            if($n_pVar === false) {
                return(false);
            }
            if($n_pVar) { // nejake su timeoutovane
                // zapisem do logu
                $query_string_pVar = 'INSERT INTO `%tusers_accesslog` (`user_id`, `access_time`, `access_type`) SELECT `user_id`, `time_next_timeout`, \'timeout\' FROM `%tusers_onlineusers` WHERE `session_status`=\'timeout\'';
                if(!self::execute_gFunc($query_string_pVar, __FILE__, __LINE__)) {
                    return(false);
                }

                // dokoncim operaciu
                $query_string_pVar = 'UPDATE `%tusers_onlineusers` SET `time_next_timeout`=DATE_ADD(now(), INTERVAL (`timeout`/2) minute), `session_status`=\'timeouted\' WHERE `session_status` = \'timeout\'';
                if(!self::execute_gFunc($query_string_pVar, __FILE__, __LINE__)) {
                    return(false);
                }
            }

            self::cacheResult_gFunc('_access2_session_setTimeouted_pVar', $n_pVar);
            return($n_pVar);
        }
    }

    public static function destroySession_gFunc($online_user_id)
    {
        $query_string_pVar = 'DELETE FROM `%tusers_onlineusers` WHERE `online_user_id`=%d';
        if(!self::execute_gFunc($query_string_pVar, __FILE__, __LINE__, array($online_user_id))) {
            return(false);
        }
        return(true);
    }

    /**
     * Zrusi timeoutovane sessiony po uplynuti ich limitu.
     * Cache je tu len preto, aby sa prikaz pouzil max. 1x za request.
     */
    public static function destroyTimeoutedSessions_gFunc()
    {
        $ret_pVar = self::getCachedResult_gFunc('_access2_session_destroyTimeouted_pVar');
        if($ret_pVar !== false) {
            return($ret_pVar);
        }
        else {
            // odstranim sessiony
            $query_string_pVar = 'DELETE FROM `%tusers_onlineusers` WHERE (`session_status`=\'timeouted\' OR `session_status`=\'restoring\') AND `time_next_timeout`<now()';
            if(!self::execute_gFunc($query_string_pVar, __FILE__, __LINE__)) {
                return(false);
            }

            // session request data su ulozene v session, takze ich nemusim mazat, zmazu sa spolu so session
            //...

            $ret_pVar = true;
            self::cacheResult_gFunc('_access2_session_destroyTimeouted_pVar', $ret_pVar);
            return($ret_pVar);
        }
    }

    public static function comparePasswords($login, $password, $hashed)
    {
        if(strlen($hashed) > 42) {
            if (\Illuminate\Support\Facades\Hash::check($password, $hashed)) {
                return true;
            }
        }

        if(
            db_session_gClass::encodePassword_gFunc(
                $login,
                $password
            ) === $hashed) {
            return true;
        }

        return false;
    }

    public static function encodePassword_gFunc($loginName_pVar, $password_pVar)
    {
        return \Illuminate\Support\Facades\Hash::make($password_pVar);
    }

    public static function legacyEncodePassword_gFunc($loginName_pVar, $password_pVar)
    {
        $loginName_pVar = trim(strtolower($loginName_pVar));
        return(sha1($loginName_pVar . $password_pVar));
    }


    /**
     * Nastavi timeout pre tuto session.
     *
     * Ak mode == 0, tak nastavi hodnotu na $mins
     * Ak mode == 1, tak nastavi hodnotu na $mins iba aj je aktualna hodnota mensia
     * Ak mode == 2, tak nastavi hodnotu na $mins iba aj je aktualna hodnota vacsia (teda znizi timeout)
     * @param $mins_pVar
     * @param $mode_pVar
     * @return unknown_type
     */
    static public function setSessionTimeout_gFunc($mins_pVar, $mode_pVar = 0)
    {
        $params_pVar = array($mins_pVar, $mins_pVar, session_gClass::getUserDetail_gFunc('online_user_id'));
        $sql_pVar = 'UPDATE `%tusers_onlineusers` SET `timeout` = %d, `time_next_timeout` = now() + INTERVAL %d MINUTE
					WHERE `online_user_id` = %d';
        if($mode_pVar == 1) {
            $sql_pVar .= ' AND `timeout` < %d';
            $params_pVar[] = $mins_pVar;
        }
        if($mode_pVar == 2) {
            $sql_pVar .= ' AND `timeout` > %d';
            $params_pVar[] = $mins_pVar;
        }
        self::execute_gFunc($sql_pVar, __FILE__, __LINE__, $params_pVar);
    }

    /**
     * Tato funkcia nemusi byt cachovana, lebo je volana len pri prihlasovani, a vtedy aj tak zdrzujem.
     */
    public static function incrementDisableLevel_gFunc($userId_pVar, $maxLevel_pVar = 'disabled')
    {
        return(false);

        /****
         * neblokujem pouzivatelov pri zadani zleho hesla  - OK
         */


        $query_string_pVar = 'SELECT `disabled_login_level` as `level`, (UNIX_TIMESTAMP(`disabled_login`) - UNIX_TIMESTAMP(now())) as `timeout` FROM `%titems_users__data` WHERE `item_id`=%d';
        $result_pVar = self::getResult_gFunc($query_string_pVar, __FILE__, __LINE__, array($userId_pVar));
        if(!$result_pVar) {
            return(false);
        }
        $currentLevel_pVar = $result_pVar['level'];
        if(!strlen($currentLevel_pVar)) {
            $currentLevel_pVar = 'enabled';
        }
        $timeout_pVar = $result_pVar['timeout'];

        if($maxLevel_pVar === 'enabled') {
            return(false);
        }
        elseif($currentLevel_pVar === 'enabled' || $maxLevel_pVar === 'level1') {
            if($currentLevel_pVar === 'enabled') {
                $currentLevel_pVar = 'level1';
            }
            if($timeout_pVar < 15) {
                $timeout_pVar = 15; // to akurat pokryje sleep
            }
        }
        elseif ($currentLevel_pVar === 'level1' || $maxLevel_pVar === 'level2') {
            if($currentLevel_pVar === 'level1') {
                $currentLevel_pVar = 'level2';
            }
            if($timeout_pVar < 60*15) {
                $timeout_pVar = 60*15;
            }
        }
        elseif ($currentLevel_pVar === 'level2' || $maxLevel_pVar === 'level3') {
            if($currentLevel_pVar === 'level2') {
                $currentLevel_pVar = 'level3';
            }
            if($timeout_pVar < 60*30) {
                $timeout_pVar = 60*30;
            }
        }
        elseif ($currentLevel_pVar === 'level3' || $maxLevel_pVar === 'disabled') {
            if($currentLevel_pVar === 'level3') {
                $currentLevel_pVar = 'disabled';
            }
            $timeout_pVar = 0;
        }
        else {
            $currentLevel_pVar = 'disabled';
            $timeout_pVar = 0;
        }

        $query_string_pVar = 'UPDATE `%titems_users__data` SET `disabled_login_level` = %s, `disabled_login` = DATE_ADD(now(),INTERVAL %d second) WHERE `item_id` = %d';
        if(!self::execute_gFunc($query_string_pVar, __FILE__, __LINE__, array($currentLevel_pVar, $timeout_pVar, $userId_pVar))) {
            return(false);
        }
        else {
            return($timeout_pVar);
        }
    }

    public static function resetDisableLevel_gFunc($userId_pVar)
    {
        $query_string_pVar = 'UPDATE `%titems_users__data` SET `disabled_login_level` = \'enabled\', `disabled_login` = now() WHERE `item_id` = %d';
        if(!self::execute_gFunc($query_string_pVar, __FILE__, __LINE__, array($userId_pVar))) {
            return(false);
        }
        else {
            return(true);
        }
    }

    public static function setPassword_gFunc($userId_pVar, $userLogin_pVar, $newPassword_pVar)
    {
        $newPassword_pVar = self::encodePassword_gFunc($userLogin_pVar, $newPassword_pVar);
        $query_string_pVar = 'UPDATE `%titems_users__data` SET `password` = %s WHERE `item_id` = %d';
        if(!self::execute_gFunc($query_string_pVar, __FILE__, __LINE__, array($newPassword_pVar, $userId_pVar))) {
            return(false);
        }
        else {
            return($newPassword_pVar);
        }
    }

    public static function cacheUserRights_gFunc($onlineUserId_pVar, &$rights_pVar, $groups_pVar = array())
    {
        // rozdelim pole na zakazane a povolene
        $rights_enabled_pVar = array();
        $rights_disabled_pVar = array();

        foreach ($rights_pVar as $k_pVar=>$v_pVar) {
            if($v_pVar) {
                $rights_enabled_pVar[] = $k_pVar;
            }
            else {
                $rights_disabled_pVar[] = $k_pVar;
            }
        }

        $rightsStr_pVar = implode(',', $rights_enabled_pVar) . '#' . implode(',', $rights_disabled_pVar) . '#' . implode(',',$groups_pVar);

        $query_string_pVar = 'UPDATE `%tusers_onlineusers` SET `rights` = %s WHERE `online_user_id` = %d';
        if(!self::execute_gFunc($query_string_pVar, __FILE__, __LINE__, array($rightsStr_pVar, $onlineUserId_pVar))) {
            return(false);
        }
        else {
            return($rightsStr_pVar);
        }
    }

    public static function startTransaction_gFunc($fileName_pVar, $fileLine_pVar)
    {
        return(parent::startTransaction_gFunc($fileName_pVar, $fileLine_pVar));
    }

    public static function commit_gFunc($fileName_pVar, $fileLine_pVar)
    {
        return(parent::commit_gFunc($fileName_pVar, $fileLine_pVar));
    }

    public static function rollback_gFunc($fileName_pVar, $fileLine_pVar)
    {
        return(parent::rollback_gFunc($fileName_pVar, $fileLine_pVar));
    }

    public static function setsessionforrestoring_gfunc($online_user_id_pVar)
    {
        error_gClass::error_gFunc(__FILE__,__LINE__, 'not implemented');
        session_gClass::logout_gFunc();
        header('location: /timeout');
    }


    public static function getOnlineusers_gFunc($filter_pVar = array())
    {
        if(!session_gClass::userHasRightsAccessAction_gFunc(s_users_show_onlinelist)) {
            return(array());
        }

        if(isset($filter_pVar['pager'])) {
            $pager_pVar = explode(',', $filter_pVar['pager']);
            if(intval($pager_pVar[1])) {
                $pager_pVar[1] = intval($pager_pVar[1]) - 1;
            }
            unset($filter_pVar['pager']);
        }
        else {
            $pager_pVar = false;
        }

        if(is_array($pager_pVar) && count($pager_pVar) == 2 && intval($pager_pVar[0]) > 0) {
            $page_pVar = intval($pager_pVar[1]);
            $pageLen_pVar = intval($pager_pVar[0]);
            $offset_pVar = $pageLen_pVar * $page_pVar;
            $limit_str_pVar = ' LIMIT %d, %d';
            $limit_data_pVar = array($offset_pVar, $pageLen_pVar);
        }
        else {
            $limit_str_pVar = '';
            $pager_pVar = false;
            $limit_data_pVar = array();
        }
        /*
               $sql_filter_pVar = '';
               if(isset($filter_pVar['filter'])) {
                   $filter_params_pVar = array();
                   parse_str($filter_pVar['filter'], $filter_params_pVar);
                   if(count($filter_params_pVar)) {
                       foreach($filter_params_pVar as $k_pVar=>$v_pVar) {
                           $sql_filter_pVar .= ' AND ';
                           $sql_filter_pVar .= '`' . addslashes($k_pVar) . '` = \'' . addslashes($v_pVar) . '\'';
                       }
                   }
               }
               */

        $sql_pVar = 'SELECT `u`.*, `ou`.* FROM (SELECT * FROM `%tusers_onlineusers` as `ou` WHERE `ou`.`time_next_timeout`>=now() ORDER BY `ou`.`time_last_access` desc' . $limit_str_pVar . ') as `ou` LEFT JOIN `%titems_users__data` as `u` ON `u`.`item_id` = `ou`.`user_id` ORDER BY `ou`.`time_last_access` desc';
        $sql_params_pVar = array();
        if(!empty($limit_str_pVar)) {
            $sql_params_pVar[] = $limit_data_pVar[0];
            $sql_params_pVar[] = $limit_data_pVar[1];
        }
        $data_pVar = db_gClass::getResultArray_gFunc($sql_pVar, __FILE__, __LINE__, $sql_params_pVar);

        if($pager_pVar !== false) {
            $sql_pVar = 'SELECT count(`online_user_id`) as `n` FROM `%tusers_onlineusers` as `ou` WHERE `ou`.`time_next_timeout`>=now()';
            $pager_pVar[2] = self::getField_gFunc($sql_pVar, __FILE__, __LINE__, false);

            $data_pVar['_pager'] = array();
            $data_pVar['_pager']['pageLen'] = $pager_pVar[0];
            $data_pVar['_pager']['currentPage'] = (int)$pager_pVar[1] + 1;
            $data_pVar['_pager']['totalItems'] = $pager_pVar[2];
            $data_pVar['_pager']['totalPages'] = ceil($data_pVar['_pager']['totalItems'] / $data_pVar['_pager']['pageLen']);
        }
        /*
                $u_pVar = db_items_gClass::getItems_gFunc('users', array('item_id'=>0));
                $data_pVar['_order_by_fields'] = $u_pVar['_order_by_fields'];
        */

        return($data_pVar);
    }

    public static function getAccesslog_gFunc($filter_pVar = array())
    {
        if(!session_gClass::userHasRightsAccessAction_gFunc(s_users_show_accesslog)) {
            return(array());
        }

        if(isset($filter_pVar['pager'])) {
            $pager_pVar = explode(',', $filter_pVar['pager']);
            if(intval($pager_pVar[1])) {
                $pager_pVar[1] = intval($pager_pVar[1]) - 1;
            }
            unset($filter_pVar['pager']);
        }
        else {
            $pager_pVar = false;
        }

        if(is_array($pager_pVar) && count($pager_pVar) == 2 && intval($pager_pVar[0]) > 0) {
            $page_pVar = intval($pager_pVar[1]);
            $pageLen_pVar = intval($pager_pVar[0]);
            $offset_pVar = $pageLen_pVar * $page_pVar;
            $limit_str_pVar = ' LIMIT ' . $offset_pVar . ', ' . $pageLen_pVar;
        }
        else {
            $limit_str_pVar = '';
            $pager_pVar = false;
        }

        $sql_pVar = 'select * FROM `%tusers_accesslog` as `al` LEFT JOIN `%titems_users__data` as `ud` ON `ud`.`item_id` = `al`.`user_id` order by `al`.`access_time` desc' . $limit_str_pVar;
        $data_pVar = db_gClass::getResultArray_gFunc($sql_pVar, __FILE__, __LINE__, 'user_id');

        if($pager_pVar !== false) {
            $sql_pVar = 'SELECT count(`access_id`) as `n` FROM `%tusers_accesslog`';
            $pager_pVar[2] = self::getField_gFunc($sql_pVar, __FILE__, __LINE__, false);

            $data_pVar['_pager'] = array();
            $data_pVar['_pager']['pageLen'] = $pager_pVar[0];
            $data_pVar['_pager']['currentPage'] = (int)$pager_pVar[1] + 1;
            $data_pVar['_pager']['totalItems'] = $pager_pVar[2];
            $data_pVar['_pager']['totalPages'] = ceil($data_pVar['_pager']['totalItems'] / $data_pVar['_pager']['pageLen']);
        }

        return($data_pVar);
    }

    public static function getRequestlog_gFunc($filter_pVar = array())
    {
        if(!session_gClass::userHasRightsAccessAction_gFunc(s_users_show_requestlog)) {
            return(array());
        }

        if(isset($filter_pVar['pager'])) {
            $pager_pVar = explode(',', $filter_pVar['pager']);
            if(intval($pager_pVar[1])) {
                $pager_pVar[1] = intval($pager_pVar[1]) - 1;
            }
            unset($filter_pVar['pager']);
        }
        else {
            $pager_pVar = false;
        }

        if(is_array($pager_pVar) && count($pager_pVar) == 2 && intval($pager_pVar[0]) > 0) {
            $page_pVar = intval($pager_pVar[1]);
            $pageLen_pVar = intval($pager_pVar[0]);
            $offset_pVar = $pageLen_pVar * $page_pVar;
            $limit_str_pVar = ' LIMIT ' . $offset_pVar . ', ' . $pageLen_pVar;
        }
        else {
            $limit_str_pVar = '';
            $pager_pVar = false;
        }

        $sql_pVar = 'select `al`.`request_id`, `al`.`user_id`, `al`.`request_time`, `al`.`request_type`, `al`.`admin_user_id`,concat(`ud`.`login`, \' (\', `al`.`user_id` , \')\')  as `user_login`, `al`.`admin_user_id`,`ud2`.`login` as `admin_login`
    					FROM `%tusers_requestlog` as `al`
    					LEFT JOIN `%titems_users__data` as `ud` ON `ud`.`item_id` = `al`.`user_id`
    					LEFT JOIN `%titems_users__data` as `ud2` ON `ud2`.`item_id` = `al`.`admin_user_id`
    					order by `al`.`request_time` desc' . $limit_str_pVar;
        $data_pVar = db_gClass::getResultArray_gFunc($sql_pVar, __FILE__, __LINE__, 'user_id');

        if($pager_pVar !== false) {
            $sql_pVar = 'SELECT count(`request_id`) as `n` FROM `%tusers_requestlog`';
            $pager_pVar[2] = self::getField_gFunc($sql_pVar, __FILE__, __LINE__, false);

            $data_pVar['_pager'] = array();
            $data_pVar['_pager']['pageLen'] = $pager_pVar[0];
            $data_pVar['_pager']['currentPage'] = (int)$pager_pVar[1] + 1;
            $data_pVar['_pager']['totalItems'] = $pager_pVar[2];
            $data_pVar['_pager']['totalPages'] = ceil($data_pVar['_pager']['totalItems'] / $data_pVar['_pager']['pageLen']);
        }

        return($data_pVar);
    }

    public static function getRawAccessLog_gFunc()
    {
        $sql_pVar = 'select * FROM `%tusers_accesslog` order by `access_time`';
        $data_pVar = db_gClass::getResultArray_gFunc($sql_pVar, __FILE__, __LINE__, 'user_id');

        return($data_pVar);
    }

    public static function getUserAddress_gFunc($userID_pVar, $addressID_pVar, $cacheEnabled_pVar = true)
    {
        $cacheName_pVar = '_access2_session_user_address_pVar' . '_' . $userID_pVar . '_' . $addressID_pVar;
        if($cacheEnabled_pVar) {
            $ret_pVar = self::getCachedResult_gFunc($cacheName_pVar);
        }

        if($cacheEnabled_pVar && $ret_pVar !== false) {
            return($ret_pVar);
        }
        else {
            $query_string_pVar = 'SELECT * FROM `%tusers_addressbook` as `a` WHERE `a`.`address_id` = %d AND `a`.`user_id` = %d AND `a`.`status`<>\'deleted\'';
            $data_pVar = self::getResult_gFunc($query_string_pVar, __FILE__, __LINE__, array($addressID_pVar, $userID_pVar));

            if($cacheEnabled_pVar === true) {
                self::cacheResult_gFunc($cacheName_pVar, $data_pVar);
            }
            return($data_pVar);
        }
    }

    public static function getUserAddressByType_gFunc($userId_pVar, $addressType_pVar, $cacheEnabled_pVar = true)
    {
        $cacheName_pVar = '_access2_session_user_address_'.$addressType_pVar.'_pVar' . '_' . $userId_pVar;
        if($cacheEnabled_pVar) {
            $ret_pVar = self::getCachedResult_gFunc($cacheName_pVar);
        }

        if($cacheEnabled_pVar && $ret_pVar !== false) {
            return($ret_pVar);
        }
        $sql_pVar = 'SELECT * FROM `%tusers_addressbook` WHERE `user_id`=%d and `address_type`=%s AND `status`<>\'deleted\'';
        $data_pVar = self::getResult_gFunc($sql_pVar, __FILE__, __LINE__, array($userId_pVar, $addressType_pVar));
        if(!count($data_pVar)) {
            $data_pVar = false;
        }
        if($cacheEnabled_pVar === true) {
            self::cacheResult_gFunc($cacheName_pVar, $data_pVar);
        }
        return($data_pVar);
    }

    public static function updateAddress_gFunc($userId_pVar, $addressType_pVar, $addressData_pVar)
    {
        $sql_pVar = 'SELECT `address_id` FROM `%tusers_addressbook` WHERE `user_id`=%d and `address_type`=%s AND `status` <> \'deleted\'';
        $addressId_pVar = self::getField_gFunc($sql_pVar, __FILE__, __LINE__, array($userId_pVar, $addressType_pVar));
        if($addressId_pVar === true) {
            $addressId_pVar = false;
        }

        $fields_pVar = array('address_name','email','name','first_name',
            'titul_pred','titul_za','street_number','city','zip','phone');

        $sqlDataStr_pVar = array();
        $sqlDataData_pVar = array();
        foreach ($fields_pVar as $v_pVar) {
            $sqlDataStr_pVar[] = '`' . $v_pVar . '`=%s';
            if(isset($addressData_pVar[$v_pVar])) {
                $sqlDataData_pVar[] = $addressData_pVar[$v_pVar];
            }
            else {
                $sqlDataData_pVar[] = '';
            }
        }

        if(!$addressId_pVar) {
            // vytvorim novu adresu
            $sqlDataStr_pVar[] = '`status`=%s'; $sqlDataData_pVar[] = 'active';
            $sqlDataStr_pVar[] = '`user_id`=%d'; $sqlDataData_pVar[] = $userId_pVar;
            $sqlDataStr_pVar[] = '`address_type`=%s'; $sqlDataData_pVar[] = $addressType_pVar;

            $sql_pVar = 'INSERT INTO `%tusers_addressbook` SET ' . implode(',', $sqlDataStr_pVar);
            $addressId_pVar = self::insert_gFunc($sql_pVar, __FILE__, __LINE__, $sqlDataData_pVar, true);
            return($addressId_pVar);
        }
        else {
            // updatnem adresu
            $sqlDataData_pVar[] = $addressId_pVar;
            $sql_pVar = 'UPDATE `%tusers_addressbook` SET ' . implode(',', $sqlDataStr_pVar) . ' WHERE `address_id`=%d';
            self::execute_gFunc($sql_pVar, __FILE__, __LINE__, $sqlDataData_pVar);
            return($addressId_pVar);
        }
    }

    static function getAllRights_gFunc(&$from_pVar, $fromComment_pVar = false)
    {
        $query_string_pVar = 'SELECT access_id FROM `%taccess__names`';
        $data_pVar = self::getResultArray_gFunc($query_string_pVar, __FILE__, __LINE__, false);
        $ret_pVar = array();
        foreach ($data_pVar as $k_pVar=>$v_pVar) {
            $ret_pVar[(int)$v_pVar['access_id']] = true;
            session_session_gClass::setFromRights_gFunc($from_pVar, $v_pVar['access_id'], true, 'system', $fromComment_pVar);
        }
        return($ret_pVar);
    }

    /**
     * Ocheckuje ci existuje uz pouzivatel s danym loginom alebo nickom..
     * Ignoruje pouzivatela $user_id_pVar
     * Vracia true, ak neexistuje (cize bude unikatny, ked ho vytvorim)
     * Vracia false ak uz existuje.
     *
     * @param unknown_type $loginName_pVar
     * @param unknown_type $user_id_pVar
     * @return unknown
     */
    static public function checkForUniqueLoginName_gFunc($loginName_pVar, $user_id_pVar = 0)
    {
        $sql_pVar = 'SELECT `item_id` FROM `%titems_users__data` WHERE (`login` = %s OR `nick` = %s OR `email` = %s) ';
        if($user_id_pVar) {
            $sql_pVar .= ' AND `item_id` <> %d ';
        }
        $sql_pVar .= ' AND `status` <> \'deleted\' AND `status`<>\'request\'';
        $result_pVar = self::getField_gFunc($sql_pVar, __FILE__, __LINE__, [
            $loginName_pVar,
            $loginName_pVar,
            $loginName_pVar,
            $user_id_pVar
        ]);
        if(!$result_pVar || $result_pVar === true) {
            return(true);
        }
        else {
            return(false);
        }
    }

    /**
     * prihlasi pouzivatela ako ineho pouzivatela bez potreby poznat heslo.
     * Musi mat prava s_system_superadmin.
     * Povodny pouzivatel je odhlaseny.
     *
     * @param unknown_type $asUserId_pVar
     * @return unknown
     */
    static public function login_as_gFunc($asUserId_pVar)
    {
        if(!session_gClass::userHasRightsAccessAction_gFunc(s_system_superadmin)) {
            return(false);
        }
        $data_pVar = self::getUserDataByID_gFunc(intval($asUserId_pVar));
        if(!is_array($data_pVar) || !count($data_pVar)) {
            return(false);
        }
        // user existuje
        $sql_pVar = "UPDATE `%tusers_onlineusers` SET `time_start`=now(), `user_id`=%d, `rights`=NULL  WHERE `session_str_id`=%s";
        self::execute_gFunc($sql_pVar, __FILE__, __LINE__, array($asUserId_pVar, session_gClass::getSessionId_pVar()));

        $query_string_pVar = 'INSERT INTO `%tusers_accesslog` (`user_id`, `access_time`, `access_type`) VALUES(%d, now(), \'logout\')';
        self::execute_gFunc($query_string_pVar, __FILE__,__LINE__, array(session_gClass::getUserDetail_gFunc('item_id')));

        $query_string_pVar = 'INSERT INTO `%tusers_accesslog` (`user_id`, `access_time`, `access_type`) VALUES(%d, now(), \'login\')';
        self::execute_gFunc($query_string_pVar, __FILE__,__LINE__, array($data_pVar['item_id']));

        return($data_pVar);
    }
}
