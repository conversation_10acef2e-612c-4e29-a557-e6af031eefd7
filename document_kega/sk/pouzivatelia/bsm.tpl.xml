<?xml version="1.0" encoding="utf-8" ?>
<truEngine-document xmlns:te="http://www.truengine.sk">

<te:source name="adduserbsm" varname="form" item_id="@{request:item_id}">
	<te:source-param name="submit_button_title_add">@L{Importovať používateľa z BSM.SK}</te:source-param>
</te:source>


<te:if test="@{form:hidden_fields:users_item_id:value}">
<te:else />
	<te:content-set name="title" value="@L{Importovať používateľa z BSM.SK}" />
</te:if>

<te:if test="@{form:error_code} == 'ok'">
		<ul>
			<li>
				Používateľ bol importovaný. Prihlásiť sa môžete prihlasovacím menom <strong>"@{form:vars:login}"</strong>.
				<a href="/log-in">@L{Prihlásiť}</a>
			</li>
		</ul>
<te:else />

		<div class="msg_error">
			@L{Používateľa sa nepodarilo importovať.}<br />
			@L{Ak chcete skúsiť znovu, reštartujte internetový prehliadač.}<br />
		</div>

			<div class="msg_error">
				sign_bsm = @{form:vars:sign_bsm}<br />
				sign_kega = @{form:vars:sign_kega}
				<br />
				Chybne polia:<br />
				<te:set ref="@{n}" value="0" />
				<te:for each="@{form:fieldsets} as @{fieldset}">
					<te:for each="@{fieldset:fields} as @{field}">
						<te:if test="@{field:error_code}!='ok'">
							<te:if test="@{n}">,</te:if>
							@{field:label}
							<?php @{n}++; ?>
						</te:if>
					</te:for>
				</te:for>
			</div>



</te:if>

</truEngine-document>
