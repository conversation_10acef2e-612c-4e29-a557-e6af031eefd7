<?php

namespace Mixedtype\Forms\Traits;

use App\Legacy\LegacyApp;
use Mixedtype\Multisite\Multisite;

trait FormStringsTrait
{
    function getString($tag, $defaultValue = null, $language = null) : string
    {
        if ($language === null) {
            $language = Multisite::getCurrentLanguage();
        }

        // todo: get string for field and language. If not found, set defaultValue

        return (string)$defaultValue;
    }

    function makeTag($str)
    {
        $str = iconv('UTF-8', 'ASCII//TRANSLIT', $str);
        $str = strtolower($str);
        $str = str_replace(' ', '_', $str);
        $str = preg_replace('/[^a-z0-9_]/', '', $str);
        return $str;
    }


    function getFieldLabelString($name, $defaultValue = null, $language = null) : string
    {
        return $this->getString('field.' . $name . '.label', $defaultValue, $language);
    }

    function getFieldInfoString($name, $defaultValue = null, $language = null) : string
    {
        return $this->getString('field.' . $name . '.info', $defaultValue, $language);
    }

    function getFieldErrorString($name, $defaultValue = null, $language = null) : string
    {
        return $this->getString('field.' . $name . '.error.' . $this->makeTag($defaultValue), $defaultValue, $language);
    }
}
