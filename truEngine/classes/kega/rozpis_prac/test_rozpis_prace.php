<?php


class test_rozpis_prace_gClass extends source_gClass
{
    protected function getData()
    {
        if(isset($this->params['delete_id']) && intval($this->params['delete_id'])) {
            if(session_gClass::userHasRightsAccess_gFunc(s_test_rozpis_prace_delete)) {
                $sql_pVar = 'UPDATE `%ttest_work` SET `status` = \'deleted\' WHERE `item_id` = %d';
                db_public_gClass::execute_gFunc($sql_pVar, __FILE__, __LINE__, array(intval($this->params['delete_id'])));
            }
        }
        if(isset($this->params['archive']) && $this->params['archive'] === 'true') {
            if(session_gClass::userHasRightsAccess_gFunc(s_test_rozpis_prace_archive)) {
                $sql_pVar = 'UPDATE `%ttest_work` SET `status` = \'archived\' WHERE `status` = \'active\'';
                db_public_gClass::execute_gFunc($sql_pVar, __FILE__, __LINE__);
            }
        }
        unset($this->params['delete_id']);
        unset($this->params['archive']);
        $adapter_pVar = items_gClass::getAdapter_gFunc('test_rozpis_prace');
        $data_pVar = $adapter_pVar->getItems_gFunc($this->params);
        return($data_pVar);
    }

}

class test_rozpis_prace extends test_rozpis_prace_gClass {}
