<?php
namespace Mixedtype\Components;


abstract class Component extends \Illuminate\View\Component
{
    use ComponentFactoryTrait;
    use ComponentHelpersTrait;
    use ComponentUploadsTrait;

    public function state() :array
    {
        return [
            //'data' => $this->data(),
            //'attributes' => $this->attributes,
        ];
    }

    public function html() : string
    {
        return $this->renderComponent();
    }

    public function removeUpload()
    {
        $field = request()->input('field');
        $uploadId = request()->input('upload');

        $this->loadUploadedFilesFromDb();
        $this->deleteFiles($field, $uploadId);

        return [
            'removed' => $uploadId
        ];
    }

}
