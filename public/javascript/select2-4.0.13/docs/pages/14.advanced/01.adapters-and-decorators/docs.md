---
title: Adapters and Decorators
taxonomy:
    category: docs
---

Starting in version 4.0, Select2 uses the [Adapter pattern](https://en.wikipedia.org/wiki/Adapter_pattern) as a powerful means of extending its features and behavior.

Most of the built-in features, such as those described in the previous chapters, are implemented via one of the [built-in adapters](/advanced/default-adapters).  You may further extend the functionality of Select2 by implementing your own adapters.

## Adapter interfaces

All custom adapters must implement the methods described by the `Adapter` interface.

In addition, adapters that override the default `selectionAdapter` and `dataAdapter` behavior must implement the additional methods described by the corresponding `SelectionAdapter` and `DataAdapter` interfaces.

### `Adapter`

All adapters must implement the `Adapter` interface, which Select2 uses to render DOM elements for the adapter and bind any internal events:

```
// The basic HTML that should be rendered by Select2. A jQuery or DOM element
// should be returned, which will automatically be placed by Select2 within the
// DOM.
//
// @returns A jQuery or DOM element that contains any elements that must be
//   rendered by Select2.
Adapter.render = function () {
  return $jq;
};

// Bind to any Select2 or DOM events.
//
// @param container The Select2 object that is bound to the jQuery element.  You
//   can listen to Select2 events with `on` and trigger Select2 events using the
//   `trigger` method.
// @param $container The jQuery DOM node that all default adapters will be
//   rendered within.
Adapter.bind = function (container, $container) { };

// Position the DOM element within the Select2 DOM container, or in another
// place. This allows adapters to be located outside of the Select2 DOM,
// such as at the end of the document or in a specific place within the Select2
// DOM node.
//
// Note: This method is not called on data adapters.
//
// @param $rendered The rendered DOM element that was returned from the call to
//   `render`. This may have been modified by Select2, but the root element
//   will always be the same.
// @param $defaultContainer The default container that Select2 will typically
//   place the rendered DOM element within. For most adapters, this is the
//   Select2 DOM element.
Adapter.position = function ($rendered, $defaultContainer) { };

// Destroy any events or DOM elements that have been created.
// This is called when `select2("destroy")` is called on an element.
Adapter.destroy = function () { };
```

### `SelectionAdapter`

The selection is what is shown to the user as a replacement of the standard `<select>` box. It controls the display of the selection option(s), as well anything else that needs to be embedded within the container, such as a search box.

Adapters that will be used to override the default `selectionAdapter` must implement the `update` method as well:

```
// Update the selected data.
//
// @param data An array of data objects that have been generated by the data
//   adapter. If no objects should be selected, an empty array will be passed.
//
// Note: An array will always be passed into this method, even if Select2 is
// attached to a source which only accepts a single selection.
SelectionAdapter.update = function (data) { };
```

### `DataAdapter`

The data set is what Select2 uses to generate the possible results that can be selected, as well as the currently selected results.

Adapters that will be used to override the default `dataAdapter`  must implement the `current` and `query` methods as well:

```
// Get the currently selected options. This is called when trying to get the
// initial selection for Select2, as well as when Select2 needs to determine
// what options within the results are selected.
//
// @param callback A function that should be called when the current selection
//   has been retrieved. The first parameter to the function should be an array
//   of data objects.
DataAdapter.current = function (callback) {
  callback(currentData);
}

// Get a set of options that are filtered based on the parameters that have
// been passed on in.
//
// @param params An object containing any number of parameters that the query
//   could be affected by. Only the core parameters will be documented.
// @param params.term A user-supplied term. This is typically the value of the
//   search box, if one exists, but can also be an empty string or null value.
// @param params.page The specific page that should be loaded. This is typically
//   provided when working with remote data sets, which rely on pagination to
//   determine what objects should be displayed.
// @param callback The function that should be called with the queried results.
DataAdapter.query = function (params, callback) {
  callback(queryiedData);
}
```

## Decorators

Select2 uses [decorators](https://en.wikipedia.org/wiki/Decorator_pattern) to expose the functionality of adapters through its [configuration options](/configuration).

You can apply a decorator to an adapter using the `Utils.Decorate` method provided with Select2:

```
$.fn.select2.amd.require(
    ["select2/utils", "select2/selection/single", "select2/selection/placeholder"],
    function (Utils, SingleSelection, Placeholder) {
  var CustomSelectionAdapter = Utils.Decorate(SingleSelection, Placeholder);
});
```

>>> All core options that use decorators or adapters will clearly state it in the "Decorator" or "Adapter" part of the documentation. Decorators are typically only compatible with a specific type of adapter, so make sure to note what adapter is given.

## AMD Compatibility

You can find more information on how to integrate Select2 with your existing AMD-based project [here](/getting-started/builds-and-modules).  Select2 automatically loads some modules when the adapters are being automatically constructed, so those who are using Select2 with a custom AMD build using their own system may need to specify the paths that are generated to the Select2 modules.
