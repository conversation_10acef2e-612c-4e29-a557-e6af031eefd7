<?php

class document404_gClass extends document_gClass
{
    function __construct($docName_pVar, $docType_pVar)
    {
        parent::__construct($docName_pVar, $docType_pVar);
        if($this->getDocName_gFunc() === false) {
            $this->setDocName_gFunc($docName_pVar);
        }
        $this->httpErrCode_pVar = 404;
    }

    function execute_gFunc()
    {
        main_gClass::setHttpErrCode_gFunc($this->getDocName_gFunc(), 404);
        return(sprintf(str___loader_err_404_sVar, $this->getDocName_gFunc()));
    }
}
