<?php

namespace App\Console\Commands;

use App\Opus\Stats\CountStats;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;


class CountStatsCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'opus:count-stats';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Count stats.';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        CountStats::count();
    }
}
