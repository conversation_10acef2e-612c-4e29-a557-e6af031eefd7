<?xml version="1.0" encoding="utf-8" ?>
<truEngine-document xmlns:te="http://www.truengine.sk">

<?php

if(!isset(@{timer_id})) {
	@{timer_id} = 1;
}
if(!isset(@{save_time})) {
	@{save_time} = false;
}
else {
	@{save_time} = @{save_time}?true:false;
}
if(!isset(@{time_start})) {
	@{time_start} = 0;
	@{time_step} = 1;
}
if(!isset(@{time_step})) {
	@{time_step} = 1;
}
if(!isset(@{ontimeout})) {
	@{ontimeout} = false;
} 

?>

<div te:print="no" id="fixed_timer@{timer_id}" style="position:absolute; left:10px;top:10px;padding:5px;background:#FFFFFF;border:2px solid #9999AA; font-size:22px"></div>
<te:include name="/sk/.html-templates/fixed-div" id="fixed_timer@{timer_id}" />


<script type="text/javascript">
var displayTime_@{timer_id}_initTime = new Date();
displayTime_@{timer_id}_initTime.setTime(displayTime_@{timer_id}_initTime.getTime());
<te:if test="@{save_time}">
	var tmp = Get_Cookie('tt_@{timer_id}');
	if(tmp) {
		displayTime_@{timer_id}_initTime.setTime(tmp);
	}
	Set_Cookie('tt_@{timer_id}', displayTime_@{timer_id}_initTime.getTime());	
</te:if>
displayTime_@{timer_id}('fixed_timer@{timer_id}');

//<![CDATA[
function displayTime_@{timer_id}(id)
{
	var d = new Date();
	var time = d.getTime() - displayTime_@{timer_id}_initTime.getTime();
	var coef = 1;
	
	time = time / 1000;
	time =  (time * @{time_step}) + @{time_start};
	
	time = parseInt(time);
	if(time < 0) {
		time = -time;
		coef = -1;
	}

	var h = parseInt(time / 3600);
		time = time - h * 3600;
	var m = parseInt(time / 60);
		time = time - m * 60;
	var s = time;
	
	if (h<10) { h="0" + h; }
	if (m<10) { m="0" + m; }
	if (s<10) { s="0" + s; }

  if($(id)) {
  	$(id).innerHTML = (coef < 0 ? '-':'') + h+":"+m+":"+s;
  }
  if(coef < 0) {
  	@{ontimeout};
  }
  
  t=setTimeout('displayTime_@{timer_id}(\'fixed_timer@{timer_id}\')',300);
}
//]]>
</script>

</truEngine-document>
