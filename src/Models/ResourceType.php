<?php

namespace Mixedtype\Mixedtype\Models;

use Illuminate\Support\Collection;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;

class ResourceType extends Model
{
    use HasFactory;

    static private Collection|null $resourceTypesCache = null;

    static function getResourceType($typeTag) : ?ResourceType
    {
        if(self::$resourceTypesCache === null) {
            self::$resourceTypesCache = ResourceType::all()
                ->keyBy('resource_type_tag');
        }

        return self::$resourceTypesCache->get($typeTag);
    }

    static function getResourceTypeId($typeTag) : ?int
    {
        $resourceType = self::getResourceType($typeTag);
        return $resourceType?->id;
    }

    static function createResourceType($typeTag, $allocatorType) : void
    {
        if(self::getResourceTypeId($typeTag)) {
            throw new \Exception("Resource type already exists ($typeTag)");
        }

        // create using model
        $resourceType = new ResourceType();
        $resourceType->resource_type_tag = $typeTag;
        $resourceType->resource_allocator_type = $allocatorType;
        $resourceType->save();

        self::$resourceTypesCache = null;
    }

    static function destroyResourceType($typeTag) : void
    {
        ResourceType::where('resource_type_tag', $typeTag)->delete();
        self::$resourceTypesCache = null;
    }
}
