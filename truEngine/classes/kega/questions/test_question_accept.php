<?php

class test_question_accept_gClass extends source_gClass
{

    protected function getData()
    {
        if(!isset($this->params['question_id']) || !is_numeric($this->params['question_id']) || !intval($this->params['question_id'])) {
            return(0);
        }
        if(!isset($this->params['type']) || ($this->params['type'] != 'accept' && $this->params['type'] != 'reject')) {
            return(0);
        }

        $question_id_pVar = intval($this->params['question_id']);
        $question_pVar = items_gClass::getItem_gFunc('test_questions', $question_id_pVar);
        if(!is_array($question_pVar) || $question_pVar['status'] != 'waiting') {
            return(0);
        }
        if(intval($question_pVar['garant_id']) && $question_pVar['garant_id'] != session_gClass::getUserDetail_gFunc('user_id')) {
            return(0);
        }

        if($this->params['type'] == 'accept') {
            if(!session_gClass::userHasRightsAccess_gFunc(s_test_accept_xquestion)) {
                return(0);
            }
            $data_pVar = array('item_id'=>$question_id_pVar, 'garant_id'=>session_gClass::getUserDetail_gFunc('user_id'), 'edited'=>'no', 'status'=>'active');


            $question_pVar['autorske_prava'] = str_replace('autor=', 'autor*=', $question_pVar['autorske_prava']);
            $arr_pVar = explode(',', $question_pVar['autorske_prava']);

            $info_pVar = db_items_gClass::getInfo_gFunc('test_questions');
            $lngs_pVar = array();
            foreach($arr_pVar as $tmp_pVar) {
                if(substr($tmp_pVar, 0, 5) == 'user/') {
                    $lngs_pVar[] = substr($tmp_pVar, 5, 2);
                }
            }
            foreach($info_pVar['languages'] as $lng_pVar) {
                if(!empty($question_pVar[$lng_pVar . '_otazka'])) {
                    if(!in_array($lng_pVar, $lngs_pVar)) {
                        $arr_pVar[] = '@user/' . $lng_pVar . '*=' . session_gClass::getUserDetail_gFunc('login');
                    }
                }
            }
            $data_pVar['autorske_prava'] = test_addquestion_gClass::userNameDuplicity_gFunc(implode(',', $arr_pVar));

            items_gClass::saveOrUpdateItem_gFunc('test_questions', $data_pVar);
        }
        else {
            if(!session_gClass::userHasRightsAccess_gFunc(s_test_reject_xquestion)) {
                return(0);
            }
            $data_pVar = array('item_id'=>$question_id_pVar, 'edited'=>'yes', 'status'=>'rejected');
            items_gClass::saveOrUpdateItem_gFunc('test_questions', $data_pVar);
            if(isset($this->params['reason']) && !empty($this->params['reason'])) {
                items_gClass::addItemComment_gFunc('test_questions', $question_id_pVar, $this->params['reason']);
            }
        }

        return(1);
    }
}

class test_question_accept extends test_question_accept_gClass { }
