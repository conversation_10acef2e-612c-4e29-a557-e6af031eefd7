<?php

class test_addtest_gClass extends additem_gClass
{
    private $official;
    private $questionsResultOk_pVar;

    protected function initParams_gFunc()
    {
        $this->setParam_gFunc('formtype-add','add_item');
        $this->setParam_gFunc('formtype-edit', '-');
        $this->setParam_gFunc('itemtype', 'test_templates');
    }

    protected function initForm_gFunc($multiedit_pVar = false, $initFormRef_pVar = true)
    {
        $this->questionsResultOk_pVar = false;
        $this->official = 'no';
        if(isset($this->params['official']) && $this->params['official'] === 'yes') {
            $this->official = 'yes';
        }
        unset($this->params['official']);

        if(!$this->isData_gFunc() && isset($this->params['template_id']) && $this->params['template_id']) {
            $initData_pVar = db_items_gClass::getItem_gFunc($this->params['itemtype'], $this->params['template_id']);
            $this->setInitArray_gFunc($initData_pVar);
        }

        parent::initForm_gFunc();

        $this->setFieldPrefix_gFunc($this->params['itemtype'] . '_');
        $info_pVar = db_items_gClass::getInfo_gFunc($this->params['itemtype']);

        $this->addField_gFunc('base', 'time_start', 'varchar', 'Čas dostupnosti (začiatok)');
        $this->addField_gFunc('base', 'time_stop', 'varchar', 'Čas dostupnosti (koniec)');
        if($this->official === 'yes') {
            $this->addField_gFunc('base', 'access_key', 'varchar', 'Prístupový kód');
        }

        $this->addHiddenField_gFunc('template_id', 0);
        if(isset($this->params['template_id']) && $this->params['template_id']) {
            $this->setHiddenFieldDefaultValue_gFunc('template_id', $this->params['template_id']);
        }

        if(!$this->getFieldValue_gFunc('test_templates_item_id')) {

        }
        else {

        }


        $this->setFieldPrefix_gFunc('');

        $tree_pVar = db_items_gClass::getTree_gFunc('test_questions', 'zaradenie');
        $questionFields_pVar = db_items_gClass::getItemsFields_gFunc('test_questions');

        foreach($tree_pVar['_defs'] as $field_pVar=>$fieldId_pVar) {
            $this->addField_gFunc('categories', $field_pVar,  $questionFields_pVar[$field_pVar]['type'], $questionFields_pVar[$field_pVar]['sk_name']);
            $values_pVar = array('-1'=>'');
            foreach($tree_pVar['_values'][$fieldId_pVar] as $value_pVar) {
                $values_pVar[$value_pVar['enum_field_value']] = $value_pVar['sk_enum_field_name_item'];
            }
            $this->setFieldOptions_gFunc('categories', $field_pVar, $values_pVar);

            $this->setFieldEvent_gFunc('categories', $field_pVar, 'onchange', 'selectFilterChanged(test_questions_zaradenie_selector, \''.$field_pVar.'\', \'\');');
            $this->addJavaScript_gFunc($tree_pVar['_js']);
        }
        $this->addField_gFunc('categories', 'category_count',  'int', 'Pomer (%)');
        $this->addField_gFunc('categories', 'add_category',  'varchar', '');

        $items_pVar = main_gClass::getSessionData_gFunc('selected_items', array());
        $this->addField_gFunc('categories', 'add_questions_values',  'text', '');
        $this->addField_gFunc('categories', 'add_questions',  'varchar', '');
        if(isset($items_pVar['test_questions']) && is_array($items_pVar['test_questions']) && count($items_pVar['test_questions'])) {
            $this->setDefaultValue_pVar('categories', 'add_questions_values', implode(',', array_keys($items_pVar['test_questions'])));
        }

        $this->setFieldPrefix_gFunc($this->params['itemtype'] . '_');
    }

    protected function getData()
    {
        $this->setFieldPrefix_gFunc($this->params['itemtype'] . '_');
        $value_pVar = $this->getFieldValue_gFunc('categories');

        $total_pVar = 0;
        $values_pVar = explode(NL, $value_pVar);
        foreach($values_pVar as $v_pVar) {
            $tmp_pVar = explode(' ', $v_pVar);
            if(count($tmp_pVar) > 1 && is_numeric($tmp_pVar[0])) {
                $total_pVar += $tmp_pVar[0];
            }
        }
        foreach($values_pVar as $k_pVar=>$v_pVar) {
            $tmp_pVar = explode(' ', $v_pVar);
            if(count($tmp_pVar) > 1 && is_numeric($tmp_pVar[0])) {
                $tmp_pVar[0] = intval((100 / $total_pVar) * $tmp_pVar[0]);
                $values_pVar[$k_pVar] = implode(' ', $tmp_pVar);
            }
        }

        $value_pVar = implode(NL, $values_pVar);

        $this->setCurrentValue_pVar('categories', 'categories', $value_pVar);

        $ret_pVar = parent::getData();
        $ret_pVar['questions_ok'] = $this->questionsResultOk_pVar;

        return($ret_pVar);
    }

    protected function saveData_gFunc()
    {
        // vytvorim novy test
        // @TODO: dorobit aktualizaciu testu

        if($this->official === 'yes') {// @TODO: dorobit prava pre aktualizaciu testu
            if(!session_gClass::userHasRightsAccess_gFunc(s_test_add_official_test)) {
                return(false);
            }
        }
        else {
            if(!session_gClass::userHasRightsAccess_gFunc(s_test_add_test)) {
                return(false);
            }
        }

        $fields_pVar = $this->getFieldsNames_gFunc();
        $sql_format_pVar = '';
        $sql_data_pVar = array();

        $settings_pVar = array();
        foreach($fields_pVar as $v_pVar) {
            $value_pVar = $this->getFieldValue_gFunc($v_pVar);
            if(empty($value_pVar)) {
                continue;
            }
            if($v_pVar == 'categories') {
                $value_pVar = str_replace("\r", '', $value_pVar);
//				$v_pVar = str_replace("\n", '', $v_pVar);
            }
            if(in_array($v_pVar, array('sk_name', 'en_name', 'sk_description', 'en_description', 'smer', 'rocnik', 'time_start', 'time_stop'))) {
                $sql_data_pVar[$v_pVar] = $value_pVar;
                if(!empty($sql_format_pVar)) {
                    $sql_format_pVar .= ', ';
                }
                $sql_format_pVar .= '%s';
            }
            else {
                $settings_pVar[$v_pVar] = $value_pVar;
            }
        }
        $settings_pVar = kega_gClass::implodeSettings_gFunc($settings_pVar);

        if(!empty($sql_format_pVar)) {
            $sql_format_pVar .= ', ';
        }
        $sql_data_pVar['creator_id'] = intval(session_gClass::getUserDetail_gFunc('user_id'));
        $sql_format_pVar .= '%d';
        $sql_data_pVar['settings'] = $settings_pVar;
        $sql_format_pVar .= ', %s';
        $sql_data_pVar['time_create'] = 'now()';
        $sql_format_pVar .= ', %r';
        $sql_data_pVar['source_template_id'] = $this->getFieldValue_gFunc('template_id');
        $sql_format_pVar .= ', %d';
        $sql_data_pVar['official'] = $this->official;
        $sql_format_pVar .= ', %s';
        if($this->official === 'yes') {
            $sql_data_pVar['access_key'] = $this->getFieldValue_gFunc('access_key');
            $sql_format_pVar .= ', %s';
        }

        // nagenerujem otazky
        $settings_pVar = kega_gClass::explodeSettings_gFunc($settings_pVar);
        $questions_pVar = question_selector_gClass::selectQuestionsId_gFunc($settings_pVar);



        if(is_array($questions_pVar) && count($questions_pVar)) {
            // vlozim test
            $test_id_pVar = db_public_gClass::insertData_gFunc('%ttests', $sql_format_pVar, $sql_data_pVar, __FILE__, __LINE__, true);

            // vlozim otazky
            foreach($questions_pVar as $question_pVar) {
                $question_pVar['test_id'] = $test_id_pVar;
                db_public_gClass::insertData_gFunc('%ttests__questions', '%d, %d, %s, %d', $question_pVar, __FILE__, __LINE__);
            }
            $this->questionsResultOk_pVar = true;
            log_gClass::write_gFunc('TEST_QUESTION_TEST_UPDATE', $test_id_pVar, false, true);
        }
        else {
            return(false);
        }

        /*
        echo '<pre>'; print_r($settings_pVar); print_r($questions_pVar); echo '</pre>';
        Array
        (
            [sposob_testovania] => vsetko
            [pocet_otazok] => 2
            [moznosti_min] => 2
            [moznosti_max] => 3
            [cas] => 10
            [miesanie_otazok] => yes
            [miesanie_odpovedi] => yes
            [language] => sk
            [ukazat_odpovede] => postupne
            [ohodnotit_test] => yes
        )
        Array
        (
            [2] => Array
                (
                    [db_question_id] => 2
                    [question_order] => 1
                    [db_answer_ids] => 5,4,6
                )

            [1] => Array
                (
                    [db_question_id] => 1
                    [question_order] => 2
                    [db_answer_ids] => 2,3,1
                )

        )
        */
    }

    protected function validateField_gFunc($fieldsetName_pVar, $fieldName_pVar, $applyPrefix_pVar = true)
    {
        if($fieldName_pVar !== 'test_templates_sk_name'
            && $fieldName_pVar !== 'test_templates_en_name') {
            return(parent::validateField_gFunc($fieldsetName_pVar, $fieldName_pVar, $applyPrefix_pVar));
        }

        $names_pVar = array();
        $names_pVar['sk'] = $this->getFieldValue_gFunc('sk_name');
        $names_pVar['en'] = $this->getFieldValue_gFunc('en_name');
        $language_pVar = $this->getFieldValue_gFunc('language');

        if(!isset($names_pVar[$language_pVar]) || empty($names_pVar[$language_pVar])) {
            $this->setError_gFunc(false, '' . $language_pVar . '_name', 'require');
        }

        return(true);
    }

}

class test_addtest extends test_addtest_gClass {}
