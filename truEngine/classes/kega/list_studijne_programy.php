<?php

class list_studijne_programy_gClass extends table_gClass
{

    protected function initTable_gFunc()
    {
        if(isset($this->params['_order_by'])) {
            $order_by_pVar = explode('/', $this->params['_order_by']);
        }
        else {
            $order_by_pVar = false;
        }

        $data_pVar = items_gClass::getValues_gFunc('test_questions', 'program', $order_by_pVar);
        foreach ($data_pVar as $k_pVar=>$v_pVar) {
            $data_pVar[$k_pVar]['sk_enum_field_name_item'] = '<a href="'.main_gClass::makeUrl_gFunc('/otazky/editovat-studijny-program?value_id='.$data_pVar[$k_pVar]['enum_id']).'">'.$data_pVar[$k_pVar]['sk_enum_field_name_item'].'</a>';
            $data_pVar[$k_pVar]['en_enum_field_name_item'] = '<a href="'.main_gClass::makeUrl_gFunc('/otazky/editovat-studijny-program?value_id='.$data_pVar[$k_pVar]['enum_id']).'">'.$data_pVar[$k_pVar]['en_enum_field_name_item'].'</a>';
            $data_pVar[$k_pVar]['cz_enum_field_name_item'] = '<a href="'.main_gClass::makeUrl_gFunc('/otazky/editovat-studijny-program?value_id='.$data_pVar[$k_pVar]['enum_id']).'">'.$data_pVar[$k_pVar]['cz_enum_field_name_item'].'</a>';
        }

        $this->setData_gFunc($data_pVar);
        if(isset($this->params['columns'])) {
            $this->setColumnsFromString_gFunc($this->params['columns']);
        }

    }
}

class list_studijne_programy extends list_studijne_programy_gClass {}
