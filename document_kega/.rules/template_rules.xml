<?xml version="1.0" encoding="UTF-8"?>
<template-rules>

<!--

<rule type="tag" tag-name="#" path=".*/" syntax="all" handler="[module]" module="#" display="normal" />
<rule type="tag" tag-name="#" path=".*/" syntax="all" handler="#" file="#" display="normal" />
<rule type="tag" tag-name="#" path=".*/" syntax="all" display="normal" />

type = element | comment | all   ... | data | cdata
dysplay = normal | open | short | inside | none
syntax  = all | xml | xhtml | html



-->

	<group path="/" type="element">

		<!-- vsetky tagy budu defaultne zobrazene <tag></tag> -->
		<group path="(.*/)*">
		</group>

		<!-- layout rules -->
		<group path="truengine-layout/">
			<group path="html/head/">
				<rule tag-name="title" class="te" replace="te:title" />
			</group>
			<group path="html/body/(.*/)*">

			</group>
		</group>

		<!-- document rules -->

		<group path="truengine-document/(.*/)*">

		</group>

		<!-- layout & document rules -->
		<group path="(truengine-layout/html/body/|truengine-document/)(.*/)*">

			<!-- forms -->
			<!--
			<group module="forms">
				<rule tag-name="form" />
				<group path="form/(.*/)*">
					<rule tag-name="form-error" />
					<rule tag-name="form-item" />
					<rule tag-name="input" />
					<rule tag-name="select" />
					<rule tag-name="textarea" />
					<rule tag-name="checkboxgroup" />
					<rule tag-name="radioboxgroup" />
				</group>
				<rule tag-name="option" path="form/(.*/)*select/(.*/)*" />
			</group>
			-->

			<!-- tables -->
			<!--
			<group module="tables">
				<rule tag-name="xtable" />
				<group path="xtable/">
					<rule tag-name="th" />
					<rule tag-name="tr" />
				</group>
				<group path="xtable/th/">
					<rule tag-name="td" />
				</group>
				<group path="table/tr/">
					<rule tag-name="td" />
				</group>
			</group>
			-->

			<group syntax="xhtml" type="element">
				<rule display="normal" /> <!-- vsetky tagy v html zobrazujem ako parove (okrem vynimiek uvedenych nizsie) -->
				<rule tag-name="br" display="short" />
				<rule tag-name="hr" display="short" />
				<rule tag-name="img" display="short" />
				<rule tag-name="input" display="short" />
				<rule tag-name="tcpdf" display="short" />
			</group>
			<group syntax="html" type="element">
				<rule display="normal" /> <!-- vsetky tagy v html zobrazujem ako parove (okrem vynimiek uvedenych nizsie) -->
				<rule tag-name="br" display="open" />
				<rule tag-name="hr" display="open" />
				<rule tag-name="img" display="open" />
				<rule tag-name="input" display="open" />
				<rule tag-name="tcpdf" display="open" />
			</group>
		</group>

		<rule tag-name="truengine-layout" display="inside"/>
		<rule tag-name="truengine-document" display="inside" />

	</group>

    <group path="/(.*/)*" class="te" type="element" display="normal">
        <rule attr-name="te:xmlns" />

        <rule attr-name="href" />
        <rule attr-name="src" />
        <rule attr-name="action" tag-name="form" />


        <rule attr-name="te:if" />
        <rule  tag-name="te:if" />
        <rule attr-name="te:else" />
        <rule  tag-name="te:else" />
        <rule attr-name="te:ajax" />
        <rule  tag-name="te:ajax" />
        <rule attr-name="te:ajax-request" />
        <rule  tag-name="te:ajax-request" />

        <rule attr-name="te:choose" />
        <rule attr-name="te:when" />
        <rule attr-name="te:otherwise" />

        <rule  tag-name="te:choose" />
        <rule  tag-name="te:when" />
        <rule  tag-name="te:otherwise" />

        <rule attr-name="te:while" />
        <rule  tag-name="te:while" />

        <rule attr-name="te:for" />
        <rule  tag-name="te:for" />

        <rule  tag-name="te:set" />
        <rule attr-name="te:attrs" />

        <rule attr-name="te:content" />
        <rule  tag-name="te:content" />

        <rule attr-name="te:content-get" />
        <rule  tag-name="te:content-get" />

        <rule  tag-name="te:content-set" />
        <rule  tag-name="te:title" />
        <rule  tag-name="te:include">
        	<rule  tag-name="te:include-param" />
        </rule>
        <rule  tag-name="te:layout-insert" />
        <rule  tag-name="te:layout-replace" />

        <rule  tag-name="te:price" />

        <rule  tag-name="te:redirect" />

        <rule  tag-name="te:source">
        	<rule  tag-name="te:source-param" />
        </rule>

        <rule  tag-name="te:search" />

        <rule attr-name="te:access" />
        <rule  tag-name="te:access" />

        <rule attr-name="te:system-document" />

        <rule attr-name="te:xaccess" />
        <rule  tag-name="te:xaccess" />

        <rule  tag-name="te:menu-group" />
        <rule  tag-name="te:menu-item" />
        <rule  tag-name="te:menu-path" />
        <rule  tag-name="te:menu-item" attr-name="te:source" />

        <rule tag-name="te:result" />
        <rule tag-name="te:header" />

        <rule attr-name="te:print" />

        <rule attr-name="te:edit" />
        <rule  tag-name="te:edit" />
        <rule attr-name="te:string" />
        <rule  tag-name="te:string" />
        <rule  tag-name="te:bookmark" />


        <rule attr-name="te:legacy-form" />
        <rule  tag-name="te:legacy-form" />

        <rule attr-name="te:component" />
        <rule  tag-name="te:component" />

        <rule attr-name="te:livewire" />
        <rule  tag-name="te:livewire" />


        <rule type="comment" />

		<group path="/(.*/)*">
			<rule tag-name="script" display="normal" />
		</group>

    </group>
</template-rules>
