@props([
    'name',
    'value'
])

<input type="file" name="{{ $name }}" id="{{ $name }}" class="form-control js--file-upload">

<div class="js--file-uploaded">
    <span class="js--file-info"></span>

    <div class="js--upload-progress-wrp upload-progress-wrp">
        <div class="progress-bar"></div>
        <div class="status">0%</div>
    </div>
    <div class="js--file-preview"></div>
    <a href="#" class="js--file-upload-reset">zrušit</a>


    <style>
        .upload-progress-wrp {
            border: 1px solid #0099CC;
            padding: 1px;
            position: relative;
            height: 30px;
            border-radius: 3px;
            margin: 10px;
            text-align: left;
            background: #fff;
            box-shadow: inset 1px 3px 6px rgba(0, 0, 0, 0.12);
        }

        .upload-progress-wrp .progress-bar {
            height: 100%;
            border-radius: 3px;
            background-color: #f39ac7;
            width: 0;
            box-shadow: inset 1px 1px 10px rgba(0, 0, 0, 0.11);
        }

        .upload-progress-wrp .status {
            top: 3px;
            left: 50%;
            position: absolute;
            display: inline-block;
            color: #000000;
        }
    </style>
</div>

@if(is_array($value))
    <script type="text/javascript">
        setUpload(
            '{{ $name }}',
            '{{ $value['name'] }}',
            '{{ $value['size'] }}',
            '{!! $value['preview'] !!}',
            true
        );
    </script>
@endif

