<?php

class itemsAdapter_test extends itemsAdapter_gClass
{
    protected $filter_pVar;

    function getInfo_gFunc()
    {
        return (array('status' => 'active', 'name' => 'test', 'languages' => array('sk', 'en')));
    }

    function getItemsFields_gFunc()
    {
        // fiely su take iste ako pri sablonach...
        $fields_pVar = db_items_gClass::getItemsFields_gFunc('test_templates');

        $fields_pVar['creator_id'] = $fields_pVar['owner_id'];
        $fields_pVar['creator_id']['tag'] = 'creator_id';
        $fields_pVar['creator_id'][main_gClass::getLanguage_gFunc() . '_name'] = 'creator_id';

        $fields_pVar['name'][main_gClass::getLanguage_gFunc() . '_name'] = 'Názov testu';

        unset($fields_pVar['owner_id']);

        return ($fields_pVar);
    }

    protected function custom_getFilterQuery_gFunc($filter_pVar)
    {
        if (!isset($filter_pVar['official']) || $filter_pVar['official'] !== 'yes') {
            $filter_pVar['official'] = 'no';
        }
        $this->filter_pVar = $filter_pVar;

        $settingsFields_pVar = array('sposob_testovania', 'pocet_otazok', 'miesanie_otazok', 'miesanie_odpovedi', 'preferred_keywords', 'language', 'media', 'ukazat_odpovede', 'ohodnotit_test');

        foreach ($settingsFields_pVar as $field_pVar) {
            if (isset($filter_pVar[$field_pVar])) {
                $values_pVar = explode('|', $filter_pVar[$field_pVar]);
                if (count($values_pVar) == 1) {
                    if (substr($filter_pVar[$field_pVar], 0, 6) == 'LIKE(%') {
                        $filter_pVar[$field_pVar] = substr($filter_pVar[$field_pVar], 5, -1);
                    }
                    $filter_pVar[$field_pVar] = array('__operator' => 'LIKE', 'settings' => '%' . $field_pVar . '=' . $filter_pVar[$field_pVar] . '%');
                } else {
                    $filter_pVar[$field_pVar] = array('_operator' => 'OR');
                    foreach ($values_pVar as $v_pVar) {
                        $filter_pVar[$field_pVar][] = array('__operator' => 'LIKE', 'settings' => '%' . $field_pVar . '=' . $v_pVar . '%');
                    }
                }
            }
        }
        //print_r($filter_pVar);
        $ret_pVar = parent::custom_getFilterQuery_gFunc($filter_pVar);
        //print_r($ret_pVar);
        return ($ret_pVar);
    }

    function getEnumFieldsValues_gFunc($where_str_pVar = '', $where_data_pVar = array(), $columns_pVar = '*', $index_by_pVar = false)
    {
        $values_pVar = db_items_gClass::getEnumFieldsValues_gFunc('test_templates');
        return ($values_pVar);
    }

    protected function _pagerData_gFunc($where_str_pVar, $where_data_pVar, $order_by_pVar, $offset_pVar, $pageLen_pVar)
    {

        if ($this->filter_pVar['official'] === 'yes') {
            if (!session_gClass::userHasRightsAccess_gFunc(s_test_list_official_tests)) {
                return (array());
            }
            if (!session_gClass::userHasRightsAccess_gFunc(s_test_list_official_unactive_tests)) {
                $where_str_pVar .= 'AND (`D`.`time_start` IS NULL OR `D`.`time_start` < now())';
                $where_str_pVar .= 'AND (`D`.`time_stop` IS NULL OR `D`.`time_stop` > now())';
            }
        } else {
            if (!session_gClass::userHasRightsAccess_gFunc(s_test_list_tests)) {
                return (array());
            }
            if (!session_gClass::userHasRightsAccess_gFunc(s_test_list_unactive_tests)) {
                $where_str_pVar .= 'AND (`D`.`time_start` IS NULL OR `D`.`time_start` < now())';
                $where_str_pVar .= 'AND (`D`.`time_stop` IS NULL OR `D`.`time_stop` > now())';
            }
        }

        $sql_pVar = 'SELECT
						`D`.`id` as `item_id`, `D`.*, count(`r`.`id`) as `instances`, min(`itq`.`aktualnost`) as aktualnost
						FROM `%ttests` as `D`
						LEFT JOIN `%ttests_running` AS `r` ON `r`.`source_test_id` = `D`.`id`
						LEFT JOIN `%ttests__questions` as `tq` ON `D`.`id` = `tq`.`test_id`
						LEFT JOIN `%titems_test_questions__data` as `itq` on `itq`.`item_id` = `tq`.`db_question_id`
						';
        $sql_pVar .= $where_str_pVar;
        $sql_pVar .= ' GROUP BY `D`.`id` ';
        $sql_pVar .= $order_by_pVar;

        $data_pVar = db_public_gClass::getResultArray_gFunc($sql_pVar, __FILE__, __LINE__, $where_data_pVar);

        $this->formatData_gFunc($data_pVar);

        return ($data_pVar);
    }

    protected function _nonPagerData_gFunc($where_str_pVar, $where_data_pVar, $order_by_pVar)
    {
        return (array());
    }

    protected function _getCountData_gFunc($where_str_pVar, $where_data_pVar)
    {
        $sql_pVar = 'SELECT
						count(`id`)
						FROM `%ttests` as `D`
						';
        $sql_pVar .= $where_str_pVar;

        return (db_public_gClass::getField_gFunc($sql_pVar, __FILE__, __LINE__, $where_data_pVar));
    }

    protected function _getOrderByFields_gFunc()
    {
        return (array('time_create' => 'Čas vytvorenia', 'time_start' => 'Čas dostupnosti (začiatok)', 'time_stop' => 'Čas dostupnosti (koniec)', 'name' => 'Názov testu', 'description' => 'Upresnenie', 'smer' => 'Smer', 'rocnik' => 'Ročník'));
    }

    private function formatData_gFunc(&$data_pVar)
    {
        foreach ($data_pVar as $k_pVar => $v_pVar) {
            if (substr($k_pVar, 0, 1) === '_') {
                continue;
            }
            $data_pVar[$k_pVar]['item_id'] = $data_pVar[$k_pVar]['id'];

            $data_pVar[$k_pVar]['settings'] = kega_gClass::explodeSettings_gFunc($v_pVar['settings']);
            foreach ($data_pVar[$k_pVar]['settings'] as $kk_pVar => $vv_pVar) {
                $data_pVar[$k_pVar][$kk_pVar] = $vv_pVar;
            }

            foreach ($v_pVar as $kk_pVar => $vv_pVar) {
                if (isset($kk_pVar[2]) && $kk_pVar[2] == '_' && substr($kk_pVar, 0, 2) == main_gClass::getLanguage_gFunc()) {
                    $data_pVar[$k_pVar][substr($kk_pVar, 3)] = $vv_pVar;
                }
            }
        }
    }

}

