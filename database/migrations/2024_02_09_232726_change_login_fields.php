<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        $registerUserFormId = \Illuminate\Support\Facades\DB::table('kega_items_users__forms')
            ->where('form_name', 'register_user')
            ->value('form_id');

        \Illuminate\Support\Facades\DB::table('kega_items_users__fields')
            ->where('tag', 'login')
            ->update([
               'sk_name' => 'Prihlasovací email',
                'cz_name' => 'Přihlašovací email',
                'en_name' => 'Login email',
                'type' => 'email',
                'pattern'=> null,

                'sk_field_info' => '::E-mail, ktorým sa budete prihlasovať do systému.<br />Zadajte svoju e-mailovú adresu.<br />Adresa sa nebude zobrazovať bežným používateľom.<br />Adresa musí byť zadaná správne, je overen<PERSON> overovacím emailom.',
                'en_field_info' => '::The e-mail which you will use to access the system.<br />Enter your email address.<br />Address will not be show to others  unless you dont request so.<br />Address must be correct, this is verified by special email.',
                'cz_field_info' => '::E-mail, ktorým sa budete prihlasovať do systému.<br />Zadajte svoju e-mailovú adresu.<br />Adresa sa nebude zobrazovať bežným používateľom.<br />Adresa musí byť zadaná správne, je overená overovacím emailom.',
            ]);

        \Illuminate\Support\Facades\DB::table('kega_items_users__fields')
            ->whereIn('tag', ['email', 'nick', 'msn', 'icq', 'skype'])
            ->update([
                'field_order' => null,
                'fieldset' => null,
            ]);

        \Illuminate\Support\Facades\DB::table('kega_items_users__form_rules')
            ->where('form_id', $registerUserFormId)
            ->whereIn('field_name', ['email', 'nick'])
            ->delete();

    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        //
    }
};
