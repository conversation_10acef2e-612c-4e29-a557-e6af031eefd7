<?php

class test_addanswer_gClass extends additem_gClass
{
    protected function initParams_gFunc()
    {
        $this->setParam_gFunc('formtype-add','add_item');
        $this->setParam_gFunc('formtype-edit', 'edit_item');
        $this->setParam_gFunc('itemtype', 'test_answers');
    }

    protected function initForm_gFunc($multiedit_pVar = false, $initFormRef_pVar = true)
    {
        if(parent::initForm_gFunc()) {
            $this->addHiddenField_gFunc('otazka', $this->params['otazka']);
            $this->setHiddenFieldDefaultValue_gFunc('test_answers_test_question', $this->params['otazka']);
            $otazka = $this->getFieldValue_gFunc('otazka');
            if(empty($otazka)) {
                error_gClass::error_gFunc(__FILE__, __LINE__);
                return;
            }
        }
    }

    protected function saveData_gFunc()
    {
        parent::saveData_gFunc();
        items_gClass::reorderItems_gFunc('test_answers', 'order', 1, '`test_question`=' . intval($this->getFieldValue_gFunc('otazka')));
        log_gClass::write_gFunc('TEST_QUESTION_UPDATED', $this->params['otazka'], false, true);
    }
}

class test_addanswer extends test_addanswer_gClass {}
