<?php

namespace Mixedtype\Forms\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Mixedtype\Forms\FormFactory;

class FormActionController extends Controller
{
    public function formAction(Request $request)
    {
        $form = FormFactory::createForm($request->input('_form_class', $request->input('_form_tag')));
        return response()->json($form->callAction($request->input('_action'), $request->all()));
    }

    public function formFieldAction(Request $request)
    {
        $field = FormFactory::createField($request->input('_field_class', $request->input('_field_tag')));
        return response()->json($field->callAction($request->input('_action'), $request->all()));

    }
}
