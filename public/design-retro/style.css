
body {
  padding:0px;
  margin:0px;
  background: url(images/bg.png) repeat-y center #dcd5b9;
  font-family:"Arial CE","Helvetica CE",Arial,helvetica,sans-serif;
  font-size:14px;
  color:#111111;
}

body a {
	color:#111111;
}

#main_container {
	width:1024px;
	margin-left:auto;
	margin-right:auto;
}

#head {
	height:261px;
	background: url(images/banner.png) no-repeat;
}
#content {
	background: url(images/bg_content.png) repeat-y;
}
#top {
	background: url(images/top.png) no-repeat;
}
#top img {
	border:0px none;
}

#left {
	width:746px;
	float:left;
	padding-left:40px;
	padding-right:30px;
	text-align:left;
}
#right {
	width:277px;
	float:left;
	border-left:1px white;
	position:relative;
	padding-top:20px;
}
#footer {
	clear:both;
    background-image: none;
}
.msel_test_questions {
    position: relative;
    top:20px;
    left:-8px;
}
#contact_link {
	position:absolute;
	top:-37px;
	left:-6px;
	width:190px;
	height:52px;
	padding:5px 0px 10px 75px;
	background:url(images/phone.png) no-repeat left #F2ECD7;
}
#contact_link a {

}

.vnav div {
	background:url(images/box-bottom.png) no-repeat bottom;
	width:237px;
	padding-bottom:20px;
	margin-left:6px;
	margin-bottom:25px;
	font-size:14px;
}

.vnav div div {
	background:url(images/box-top.png) no-repeat;
	padding:0px;
	margin:0px;
}

.vnav div div ul {
	list-style-type:none;
	margin:0px;
	padding:0px;
	padding-top:10px;
	background:url(images/box-top-ul2.png) no-repeat 15px 0px;
}
.vnav div div ul li {
	background:url(images/box-li.png) no-repeat;
	margin:0px 15px;
	height:25px;
	padding-left:10px;
	padding-top:8px;
    padding-bottom: 25px;
}
.vnav div div ul li.active {
	background:url(images/box-li-active.png) no-repeat right;
	margin:0px 4px 0px 0px;
	padding-left:25px;
    height:33px;
}
.vnav div div ul li:hover {
	font-weight:bold;
}

.vnav div div ul li a {
	display:block;
}

.vnav div h4 {
	margin:0px;
	padding:10px 0px 10px 20px;
}

.vnav h4.loginname {
	margin:0px;
	padding:0px;
	background:url(images/label.png) no-repeat bottom;
	height:69px;
	width:278px;
	margin-left:22px;
	padding-top:22px;
	padding-left:67px;
	font-size:15px;
	margin-bottom:5px;
}

.vnav h4.loginname a {

}

#footer {
  margin-left:auto;
  margin-right:auto;
  width:1000px;
  min-height:60px;
  font-size:11px;
  color:#999999;
  line-height:15px;
  position:relative;
}
#footer a {
  color:#999999;
}

#footer div {
  float:right;
  width:330px;
}

#footer #footer_top {
	position:absolute;
	top:50px;
	left:300px;
	background:#E0E9ED;
	width:191px;
	height:31px;
	text-align:center;
	padding-top:20px;
	background:url(images/gotop.png);
	font-size:12px;
}

#footer #footer_top a {
	color:#000000;
}

#footer .footer_text {
  float:left;
  width:700px;
  margin-left:20px;
  margin-top:10px;
}

#footer .te_logo {
  width:270px;
  margin-left:0px;
}

#footer .te_logo div {
  float:right;
  width:175px;
  padding-top:25px;
}

#footer .te_logo img {
  padding:10px 0 10px 0;
  border:none;
}

/*menu*/
#top ul {
	float:right;
	margin:0px 40px 0px 0px;
	padding:0px;
	font-family:courier new;
	list-style-type:none;
	font-size:13px;
}
#top li {
	float:left;
	padding:10px 15px;
	height:99px;
}
#top li#menu_system.active, #top li#menu_testy.active {
	background:url(images/menu-w76.png) no-repeat center top;
}
#top li#menu_databaza.active {
	background:url(images/menu-w148.png) no-repeat center top;
}
#top li#menu_pouzivatelia.active {
	background:url(images/menu-w130.png) no-repeat center top;
}
#top li#menu_profil.active {
	background:url(images/menu-w105.png) no-repeat center top;
}
#top li .divider {
	display:none;
}
#top li a {
	font-weight:bold;
}

#top li a:hover {
	color:#666666;
}

.form_fieldset, .userlist, .questionlist, .itemlist, fieldset legend {
	background-color:#F1EAD4;
	border:1px solid #D6CFB5;
}

fieldset legend {
	background-color:#FAF5E4;
	border:1px solid #D6CFB5;
	padding:4px 15px;
}

#filter_container_emptydefault a {
	padding:5px;
}

.pager a {
	padding:5px;
}
.pager select {
	font-size:10px;
}

.tabulka .suda td {
	background-color:#FAF5E4;
	padding:2px;
}

.tabulka .licha td {
	background-color:#F1EAD4;
	padding:2px;
}

#layout-menu {

}

.questionlist.media {
	background:#FfFfe9;
}
