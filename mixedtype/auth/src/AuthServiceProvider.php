<?php
namespace Mixedtype\Auth;

use Illuminate\Support\ServiceProvider;
use Mixedtype\Auth\Forms\ForgotPasswordForm;
use Mixedtype\Auth\Forms\LoginForm;
use Mixedtype\Auth\Forms\RegisterForm;
use Mixedtype\Auth\Forms\ResetPasswordForm;
use Mixedtype\Forms\FormFactory;
use Illuminate\Support\Facades\Blade;


class AuthServiceProvider extends ServiceProvider
{

    public function boot()
    {
        $this->loadRoutesFrom(__DIR__.'/../routes/web.php');
        $this->loadViewsFrom(__DIR__.'/../resources/views', 'mixedtype-auth');

        // registracia namespace - ale aj tak musim registrovat formular cez FormFactory
        //Blade::componentNamespace('Mixedtype\Auth\Forms', 'mixedtype-auth');

        FormFactory::registerFormClass(RegisterForm::class, 'mixedtype-auth::register-form');


        Blade::component(ForgotPasswordForm::class, 'mixedtype-auth::forgot-password-form');
        Blade::component(LoginForm::class, 'mixedtype-auth::login-form');
        Blade::component(ResetPasswordForm::class, 'mixedtype-auth::reset-password-form');



        if ($this->app->runningInConsole()) {

        }
    }


    public function register()
    {
        $this->mergeConfigFrom(
            __DIR__.'/../config/auth.php', 'mixedtype.auth'
        );
    }
}
