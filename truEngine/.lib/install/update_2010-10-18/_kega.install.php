<?php

if(!isStamp_gFunc('kega', 'kega update-2010-10-18xx')) {
	$items_pVar = new install_item_gClass('test_templates', false);
	$items_pVar->addField_gFunc('base', 'official', 'enum', 'Oficiálny test', array('sk_field_info'=>'::Oficiálny alebo neoficiálny test.', 'sk_default_value'=>'no', 'not_null'=>'yes'));	
	$items_pVar->setFormRule_gFunc('edit_item', 'official', 'static');
	$items_pVar->setFormRule_gFunc('add_item', 'official', 'edit');	
	$items_pVar->addEnum_gFunc('official', 'yes', 'áno');
	$items_pVar->addEnum_gFunc('official', 'no', 'nie');
	

	$items_pVar->addField_gFunc('base', 'parametric', 'enum', 'Parametrický test', array('sk_field_info'=>'::Parametrický test generuje otázky pri každom spustení.', 'sk_default_value'=>'yes', 'not_null'=>'yes'));	
	$items_pVar->setFormRule_gFunc('edit_item', 'parametric', 'static');
	$items_pVar->setFormRule_gFunc('add_item', 'parametric', 'edit');
	$items_pVar->addEnum_gFunc('parametric', 'yes', 'áno');
	$items_pVar->addEnum_gFunc('parametric', 'no', 'nie');
		
	
	$items_pVar->addField_gFunc('base', 'access_key', 'xvarchar', 'Prístupový kód', array('sk_field_info'=>'::Heslo, ktoré je nutné zadať pri spustení oficiálneho testu.'));	
	$items_pVar->setFormRule_gFunc('edit_item', 'access_key', 'edit');
	$items_pVar->setFormRule_gFunc('add_item', 'access_key', 'edit');

	$items_pVar->addField_gFunc('base', 'time_start', 'xvarchar', 'Čas dostupnosti (začiatok)', array('sk_field_info'=>'::Dátum a čas, odkedy bude test spustiteľný.'));	
	$items_pVar->setFormRule_gFunc('edit_item', 'time_start', 'edit');
	$items_pVar->setFormRule_gFunc('add_item', 'time_start', 'edit');
	
	$items_pVar->addField_gFunc('base', 'time_stop', 'xvarchar', 'Čas dostupnosti (koniec)', array('sk_field_info'=>'::Dátum a čas, dokedy bude test spustiteľný.'));	
	$items_pVar->setFormRule_gFunc('edit_item', 'time_stop', 'edit');
	$items_pVar->setFormRule_gFunc('add_item', 'time_stop', 'edit');
	
	
	$items_pVar->apply_gFunc();
	
	stamp_gFunc('kega', 'kega update-2010-10-18xx');
}

if(!isStamp_gFunc('kega', 'kega update-2010-10-18xxx')) {
	$sql_pVar = '
		CREATE TABLE `kega_template_questions` (
		  `template_id` INT(10) UNSIGNED NOT NULL,
		  `db_question_id` INT(10) UNSIGNED NOT NULL,
		  `question_order` INT(11) NOT NULL,
		  `db_answer_ids` VARCHAR(255) NOT NULL,
		  PRIMARY KEY  (`template_id`,`db_question_id`)
		)';
	
	if(!db_public_gClass::execute_gFunc($sql_pVar, __FILE__, __LINE__)) {
		return(false);
	}

	$sql_pVar = 'INSERT INTO `%titems_test_templates__access`(`access_rule_id`,`access_type`,`access_filter`,`access_field`,`access_rights`) 
		VALUES ( NULL, \'get\',\'official=yes\',NULL,\'s_test_list_official_tests\')';
	if(!db_public_gClass::execute_gFunc($sql_pVar, __FILE__, __LINE__)) {
		return(false);
	}
	
	stamp_gFunc('kega', 'kega update-2010-10-18xxx');
}



