<?xml version="1.0" encoding="utf-8" ?>
<truEngine-document xmlns:te="http://www.truengine.sk" te:access="s_system_show_roles">

<te:content-set name="title" value="@L{Roly}" />

<te:source name="list_roles" varname="roles" />


<p>
	<a href="#" onclick="$('add_user_to_role').style.display='block'; return(false);">@L{Pridať používateľa do roly}</a>
	<div id="add_user_to_role" style="display:none;" class="msg_info">
		@L{Používateľa môžete pridať do roly nastavením roly v používateľovom profile. Používateľ môže byť priradený iba do jednej roly.}
	</div>
</p>

<te:for each="@{roles} as @{role}">
	<h3>@{role:title}</h3>
	<p>
		<te:choose test="@{role:name}">
			<te:when case="superuser">
				@L{Používateľ s neobmedzenými právami. Jeho práva nie je možné obmedziť žiadnými nastaveniami.}
				<br /><a href="/pouzivatelia/administratori?filter=%26user_role%3D@{role:name}" te:access="s_users_show_userlist" te:print="no"><te:string id="Zobraziť používateľov roly '@{role:title}'" /></a>
				<a href="/pouzivatelia/prava/rola?rola=@{role:name}" style="margin-left:30px" te:access="s_system_show_role_rights" te:print="no"><te:string id="Zobraziť práva roly '@{role:title}'" /></a>
			</te:when>
			
			<te:when case="admin">
				@L{Používateľ s administrátorskými právami. Väčšinu vecí má povolených. Práva je možné obmedziť podľa potreby.}
				<br /><a href="/pouzivatelia/administratori?filter=%26user_role%3D@{role:name}" te:access="s_users_show_userlist" te:print="no"><te:string id="Zobraziť používateľov roly '@{role:title}'" /></a>
				<a href="/pouzivatelia/prava/rola?rola=@{role:name}" style="margin-left:30px" te:access="s_system_show_role_rights" te:print="no"><te:string id="Zobraziť práva roly '@{role:title}'" /></a>
			</te:when>
			
			<te:when case="none">
				@L{Používateľ bez roly. Nemá žiadne priradené práva. V prípade potreby je mu možné priradiť minimálne práva.}
				<br /><a href="/pouzivatelia?filter=%26user_role%3D@{role:name}" te:access="s_users_show_userlist" te:print="no"><te:string id="Zobraziť používateľov roly '@{role:title}'" /></a>
				<a href="/pouzivatelia/prava/rola?rola=@{role:name}" style="margin-left:30px" te:access="s_system_show_role_rights" te:print="no"><te:string id="Zobraziť práva roly '@{role:title}'" /></a>
			</te:when>

			<te:when case="pedagog">
				@L{Používateľ s právami editovania otázok a testov.}
				<br /><a href="/pouzivatelia/pedagogovia" te:access="s_users_show_userlist" te:print="no"><te:string id="Zobraziť používateľov roly '@{role:title}'" /></a>
				<a href="/pouzivatelia/prava/rola?rola=@{role:name}" style="margin-left:30px" te:access="s_system_show_role_rights" te:print="no"><te:string id="Zobraziť práva roly '@{role:title}'" /></a>
			</te:when>

			<te:when case="student_navrhovatel">
				@L{Používateľ si môže spúštať testy, a zadávať predotázky, ktoré podliehajú schváleniu pedagógom.}
				<br /><a href="/pouzivatelia/studenti?filter=%26user_role%3D@{role:name}" te:access="s_users_show_userlist" te:print="no"><te:string id="Zobraziť používateľov roly '@{role:title}'" /></a>
				<a href="/pouzivatelia/prava/rola?rola=@{role:name}" style="margin-left:30px" te:access="s_system_show_role_rights" te:print="no"><te:string id="Zobraziť práva roly '@{role:title}'" /></a>
			</te:when>

			<te:when case="student">
				@L{Používateľ si môže spúštať testy.}
				<br /><a href="/pouzivatelia/studenti?filter=%26user_role%3D@{role:name}" te:access="s_users_show_userlist" te:print="no"><te:string id="Zobraziť používateľov roly '@{role:title}'" /></a>
				<a href="/pouzivatelia/prava/rola?rola=@{role:name}" style="margin-left:30px" te:access="s_system_show_role_rights" te:print="no"><te:string id="Zobraziť práva roly '@{role:title}'" /></a>
			</te:when>
			
			<te:when case="tester">
				@L{Špeciálna rola pre oficiálne testovanie. Ak si používateľ spustí oficiálny test, budú mu pridelené práva akoby mal túto rolu.}
				<br /><a href="/pouzivatelia/studenti?filter=%26user_role%3D@{role:name}" te:access="s_users_show_userlist" te:print="no"><te:string id="Zobraziť používateľov roly '@{role:title}'" /></a>
				<a href="/pouzivatelia/prava/rola?rola=@{role:name}" style="margin-left:30px" te:access="s_system_show_role_rights" te:print="no"><te:string id="Zobraziť práva roly '@{role:title}'" /></a>
			</te:when>
			
		</te:choose>
	</p>
</te:for>

<te:content-set name="pdf_icon" value="@{REQUEST_URI}" />

</truEngine-document>