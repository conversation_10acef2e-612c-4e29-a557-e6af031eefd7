@props([
    'languages',
])
<div class="js--group-item" data-index="{{ $index }}">
    <button style="float: right" class="btn btn-outline-secondary js--group-remove-button">
        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-trash" viewBox="0 0 16 16">
            <path d="M5.5 5.5A.5.5 0 0 1 6 6v6a.5.5 0 0 1-1 0V6a.5.5 0 0 1 .5-.5m2.5 0a.5.5 0 0 1 .5.5v6a.5.5 0 0 1-1 0V6a.5.5 0 0 1 .5-.5m3 .5a.5.5 0 0 0-1 0v6a.5.5 0 0 0 1 0z"/>
            <path d="M14.5 3a1 1 0 0 1-1 1H13v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V4h-.5a1 1 0 0 1-1-1V2a1 1 0 0 1 1-1H6a1 1 0 0 1 1-1h2a1 1 0 0 1 1 1h3.5a1 1 0 0 1 1 1zM4.118 4 4 4.059V13a1 1 0 0 0 1 1h6a1 1 0 0 0 1-1V4.059L11.882 4zM2.5 3h11V2h-11z"/>
        </svg>
    </button>
    <button style="float: right" class="btn btn-outline-secondary js--group-up-button">
        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-arrow-up" viewBox="0 0 16 16">
            <path fill-rule="evenodd" d="M8 15a.5.5 0 0 0 .5-.5V2.707l3.146 3.147a.5.5 0 0 0 .708-.708l-4-4a.5.5 0 0 0-.708 0l-4 4a.5.5 0 1 0 .708.708L7.5 2.707V14.5a.5.5 0 0 0 .5.5"/>
        </svg>
    </button>
    <button style="float: right" class="btn btn-outline-secondary js--group-down-button">
        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-arrow-down" viewBox="0 0 16 16">
            <path fill-rule="evenodd" d="M8 1a.5.5 0 0 1 .5.5v11.793l3.146-3.147a.5.5 0 0 1 .708.708l-4 4a.5.5 0 0 1-.708 0l-4-4a.5.5 0 0 1 .708-.708L7.5 13.293V1.5A.5.5 0 0 1 8 1"/>
        </svg>
    </button>


    <h2>{{ $index }}.</h2>
    <x-field name="answer_id" type="hidden" />
    <x-field name="odpoved" type="textarea" :languages="$languages" label="Znenie odpovede" opus="test_answers:odpoved" upload="odpoved_media">
        <x-field name="odpoved_media" type="file" :languages="$languages" label="hidden" opus="test_answers:odpoved_media" />
    </x-field>
    <x-field name="spravnost" type="select" label="Správnosť" :options="['spravne' => 'správne', 'nespravne' => 'nesprávne']" opus="test_answers:spravnost" />
    <x-field name="vysvetlenie" type="textarea" :languages="$languages" label="Vysvetlenie" opus="test_answers:odpoved_vysvetlenie" />
    <br /><hr /><br />
</div>
