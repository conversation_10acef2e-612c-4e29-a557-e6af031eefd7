<?php


class literatura_gClass
{
    static function insert_gFunc($name_pVar)
    {
        $md5_pVar = md5($name_pVar);

        $sql_pVar = 'SELECT count(`item_id`) as `c` FROM `%titems_literatura__data` WHERE `qmd5` = %s';

        $exists_pVar = db_public_gClass::getField_gFunc($sql_pVar, __FILE__, __LINE__, $md5_pVar);

        $data_pVar = array('name'=>$name_pVar, 'search_name'=>$name_pVar, 'qmd5'=>$md5_pVar);
        $data_str_pVar = '%s, %s, %s';
        if(!$exists_pVar) {
            if(preg_match('/.*(\d{4}).*/', $name_pVar, $matches_pVar)) {
                $data_pVar['year'] = $matches_pVar[1];
                $data_str_pVar .= ', %d';
            }
            db_public_gClass::insertData_gFunc('%titems_literatura__data', $data_str_pVar, $data_pVar, __FILE__, __LINE__);
        }
    }
}


