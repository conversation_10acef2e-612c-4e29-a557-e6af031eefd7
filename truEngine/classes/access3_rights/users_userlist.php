<?php



class users_userlist_gClass extends source_gClass
{
    protected function getData()
    {
        if(isset($this->params['user_type']) && $this->params['user_type'] === 'admin') {
            $users_pVar = db_session_gClass::getAdmins_gFunc();
        }
        else {
            $users_pVar = db_session_gClass::getUsers_gFunc();
        }

        foreach ($users_pVar as $k_pVar=>$v_pVar) {
            $users_pVar[$k_pVar]['login'] = '<a href="'.main_gClass::makeUrl_gFunc('/admin/pouzivatelia/useredit?item_id='.$users_pVar[$k_pVar]['user_id']).'">' .$users_pVar[$k_pVar]['login'] . '</a>';
        }


        if(modules_gClass::isModuleRegistred_gFunc('tables')) {
            $table_pVar = new table_gClass();
            $table_pVar->setData_gFunc($users_pVar);
            if(isset($this->params['columns'])) {
                $table_pVar->setColumnsFromString_gFunc($this->params['columns']);
            }

            return($table_pVar->getData());
        }

        return($onlineusers_pVar);
    }
}


class users_userlist extends users_userlist_gClass
{

}

