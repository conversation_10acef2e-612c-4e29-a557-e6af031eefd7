<?php

class x404_gClass
{
    static function doc404_gFunc($docName_pVar)
    {
        if(substr($docName_pVar, -1) === '/') {
            $docName_pVar = substr($docName_pVar, 0, -1);
        }
        $dirs_pVar = explode('/', $docName_pVar);
        if($dirs_pVar[count($dirs_pVar)-1] !== '.404') {
            // pohladam .404.tpl.xml od korena

            $array404_pVar = array();
            $webPath_pVar = main_gClass::getConfigVar_gFunc('documents_dir', 'main');
            if(fileSafe_gClass::file_exists_gFunc($webPath_pVar . '.404.tpl.xml')) {
                $array404_pVar[] = '';
            }

            $dirPath_pVar = '';
            foreach ($dirs_pVar as $v_pVar) {
                $dirPath_pVar .= $v_pVar . '/';
                if(!is_dir($webPath_pVar . $dirPath_pVar)) {
                    break;
                }
                if(fileSafe_gClass::file_exists_gFunc($webPath_pVar . $dirPath_pVar . '.404.tpl.xml')) {
                    $array404_pVar[] = $dirPath_pVar;
                }
            }

            main_gClass::setHttpErrCode_gFunc($docName_pVar, 404);

            // v poli $array404_pVar[] mam adresare, v ktorych sa nachadza .404.tpl.xml
            // vyberam od konca.
            $data_pVar = false;
            while(count($array404_pVar)) {
                $currentDir_pVar = array_pop($array404_pVar);
                $docName404_pVar = $currentDir_pVar . '.404';

                $vars_pVar = varStack_gClass::$vars;
                $vars_pVar['doc'] = $docName_pVar;
                ob_start();
                callStack_gClass::createNewStack_gFunc();
                $ret_pVar = callStack_gClass::execute_gFunc($docName404_pVar, false, $vars_pVar, true);
                callStack_gClass::destroyLastStack_gFunc();
                $data_pVar = ob_get_contents();
                ob_end_clean();
                if($ret_pVar === true || $ret_pVar == 404) {
                    break;
                }
                if($ret_pVar === 200) {
                    main_gClass::setHttpErrCode_gFunc($docName_pVar, 200);
                    break;
                }
            }
            return($data_pVar);
        }
        return(false);
    }
}
