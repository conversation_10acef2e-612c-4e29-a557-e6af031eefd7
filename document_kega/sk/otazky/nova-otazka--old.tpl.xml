<?xml version="1.0" encoding="utf-8" ?>
<truEngine-document xmlns:te="http://www.truengine.sk">

<te:if test="@{request:test_multiaddanswer_data}">
	<te:include name="/sk/otazky/odpovede-multiedit" otazka="@{request:question_id}" />
<te:else />

    <te:component component="\App\Opus\Forms\NewQuestionForm" />

	<te:source name="test_addquestion" varname="form" item_id="@{request:item_id}" status="active" default_status="active">
		<te:source-param name="submit_button_title_add">@L{Pridať otázku}</te:source-param>
		<te:source-param name="submit_button_title_update">@L{Aktualizovať otázku}</te:source-param>
	</te:source>

	<te:include name="/sk/components/form" form="@@{form}" />

	<te:if test="@{form:hidden_fields:test_questions_item_id:value}">
		<te:content-set name="title" value="@L{Editovať otázku}" />
	<te:else />
		<te:content-set name="title" value="@L{Nová otázka}" />
	</te:if>

	<te:if test="@{form:error_code} == 'ok'">
		<te:if test="@{form:hidden_fields:test_questions_item_id:value}">
			<b>@L{Znenie otázky}:</b> @{form:fieldsets:main:fields:test_questions_sk_otazka:value}
			<ul>
				<li><a href="/otazky/odpovede?otazka=@{form:vars:item_id}">@L{Zoznam odpovedí}</a></li>
				<li><a href="/otazky/editovat-otazku?item_id=@{form:vars:item_id}">@L{Editovať otázku}</a></li>
				<li style="padding-top:30px;"><a href="/otazky/">@L{Zoznam otázok}</a></li>
				<li style="padding-top:30px;"><a href="/otazky/nova-otazka">@L{Nová otázka}</a></li>
			</ul>
			<te:else />
			<te:redirect location="/otazky/odpovede-multiedit?otazka=@{form:vars:item_id}" />
			<!-- <te:include name="/sk/otazky/odpovede-multiedit" otazka="@{form:vars:item_id}" /> -->
		</te:if>
		<te:else />
		<script type="text/javascript">
			$j(document).ready(function() {
			  blockLayout();

			  var tmp_modul = $j('#test_questions_modul').val();
			  var tmp_program = $j('#test_questions_program').val();
			  var tmp_predmet = $j('#test_questions_predmet').val();
			  var tmp_kategoria = $j('#test_questions_kategoria').val();
			  var tmp_podkategoria = $j('#test_questions_podkategoria').val();

			  selectFilterChanged(test_questions_zaradenie_selector, 'modul', 'test_questions_');

			  $j('#test_questions_program').val(tmp_program);
			  selectFilterChanged(test_questions_zaradenie_selector, 'program', 'test_questions_');

			  $j('#test_questions_predmet').val(tmp_predmet);
			  selectFilterChanged(test_questions_zaradenie_selector, 'predmet', 'test_questions_');

			  $j('#test_questions_kategoria').val(tmp_kategoria);
			  selectFilterChanged(test_questions_zaradenie_selector, 'kategoria', 'test_questions_');

			  $j('#test_questions_podkategoria').val(tmp_podkategoria);
			});
		</script>
	</te:if>
</te:if>

<script type="text/javascript">
// autocomplete script
document.addEvent('domready', function() {

	var inputWord = $('test_questions_literatura');
	new Autocompleter.Request.JSON(inputWord, '@{config:runtime:web_dir}index.php?doc=@{config:runtime:language}/ajax/autocomplete&amp;ajax=true&amp;ajaxFormat=json&amp;type=items-test_questions-literatura', {
//		'indicatorClass': 'autocompleter-loading',
		'minLength': 2
	});


	var inputWord2 = $('test_questions_sk_keywords');
//	var indicator = inputWord2.getPrevious().getElement('.autocompleter-loading');
//	indicator.setStyle('display', 'none');
	new Autocompleter.Request.JSON(inputWord2, '@{config:runtime:web_dir}index.php?doc=@{config:runtime:language}/ajax/autocomplete&amp;ajax=true&amp;ajaxFormat=json&amp;type=items-test_questions-sk_keywords', {
//		'indicator': indicator,
		'multiple': true,
		'selectFirst': true,
		'selectMode': false,
		'minLength': 2
	});

	var inputWord3 = $('test_questions_en_keywords');
//	var indicator = inputWord3.getPrevious().getElement('.autocompleter-loading');
//	indicator.setStyle('display', 'none');
	new Autocompleter.Request.JSON(inputWord3, '@{config:runtime:web_dir}index.php?doc=@{config:runtime:language}/ajax/autocomplete&amp;ajax=true&amp;ajaxFormat=json&amp;type=items-test_questions-en_keywords', {
//		'indicator': indicator,
		'multiple': true,
		'selectFirst': true,
		'selectMode': false,
		'minLength': 2
	});

});
</script>

</truEngine-document>
