<?php


class db_items_gClass extends db_gClass
{
    const FORM_RESULT_OK = 'ok';
    const FORM_RESULT_DISPLAY = 'display';
    const FORM_RESULT_ERROR = 'error';

    /**
     * <PERSON><PERSON><PERSON><PERSON> zakladnu strukturu tabuliek pre novy system
     * @param string $systemName_pVar
     * @return boolean
     */
    static public function createBaseStruct_gFunc($systemName_pVar)
    {
        $info_pVar = self::getInfo_gFunc($systemName_pVar);

        $sql_pVar = "
			CREATE TABLE IF NOT EXISTS `%titems_" . $systemName_pVar . "__data` (
			  `item_id` int(10) unsigned NOT NULL auto_increment,
			  PRIMARY KEY  (`item_id`)
			) ENGINE=InnoDB;
		";
        if(!db_public_gClass::execute_gFunc($sql_pVar, __FILE__, __LINE__)) {
            return(false);
        }


        $types_pVar = array('hidden_int',
            'hidden_varchar',
            'enum', 'set',
            'int', 'float',
            'varchar', 'text', 'blob', 'email', 'url', 'password', 'xvarchar', 'xtext',
            'join',
            'date', 'time', 'datetime','timestamp',
            'imagelist', 'filelist', 'ximagelist', 'xfilelist', 'itemlist',
            'network_ip', 'network_mask', 'network_ip_mask',
            'intInterval', 'floatInterval',
            'ref',
            'user_id'
        );

        $sql_pVar = "
			CREATE TABLE IF NOT EXISTS `%titems_" . $systemName_pVar . "__fields` (
			  `field_id` int(10) unsigned NOT NULL auto_increment,
			  `tag` varchar(100) NOT NULL default '',
			  `type` enum('" . implode('\',\'', $types_pVar) . "') NOT NULL default 'int',
			  `len` int(10) unsigned default NULL COMMENT 'velkost v DB napr. varchar(255)',
			  `not_null` enum('yes','no') NOT NULL default 'no' COMMENT 'ci je priznak NOT NULL',
			  `pattern` varchar(255) default NULL COMMENT 'regularny vyraz, ktoremu musi zodpovdat vstup',
			  `min_value` varchar(100) default NULL COMMENT 'minimalna hodnota pri ciselnych poliach, minimalny pocet znakov pri retazcoch',
			  `max_value` varchar(100) default NULL COMMENT 'maximalna hodnota pri ciselnych poliach, maximalny pocet znakov pri retazcoch',
			  `url_order` int(10) unsigned default NULL COMMENT 'ak sa z tohto pola vytvara url, je tu pozicia v url od 1 do x',
			  `fieldset` int(10) unsigned default NULL COMMENT 'do ktoreho fieldsetu je zaradene toto pole. Ak je NULL, nie je zaradene v ziadnom fieldsete, a je zobrazene mimo fieldsetov.',
			  `tab` int(10) unsigned default NULL COMMENT 'do ktoreho tabu je zaradene toto pole. Ak je NULL, nie je zaradene v ziadnom tabe a je zobrazene mimo tabov.',
			  `field_order` tinyint(3) unsigned default NULL COMMENT 'poradie pola vramci fieldsetu, alebo vramci formulara alebo tabu, ak nie su pouzite fieldsety',
			  `order_by` enum('yes', 'no') DEFAULT 'no' COMMENT 'ci sa pouziva v order by',
			  `comment` varchar(255) COMMENT 'sql komentar',
			  PRIMARY KEY  (`field_id`),
			  UNIQUE KEY `tag` (`tag`)
			) ENGINE=InnoDB;
		";
        if(!db_public_gClass::execute_gFunc($sql_pVar, __FILE__, __LINE__)) {
            return(false);
        }

        $sql_pVar = "
			CREATE TABLE IF NOT EXISTS `%titems_" . $systemName_pVar . "__fieldsets` (
			  `fieldset_id` int(10) unsigned NOT NULL auto_increment,
			  `fieldset_name` varchar(255) NOT NULL default '' COMMENT 'indexovy nazov fieldsetu',
			  `fieldset_order` tinyint(4) default NULL COMMENT 'poradie fieldsetu vramci tabu alebo formulara (ak nie su pouzite taby)',
			  PRIMARY KEY  (`fieldset_id`)
			) ENGINE=InnoDB;
		";
        if(!db_public_gClass::execute_gFunc($sql_pVar, __FILE__, __LINE__)) {
            return(false);
        }

        $sql_pVar = "
			CREATE TABLE IF NOT EXISTS `%titems_" . $systemName_pVar . "__indexes` (
			  `index_name` varchar(255),
			  `index_type` enum('INDEX','FULLTEXT','UNIQUE'),
			  `index_fields` varchar(255),
			  PRIMARY KEY  (`index_name`)
			) ENGINE=InnoDB;
		";
        if(!db_public_gClass::execute_gFunc($sql_pVar, __FILE__, __LINE__)) {
            return(false);
        }

        $sql_pVar = "
			CREATE TABLE IF NOT EXISTS `%titems_" . $systemName_pVar . "__tabs` (
			  `tab_id` int(10) unsigned NOT NULL auto_increment,
			  `tab_name` varchar(255) NOT NULL default '' COMMENT 'indexovy nazov tabu',
			  `tab_order` tinyint(4) default NULL COMMENT 'poradie tabu vramci formulara',
			  PRIMARY KEY  (`tab_id`)
			) ENGINE=InnoDB;
		";
        if(!db_public_gClass::execute_gFunc($sql_pVar, __FILE__, __LINE__)) {
            return(false);
        }

        $sql_pVar = "
			CREATE TABLE IF NOT EXISTS `%titems_" . $systemName_pVar . "__forms` (
			  `form_id` int(10) unsigned NOT NULL auto_increment,
			  `form_name` varchar(255) NOT NULL default '' COMMENT 'indexovy nazov formularu',
			  PRIMARY KEY  (`form_id`)
			) ENGINE=InnoDB;
		";
        if(!db_public_gClass::execute_gFunc($sql_pVar, __FILE__, __LINE__)) {
            return(false);
        }

        $sql_pVar = "
			CREATE TABLE IF NOT EXISTS `%titems_" . $systemName_pVar . "__form_rules` (
			  `form_rule_id` int(10) unsigned NOT NULL auto_increment,
			  `form_id` int(10) NOT NULL COMMENT 'id formularu, pre ktore plati toto pravidlo',
			  `field_name` varchar(255) default NULL COMMENT 'Nazov fieldu, ktoreho sa tyka toto pravidlo',
			  `field_access` enum('static', 'edit', 'hidden','none') default 'edit' COMMENT '',
			  `field_init` enum('default', 'value') default 'default' COMMENT '',
			  `field_value` varchar(255) default NULL COMMENT 'hodnota ktora je pouzita ak field_init je value',
			  PRIMARY KEY  (`form_rule_id`)
			) ENGINE=InnoDB;
		";
        if(!db_public_gClass::execute_gFunc($sql_pVar, __FILE__, __LINE__)) {
            return(false);
        }

        $sql_pVar = "
			CREATE TABLE IF NOT EXISTS `%titems_" . $systemName_pVar . "__values` (
			  `enum_id` int(10) unsigned NOT NULL auto_increment,
			  `enum_field_id` int(11) NOT NULL default '0' COMMENT 'id fieldu z tabulky _fields',
			  `enum_value_order` int(11) NOT NULL default '0' COMMENT 'radenie',
			  `enum_field_value` varchar(255) NOT NULL default '' COMMENT 'hodnota ktora sa zapise do tabulky _data',
			  PRIMARY KEY  (`enum_id`),
			  KEY `key_enum_field_id` (`enum_field_id`)
			) ENGINE=InnoDB;
		";

        if(!db_public_gClass::execute_gFunc($sql_pVar, __FILE__, __LINE__)) {
            return(false);
        }

        $sql_pVar = "
			CREATE TABLE IF NOT EXISTS `%titems_" . $systemName_pVar . "__access` (
			  `access_rule_id` int(10) unsigned NOT NULL auto_increment,
			  `access_type` enum('insert','update','update_from','delete','get','properties') NOT NULL DEFAULT 'get',
			  `access_filter` text,
			  `access_field` varchar(255) NULL COMMENT 'tag pola, pre ktore platia tieto prava. Ak je NULL, ide o vseobecne prava',
			  `access_rights` varchar(255),
			  PRIMARY KEY  (`access_rule_id`)
			) ENGINE=InnoDB;
		";

        if(!db_public_gClass::execute_gFunc($sql_pVar, __FILE__, __LINE__)) {
            return(false);
        }

        $sql_pVar = "
			CREATE TABLE IF NOT EXISTS `%titems_" . $systemName_pVar . "__comments` (
            	`comment_id` int(10) unsigned NOT NULL auto_increment,
                `user_id` int(10) unsigned NOT NULL default '0',
                `item_id` int(10) unsigned NOT NULL default '0',
				`comment_state` enum('enabled','deleted') NOT NULL default 'enabled',
                `comment_access` enum('public','private') default NULL,
                `comment_datetime` datetime default NULL,
                `comment_language` varchar(2) default NULL,
                `comment_text` text,
                 PRIMARY KEY  (`comment_id`)
            ) ENGINE=InnoDB;
		";

        if(!db_public_gClass::execute_gFunc($sql_pVar, __FILE__, __LINE__)) {
            return(false);
        }

        $sql_pVar = "
			CREATE TABLE IF NOT EXISTS `%titems_" . $systemName_pVar . "__changes` (
            	`change_id` int(10) unsigned NOT NULL auto_increment,
                `user_id` int(10) unsigned NOT NULL default '0',
                `change_datetime` datetime NOT NULL default '0000-00-00 00:00:00',
                `item_id` int unsigned NULL default NULL,
                `db_id` int unsigned NULL default NULL,
                `change_type` enum('insert','update','insertcomment','updatecomment','deletecomment', 'insertvalue', 'updatevalue', 'deletevalue') default NULL,
                PRIMARY KEY  (`change_id`)
            ) ENGINE=InnoDB;
		";

        if(!db_public_gClass::execute_gFunc($sql_pVar, __FILE__, __LINE__)) {
            return(false);
        }

        $sql_pVar = "
			CREATE TABLE IF NOT EXISTS `%titems_" . $systemName_pVar . "__change_details` (
            	`change_detail_id` int(10) unsigned NOT NULL auto_increment,
                `change_id` int(10) unsigned NOT NULL default '0',
                `field_name` varchar(255) NOT NULL default '?',
                `old_value` text,
                `new_value` text,
                `value_language` varchar(2) default NULL,
                PRIMARY KEY  (`change_detail_id`)
            ) ENGINE=InnoDB;
		";

        if(!db_public_gClass::execute_gFunc($sql_pVar, __FILE__, __LINE__)) {
            return(false);
        }

        if($info_pVar['tree'] === 'yes') {
            $sql_pVar = 'CREATE TABLE IF NOT EXISTS `%titems_' . $systemName_pVar . '__tree_categories` ('
                . '`tree_id` int(10) unsigned NOT NULL auto_increment,'
                . '`tree_left_value` int(10) unsigned NOT NULL default \'0\','
                . '`tree_right_value` int(10) unsigned NOT NULL default \'0\','
                . '`tree_status` enum(\'enabled\',\'hidden\',\'disabled\') NOT NULL default \'enabled\','
                . 'PRIMARY KEY  (`tree_id`)'
                . ') ENGINE=InnoDB';

            if(self::execute_gFunc($sql_pVar, __FILE__, __LINE__) ===false) {
                return(false);
            }
            $sql_pVar = 'SELECT count(`tree_id`) as `n` FROM `%titems_' . $systemName_pVar . '__tree_categories` WHERE  `tree_id`=1';
            $n_pVar = self::getField_gFunc($sql_pVar, __FILE__, __LINE__);
            if(!$n_pVar) {
                $sql_pVar = 'INSERT INTO `%titems_' . $systemName_pVar . '__tree_categories` (`tree_id`, `tree_left_value`, `tree_right_value`, `tree_status`) VALUES(1, 0, 0, \'enabled\')';
                self::insert_gFunc($sql_pVar, __FILE__, __LINE__);
            }
        }

        if($info_pVar['tree_defs'] === 'yes') {
            $sql_pVar = 'CREATE TABLE IF NOT EXISTS `%titems_' . $systemName_pVar . '__tree__defs` ('
                . '`tree_id` int(10) unsigned NOT NULL auto_increment,'
                . '`tree_name` varchar(255) NOT NULL default \'\','
                . '`tree_def` text,'
                . 'PRIMARY KEY  (`tree_id`)'
                . ') ENGINE=InnoDB';
            if(self::execute_gFunc($sql_pVar, __FILE__, __LINE__) ===false) {
                return(false);
            }

            $sql_pVar = 'CREATE TABLE IF NOT EXISTS `%titems_' . $systemName_pVar . '__tree__values` ('
                . '`tree_rule_id` int(10) unsigned NOT NULL auto_increment,'
                . '`tree_id` int(10) unsigned NOT NULL default \'0\','
                . '`enum_id` int(10) unsigned default NULL,'
                . '`parent_value_id` int(10) unsigned default NULL,'
                . '`grandparent_value_id` int(10) unsigned default NULL,'
                . '`stats` text,'
                . 'PRIMARY KEY  (`tree_rule_id`)'
                . ') ENGINE=InnoDB';
            if(self::execute_gFunc($sql_pVar, __FILE__, __LINE__) ===false) {
                return(false);
            }
        }

        self::applyLanguagesToTables_gFunc($systemName_pVar);

        $sql_pVar = 'SELECT count(`tag`) as `n` FROM `%titems_' . $systemName_pVar . '__fields` WHERE  `tag`=\'status\'';
        $n_pVar = self::getField_gFunc($sql_pVar, __FILE__, __LINE__);
        if(!$n_pVar) {
            $sql_fields_pVar = array();
            $sql_fields_values_pVar = array();
            foreach ($info_pVar['languages'] as $language_pVar) {
                $sql_fields_pVar[] = '`'.$language_pVar.'_name`';
                $sql_fields_pVar[] = '`'.$language_pVar.'_default_value`';
                $sql_fields_pVar[] = '`'.$language_pVar.'_field_info`';
                $sql_fields_values_pVar[] = '\'Status\'';
                $sql_fields_values_pVar[] = '\'active\'';
                $sql_fields_values_pVar[] = '\'' . string_gClass::get('str_items_fieldinfo_status_pVar') . '\'';
            }
            $sql_pVar = 'INSERT INTO `%titems_' . $systemName_pVar . '__fields` '
                . '(`tag`,`type`,`not_null`,`pattern`,'.implode(',', $sql_fields_pVar).',`comment`) '
                . 'VALUES(\'status\', \'enum\', \'yes\', \'/active|deleted/\', '.implode(',', $sql_fields_values_pVar).',\'Stav polozky - aktivny, zmazany...\')';
            $id_pVar = self::insert_gFunc($sql_pVar, __FILE__, __LINE__, false, true);
            if($id_pVar === false) {
                return(false);
            }

            /*
                            $sql_fields_pVar = array();
                            $sql_fields_values_pVar = array();
                            foreach ($info_pVar['languages'] as $language_pVar) {
                                $sql_fields_pVar[] = '`'.$language_pVar.'_enum_field_name_item`';
                                $sql_fields_pVar[] = '`'.$language_pVar.'_enum_field_name_group`';
                                $sql_fields_values_pVar[] = '\'hidden\'';
                                $sql_fields_values_pVar[] = '\'hidden\'';
                            }
                            $sql_pVar = 'INSERT INTO `%titems_' . $systemName_pVar . '__values` '
                                        . '(`enum_field_id`, `enum_field_value`, '.implode(',', $sql_fields_pVar).') '
                                        . 'VALUES (%d, \'hidden\', '.implode(',', $sql_fields_values_pVar).')';
                            if(self::execute_gFunc($sql_pVar, __FILE__, __LINE__, $id_pVar) ===false) {
                                return(false);
                            }
            */
            $sql_fields_pVar = array();
            $sql_fields_values_pVar = array();
            foreach ($info_pVar['languages'] as $language_pVar) {
                $sql_fields_pVar[] = '`'.$language_pVar.'_enum_field_name_item`';
                $sql_fields_pVar[] = '`'.$language_pVar.'_enum_field_name_group`';
                $sql_fields_values_pVar[] = '\'active\'';
                $sql_fields_values_pVar[] = '\'active\'';
            }
            $sql_pVar = 'INSERT INTO `%titems_' . $systemName_pVar . '__values` '
                . '(`enum_field_id`, `enum_field_value`, '.implode(',', $sql_fields_pVar).') '
                . 'VALUES (%d, \'active\', '.implode(',', $sql_fields_values_pVar).')';
            if(self::execute_gFunc($sql_pVar, __FILE__, __LINE__, $id_pVar) ===false) {
                return(false);
            }

            $sql_fields_pVar = array();
            $sql_fields_values_pVar = array();
            foreach ($info_pVar['languages'] as $language_pVar) {
                $sql_fields_pVar[] = '`'.$language_pVar.'_enum_field_name_item`';
                $sql_fields_pVar[] = '`'.$language_pVar.'_enum_field_name_group`';
                $sql_fields_values_pVar[] = '\'deleted\'';
                $sql_fields_values_pVar[] = '\'deleted\'';
            }
            $sql_pVar = 'INSERT INTO `%titems_' . $systemName_pVar . '__values` '
                . '(`enum_field_id`, `enum_field_value`, '.implode(',', $sql_fields_pVar).') '
                . 'VALUES (%d, \'deleted\', '.implode(',', $sql_fields_values_pVar).')';
            if(self::execute_gFunc($sql_pVar, __FILE__, __LINE__, $id_pVar) ===false) {
                return(false);
            }

            $sql_fields_pVar = array();
            $sql_fields_values_pVar = array();
            foreach ($info_pVar['languages'] as $language_pVar) {
                $sql_fields_pVar[] = '`'.$language_pVar.'_form_legend`';
                $sql_fields_values_pVar[] = '\'Pridať novú položku\'';
            }
            $sql_pVar = 'INSERT INTO `%titems_' . $systemName_pVar . '__forms` '
                . '(`form_id`, `form_name`, '.implode(',', $sql_fields_pVar).') '
                . 'VALUES (1, \'add_item\', '.implode(',', $sql_fields_values_pVar).')';
            if(self::execute_gFunc($sql_pVar, __FILE__, __LINE__, $id_pVar) ===false) {
                return(false);
            }

            $sql_fields_pVar = array();
            $sql_fields_values_pVar = array();
            foreach ($info_pVar['languages'] as $language_pVar) {
                $sql_fields_pVar[] = '`'.$language_pVar.'_form_legend`';
                $sql_fields_values_pVar[] = '\'Editovať položku\'';
            }
            $sql_pVar = 'INSERT INTO `%titems_' . $systemName_pVar . '__forms` '
                . '(`form_id`, `form_name`, '.implode(',', $sql_fields_pVar).') '
                . 'VALUES (2, \'edit_item\', '.implode(',', $sql_fields_values_pVar).')';
            if(self::execute_gFunc($sql_pVar, __FILE__, __LINE__, $id_pVar) ===false) {
                return(false);
            }


        }

        $sql_pVar = 'SELECT count(`tag`) as `n` FROM `%titems_' . $systemName_pVar . '__fields` WHERE  `tag`=\'owner_id\'';
        $n_pVar = self::getField_gFunc($sql_pVar, __FILE__, __LINE__);
        if(!$n_pVar) {
            $sql_fields_pVar = array();
            $sql_fields_values_pVar = array();
            foreach ($info_pVar['languages'] as $language_pVar) {
                $sql_fields_pVar[] = '`'.$language_pVar.'_name`';
                $sql_fields_pVar[] = '`'.$language_pVar.'_default_value`';
                $sql_fields_pVar[] = '`'.$language_pVar.'_field_info`';
                $sql_fields_values_pVar[] = '\'owner id\'';
                $sql_fields_values_pVar[] = '\'0\'';
                $sql_fields_values_pVar[] = '\'' . string_gClass::get('str_items_fieldinfo_owner_id_pVar') . '\'';
            }
            $sql_pVar = 'INSERT INTO `%titems_' . $systemName_pVar . '__fields` '
                . '(`tag`,`type`,`not_null`,`pattern`,'.implode(',', $sql_fields_pVar).',`comment`) '
                . 'VALUES(\'owner_id\', \'user_id\', \'no\', \'/[0-9]+/\', '.implode(',', $sql_fields_values_pVar).',\'ID pouzivatela, ktory je majitelom polozky.\')';
            $id_pVar = self::insert_gFunc($sql_pVar, __FILE__, __LINE__, false, true);
            if($id_pVar === false) {
                return(false);
            }
        }

        if($info_pVar['garant'] === 'yes') {
            $sql_pVar = 'SELECT count(`tag`) as `n` FROM `%titems_' . $systemName_pVar . '__fields` WHERE  `tag`=\'garant_id\'';
            $n_pVar = self::getField_gFunc($sql_pVar, __FILE__, __LINE__);
            if(!$n_pVar) {
                $sql_fields_pVar = array();
                $sql_fields_values_pVar = array();
                foreach ($info_pVar['languages'] as $language_pVar) {
                    $sql_fields_pVar[] = '`'.$language_pVar.'_name`';
                    $sql_fields_pVar[] = '`'.$language_pVar.'_default_value`';
                    $sql_fields_pVar[] = '`'.$language_pVar.'_field_info`';
                    $sql_fields_values_pVar[] = '\'Garant\'';
                    $sql_fields_values_pVar[] = '\'0\'';
                    $sql_fields_values_pVar[] = '\'' . string_gClass::get('str_items_fieldinfo_garant_id_pVar') . '\'';
                }
                $sql_pVar = 'INSERT INTO `%titems_' . $systemName_pVar . '__fields` '
                    . '(`tag`,`type`,`not_null`,`pattern`,'.implode(',', $sql_fields_pVar).',`comment`) '
                    . 'VALUES(\'garant_id\', \'user_id\', \'no\', \'/[0-9]+/\', '.implode(',', $sql_fields_values_pVar).',\'ID pouzivatela, ktory je garantom polozky.\')';
                $id_pVar = self::insert_gFunc($sql_pVar, __FILE__, __LINE__, false, true);
                if($id_pVar === false) {
                    return(false);
                }
            }

            $sql_pVar = 'SELECT count(`tag`) as `n` FROM `%titems_' . $systemName_pVar . '__fields` WHERE  `tag`=\'edited\'';
            $n_pVar = self::getField_gFunc($sql_pVar, __FILE__, __LINE__);
            if(!$n_pVar) {
                $sql_fields_pVar = array();
                $sql_fields_values_pVar = array();
                foreach ($info_pVar['languages'] as $language_pVar) {
                    $sql_fields_pVar[] = '`'.$language_pVar.'_name`';
                    $sql_fields_pVar[] = '`'.$language_pVar.'_default_value`';
                    $sql_fields_pVar[] = '`'.$language_pVar.'_field_info`';
                    $sql_fields_values_pVar[] = '\'Platnosť dát\'';
                    $sql_fields_values_pVar[] = '\'yes\'';
                    $sql_fields_values_pVar[] = '\'' . string_gClass::get('str_items_fieldinfo_edited_pVar') . '\'';
                }
                $sql_pVar = 'INSERT INTO `%titems_' . $systemName_pVar . '__fields` '
                    . '(`tag`,`type`,`not_null`,`pattern`,'.implode(',', $sql_fields_pVar).',`comment`) '
                    . 'VALUES(\'edited\', \'enum\', \'yes\', \'/yes|no/\', '.implode(',', $sql_fields_values_pVar).',\'Priznak editovania. (Data boli editovane, garant musi potvrdit ich platnost.)\')';
                $id_pVar = self::insert_gFunc($sql_pVar, __FILE__, __LINE__, false, true);
                if($id_pVar === false) {
                    return(false);
                }

                $sql_fields_pVar = array();
                $sql_fields_values_pVar = array();
                foreach ($info_pVar['languages'] as $language_pVar) {
                    $sql_fields_pVar[] = '`'.$language_pVar.'_enum_field_name_item`';
                    $sql_fields_pVar[] = '`'.$language_pVar.'_enum_field_name_group`';
                    $sql_fields_values_pVar[] = '\'editované\'';
                    $sql_fields_values_pVar[] = '\'editované\'';
                }
                $sql_pVar = 'INSERT INTO `%titems_' . $systemName_pVar . '__values` '
                    . '(`enum_field_id`, `enum_field_value`, '.implode(',', $sql_fields_pVar).') '
                    . 'VALUES (%d, \'yes\', '.implode(',', $sql_fields_values_pVar).')';
                if(self::execute_gFunc($sql_pVar, __FILE__, __LINE__, $id_pVar) ===false) {
                    return(false);
                }

                $sql_fields_pVar = array();
                $sql_fields_values_pVar = array();
                foreach ($info_pVar['languages'] as $language_pVar) {
                    $sql_fields_pVar[] = '`'.$language_pVar.'_enum_field_name_item`';
                    $sql_fields_pVar[] = '`'.$language_pVar.'_enum_field_name_group`';
                    $sql_fields_values_pVar[] = '\'garantované\'';
                    $sql_fields_values_pVar[] = '\'garantované\'';
                }
                $sql_pVar = 'INSERT INTO `%titems_' . $systemName_pVar . '__values` '
                    . '(`enum_field_id`, `enum_field_value`, '.implode(',', $sql_fields_pVar).') '
                    . 'VALUES (%d, \'no\', '.implode(',', $sql_fields_values_pVar).')';
                if(self::execute_gFunc($sql_pVar, __FILE__, __LINE__, $id_pVar) ===false) {
                    return(false);
                }
            }
        }

        $sql_pVar = 'SELECT count(`tag`) as `n` FROM `%titems_' . $systemName_pVar . '__fields` WHERE  `tag`=\'insert_time\'';
        $n_pVar = self::getField_gFunc($sql_pVar, __FILE__, __LINE__);
        if(!$n_pVar) {
            $sql_fields_pVar = array();
            $sql_fields_values_pVar = array();
            foreach ($info_pVar['languages'] as $language_pVar) {
                $sql_fields_pVar[] = '`'.$language_pVar.'_name`';
                $sql_fields_pVar[] = '`'.$language_pVar.'_default_value`';
                $sql_fields_pVar[] = '`'.$language_pVar.'_field_info`';
                $sql_fields_values_pVar[] = '\'insert time\'';
                $sql_fields_values_pVar[] = 'NULL';
                $sql_fields_values_pVar[] = '\'' . string_gClass::get('str_items_fieldinfo_insert_time_pVar') . '\'';
            }
            $sql_pVar = 'INSERT INTO `%titems_' . $systemName_pVar . '__fields` '
                . '(`tag`,`type`,`not_null`,`pattern`,'.implode(',', $sql_fields_pVar).',`comment`) '
                . 'VALUES(\'insert_time\', \'datetime\', \'no\', NULL, '.implode(',', $sql_fields_values_pVar).',\'Cas vytvorenia polozky.\')';
            $id_pVar = self::insert_gFunc($sql_pVar, __FILE__, __LINE__, false, true);
            if($id_pVar === false) {
                return(false);
            }
        }

        $sql_pVar = 'SELECT count(`tag`) as `n` FROM `%titems_' . $systemName_pVar . '__fields` WHERE  `tag`=\'update_time\'';
        $n_pVar = self::getField_gFunc($sql_pVar, __FILE__, __LINE__);
        if(!$n_pVar) {
            $sql_fields_pVar = array();
            $sql_fields_values_pVar = array();
            foreach ($info_pVar['languages'] as $language_pVar) {
                $sql_fields_pVar[] = '`'.$language_pVar.'_name`';
                $sql_fields_pVar[] = '`'.$language_pVar.'_default_value`';
                $sql_fields_pVar[] = '`'.$language_pVar.'_field_info`';
                $sql_fields_values_pVar[] = '\'update time\'';
                $sql_fields_values_pVar[] = 'NULL';
                $sql_fields_values_pVar[] = '\'' . string_gClass::get('str_items_fieldinfo_update_time_pVar') . '\'';
            }
            $sql_pVar = 'INSERT INTO `%titems_' . $systemName_pVar . '__fields` '
                . '(`tag`,`type`,`not_null`,`pattern`,'.implode(',', $sql_fields_pVar).',`comment`) '
                . 'VALUES(\'update_time\', \'datetime\', \'no\', NULL, '.implode(',', $sql_fields_values_pVar).',\'Cas poslednej aktualizacie polozky pouzivatelom (cez formular).\')';
            $id_pVar = self::insert_gFunc($sql_pVar, __FILE__, __LINE__, false, true);
            if($id_pVar === false) {
                return(false);
            }
        }

        self::applyFieldsToDataTable_gFunc($systemName_pVar);

        return(true);
    }

    /**
     * Aktualizujem strukturu tabuliek podla zvolenych jazykov v hlavnej tabulke
     */
    static public function applyLanguagesToTables_gFunc($systemName_pVar)
    {
        // selectnem si jazyky
        $settings_pVar = self::getInfo_gFunc($systemName_pVar);
        if(!is_array($settings_pVar) || !isset($settings_pVar['languages'])) {
            return;
        }

        self::startTransaction_gFunc(__FILE__, __LINE__);

        // selectnem polia tabuliek
        $fields_fields_pVar = self::getFields_gFunc('items_' . $systemName_pVar . '__fields', __FILE__, __LINE__);
        $fields_fieldsets_pVar = self::getFields_gFunc('items_' . $systemName_pVar . '__fieldsets', __FILE__, __LINE__);
        $fields_tabs_pVar = self::getFields_gFunc('items_' . $systemName_pVar . '__tabs', __FILE__, __LINE__);
        $fields_forms_pVar = self::getFields_gFunc('items_' . $systemName_pVar . '__forms', __FILE__, __LINE__);
        $fields_values_pVar = self::getFields_gFunc('items_' . $systemName_pVar . '__values', __FILE__, __LINE__);
        if($settings_pVar['tree'] === 'yes') {
            $fields_tree_pVar = self::getFields_gFunc('items_' . $systemName_pVar . '__tree_categories', __FILE__, __LINE__);
        }

        foreach ($settings_pVar['languages'] as $language_pVar) {
            $languagePrefix_pVar = $language_pVar . '_';
            if($systemName_pVar === '') {
                $languagePrefix_pVar = '';
            }

            // tabulka fields
            if(!isset($fields_fields_pVar[$languagePrefix_pVar . 'name'])) {
                $sql_pVar = 'ALTER TABLE `%titems_' . $systemName_pVar . '__fields` add column `' . $languagePrefix_pVar . 'name` varchar (255) DEFAULT NULL COMMENT \'indexovy nazov pola\'';
                if(!self::execute_gFunc($sql_pVar, __FILE__, __LINE__)) {
                    self::rollback_gFunc(__FILE__, __LINE__);
                    return;
                }
            }
            if(!isset($fields_fields_pVar[$languagePrefix_pVar . 'default_value'])) {
                $sql_pVar = 'ALTER TABLE `%titems_' . $systemName_pVar . '__fields` add column `' . $languagePrefix_pVar . 'default_value` varchar (255) DEFAULT NULL COMMENT \'defaultna hodnota pola\'';
                if(!self::execute_gFunc($sql_pVar, __FILE__, __LINE__)) {
                    self::rollback_gFunc(__FILE__, __LINE__);
                    return;
                }
            }
            if(!isset($fields_fields_pVar[$languagePrefix_pVar . 'field_info'])) {
                $sql_pVar = 'ALTER TABLE `%titems_' . $systemName_pVar . '__fields` add column `' . $languagePrefix_pVar . 'field_info` text DEFAULT NULL COMMENT \'informacny retazec ktory sa zobrazuje ako napveda vo formulari\'';
                if(!self::execute_gFunc($sql_pVar, __FILE__, __LINE__)) {
                    self::rollback_gFunc(__FILE__, __LINE__);
                    return;
                }
            }

            // tabulka fieldsets
            if(!isset($fields_fieldsets_pVar[$languagePrefix_pVar . 'fieldset_legend'])) {
                $sql_pVar = 'ALTER TABLE `%titems_' . $systemName_pVar . '__fieldsets` add column `' . $languagePrefix_pVar . 'fieldset_legend` varchar (255) DEFAULT NULL COMMENT \'legend popis, ak je NULL, legend tag nebude pouzity\'';
                if(!self::execute_gFunc($sql_pVar, __FILE__, __LINE__)) {
                    self::rollback_gFunc(__FILE__, __LINE__);
                    return;
                }
            }

            // tabulka tabs
            if(!isset($fields_tabs_pVar[$languagePrefix_pVar . 'tab_legend'])) {
                $sql_pVar = 'ALTER TABLE `%titems_' . $systemName_pVar . '__tabs` add column `' . $languagePrefix_pVar . 'tab_legend` varchar (255) DEFAULT NULL COMMENT \'nadpis tabu\'';
                if(!self::execute_gFunc($sql_pVar, __FILE__, __LINE__)) {
                    self::rollback_gFunc(__FILE__, __LINE__);
                    return;
                }
            }

            // tabulka forms
            if(!isset($fields_forms_pVar[$languagePrefix_pVar . 'form_legend'])) {
                $sql_pVar = 'ALTER TABLE `%titems_' . $systemName_pVar . '__forms` add column `' . $languagePrefix_pVar . 'form_legend` varchar (255) DEFAULT NULL COMMENT \'nadpis formularu\'';
                if(!self::execute_gFunc($sql_pVar, __FILE__, __LINE__)) {
                    self::rollback_gFunc(__FILE__, __LINE__);
                    return;
                }
            }

            // tabulka values
            if(!isset($fields_values_pVar[$languagePrefix_pVar . 'enum_field_name_item'])) {
                $sql_pVar = 'ALTER TABLE `%titems_' . $systemName_pVar . '__values` add column `' . $languagePrefix_pVar . 'enum_field_name_item` varchar (255) DEFAULT NULL COMMENT \'nazov - jednotne cislo\'';
                if(!self::execute_gFunc($sql_pVar, __FILE__, __LINE__)) {
                    self::rollback_gFunc(__FILE__, __LINE__);
                    return;
                }
            }
            if(!isset($fields_values_pVar[$languagePrefix_pVar . 'enum_field_name_group'])) {
                $sql_pVar = 'ALTER TABLE `%titems_' . $systemName_pVar . '__values` add column `' . $languagePrefix_pVar . 'enum_field_name_group` varchar (255) DEFAULT NULL COMMENT \'nazov - mnozne cislo\'';
                if(!self::execute_gFunc($sql_pVar, __FILE__, __LINE__)) {
                    self::rollback_gFunc(__FILE__, __LINE__);
                    return;
                }
            }
            if(!isset($fields_values_pVar[$languagePrefix_pVar . 'enum_confirm_set'])) {
                $sql_pVar = 'ALTER TABLE `%titems_' . $systemName_pVar . '__values` add column `' . $languagePrefix_pVar . 'enum_confirm_set` varchar (255) DEFAULT NULL COMMENT \'Potvrdzujuci text pri nastaveni volby. Ak je prazdny, nepozaduje sa potvrdenie.\'';
                if(!self::execute_gFunc($sql_pVar, __FILE__, __LINE__)) {
                    self::rollback_gFunc(__FILE__, __LINE__);
                    return;
                }
            }
            if(!isset($fields_values_pVar[$languagePrefix_pVar . 'enum_confirm_unset'])) {
                $sql_pVar = 'ALTER TABLE `%titems_' . $systemName_pVar . '__values` add column `' . $languagePrefix_pVar . 'enum_confirm_unset` varchar (255) DEFAULT NULL COMMENT \'Potvrdzujuci text pri zruseni volby. Ak je prazdny, nepozaduje sa potvrdenie.\'';
                if(!self::execute_gFunc($sql_pVar, __FILE__, __LINE__)) {
                    self::rollback_gFunc(__FILE__, __LINE__);
                    return;
                }
            }
            if(!isset($fields_values_pVar[$languagePrefix_pVar . 'url_name'])) {
                $sql_pVar = 'ALTER TABLE `%titems_' . $systemName_pVar . '__values` add column `' . $languagePrefix_pVar . 'url_name` varchar (255) DEFAULT NULL COMMENT \'url hodnota\'';
                if(!self::execute_gFunc($sql_pVar, __FILE__, __LINE__)) {
                    self::rollback_gFunc(__FILE__, __LINE__);
                    return;
                }
            }

            // tabulka tree_categories
            if($settings_pVar['tree'] === 'yes') {
                $fields_pVar = array('tree_path', 'name', 'desc_short');
                foreach ($fields_pVar as $v_pVar) {
                    if(!isset($fields_tree_pVar[$languagePrefix_pVar . $v_pVar])) {
                        $sql_pVar = 'ALTER TABLE `%titems_' . $systemName_pVar . '__tree_categories` add column `' . $languagePrefix_pVar . $v_pVar . '` varchar (255) DEFAULT NULL';
                        if(!self::execute_gFunc($sql_pVar, __FILE__, __LINE__)) {
                            self::rollback_gFunc(__FILE__, __LINE__);
                            return;
                        }
                    }
                }
                if(!isset($fields_tree_pVar[$languagePrefix_pVar . 'desc_long'])) {
                    $sql_pVar = 'ALTER TABLE `%titems_' . $systemName_pVar . '__tree_categories` add column `' . $languagePrefix_pVar . 'desc_long` text DEFAULT NULL';
                    if(!self::execute_gFunc($sql_pVar, __FILE__, __LINE__)) {
                        self::rollback_gFunc(__FILE__, __LINE__);
                        return;
                    }
                }
            }
        }
        // tabulka data - tu musim riesit podla typov fieldov (varchar a text su viacjazycne)
        self::applyFieldsToDataTable_gFunc($systemName_pVar);

        self::commit_gFunc(__FILE__, __LINE__);
    }

    /**
     * Aktualizujem struktru datovej tabulky podla nastaveni v tabulke fields
     *
     * @param unknown_type $systemName_pVar
     * @return unknown
     */
    static public function applyFieldsToDataTable_gFunc($systemName_pVar)
    {
        $fields_pVar = self::getItemsFields_gFunc($systemName_pVar, false);

        $sql_pVar = 'SHOW COLUMNS FROM `%titems_'.$systemName_pVar.'__data`';
        $tableFields_pVar = self::getResultArray_gFunc($sql_pVar, __FILE__, __LINE__, false, 'Field');

        if($tableFields_pVar === null || !count($tableFields_pVar)) {
            // tabulka nie je vytvorena, alebo ina chyba
            return(false);
        }

        // selectnem si jazyky
        $settings_pVar = self::getInfo_gFunc($systemName_pVar);
        if(!is_array($settings_pVar) || !isset($settings_pVar['languages'])) {
            return;
        }

        $nLng_pVar = 0;
        $ret_pVar = true;
        foreach ($settings_pVar['languages'] as $language_pVar) {
            $nLng_pVar++;
            foreach ($fields_pVar as $v_pVar)
            {
                if($nLng_pVar > 1) {
                    if($v_pVar['type'] !== 'text' && $v_pVar['type'] !== 'varchar'
                        && $v_pVar['type'] !== 'filelist' && $v_pVar['type'] !== 'imagelist') {
                        // vsetky okrem viacjazycnych sa mi oplati iba raz prezerat (v jednom jazyku).. zbytocne by to zaberalo cas keby som to prehladal v kazdom jazyku
                        continue;
                    }
                }
                if($v_pVar['type'] === 'text' || $v_pVar['type'] === 'varchar'
                    || $v_pVar['type'] === 'filelist' || $v_pVar['type'] === 'imagelist') {
                    $prefix_pVar = $language_pVar . '_';
                }
                else {
                    $prefix_pVar = '';
                }

                if($systemName_pVar === '') {
                    $prefix_pVar = '';
                }

                if(isset($tableFields_pVar[$prefix_pVar . $v_pVar['tag']])) {
                    // pole uz existuje, ale mozno ho potrebujem aktalizovat
                    $needUpdate_pVar = false;
                    $type_pVar = self::_formatTypeForField_gFunc($systemName_pVar, $v_pVar);
                    if($tableFields_pVar[$prefix_pVar . $v_pVar['tag']]['Type'] !== $type_pVar) {
                        $needUpdate_pVar = true;
                    }
                    $defaultValue_pVar = self::_formatDefaultValueForField_gFunc($systemName_pVar, $v_pVar, $language_pVar);
                    if($tableFields_pVar[$prefix_pVar . $v_pVar['tag']]['Default'] !== $defaultValue_pVar) {
                        $needUpdate_pVar = true;
                    }

                    if($tableFields_pVar[$prefix_pVar . $v_pVar['tag']]['Null'] === 'YES') {
                        if($v_pVar['not_null'] === 'yes') {
                            $needUpdate_pVar = true;
                        }
                    }
                    else {
                        if($v_pVar['not_null'] !== 'yes') {
                            $needUpdate_pVar = true;
                        }
                    }

                    if($needUpdate_pVar) {
                        $sql_pVar = 'ALTER TABLE `%titems_'.$systemName_pVar.'__data` CHANGE `'. $prefix_pVar . $v_pVar['tag'].'` `'. $prefix_pVar  . $v_pVar['tag'].'` ';
                        $sql_pVar .= $type_pVar . ' ';
                        if($defaultValue_pVar !== NULL) {
                            $sql_pVar .= 'DEFAULT ' . $defaultValue_pVar . ' ';
                        }
                        if($v_pVar['not_null'] === 'yes') {
                            $sql_pVar .= 'NOT NULL ';
                        }
                        else {
                            $sql_pVar .= 'NULL ';
                        }

                        if($prefix_pVar . $v_pVar['tag'] === 'nekorektnost') {
 //                           dd($sql_pVar);
                        }

   //                     var_dump($sql_pVar);
                        if(!self::execute_gFunc($sql_pVar, __FILE__, __LINE__)) {
                            $ret_pVar = false;
                        }

                        if(!empty($v_pVar['comment'])) {
                            $sql_pVar .= ' COMMENT \'' . addslashes($v_pVar['comment']) . '\''; // tu pouzivam addslashes, lebo je to jednoduchsie, a je to aj secure, vzhladom na povahu zdroja dat.. neche sa mi to riesit inak
                        }
                    }
                    unset($tableFields_pVar[$prefix_pVar . $v_pVar['tag']]); // unsetnem ho, aby som ho nezmazal
                }
                else {
                    // vytvorim nove pole
                    $sql_pVar = 'ALTER TABLE `%titems_'.$systemName_pVar.'__data` add column `'.$prefix_pVar . $v_pVar['tag'].'` ';
                    $type_pVar = self::_formatTypeForField_gFunc($systemName_pVar, $v_pVar);
                    $sql_pVar .= $type_pVar . ' ';

                    // default hodnota
                    $defaultValue_pVar = self::_formatDefaultValueForField_gFunc($systemName_pVar, $v_pVar, $language_pVar);
                    if($defaultValue_pVar !== null) {
                        $sql_pVar .= 'default ' . $defaultValue_pVar;
                    }

                    // not null
                    if($v_pVar['not_null'] === 'yes') {
                        $sql_pVar .= 'NOT NULL ';
                    }
                    else {
                        $sql_pVar .= 'NULL ';
                    }

                    if(!empty($v_pVar['comment'])) {
                        $sql_pVar .= ' COMMENT \'' . addslashes($v_pVar['comment']) . '\''; // tu pouzivam addslashes, lebo je to jednoduchsie, a je to aj secure, vzhladom na povahu zdroja dat.. neche sa mi to riesit inak
                    }

                    if(!self::execute_gFunc($sql_pVar, __FILE__, __LINE__)) {
                        $ret_pVar = false;
                    }
                }
            }
        }

        // zmazem ktore netreba
        foreach ($tableFields_pVar as $v_pVar)
        {
            if($v_pVar['Field'] === 'item_id') {
                continue;
            }
            $sql_pVar = 'ALTER TABLE `%titems_'.$systemName_pVar.'__data` DROP COLUMN `' . $v_pVar['Field'] . '`';
            if(!self::execute_gFunc($sql_pVar, __FILE__, __LINE__)) {
                $ret_pVar = false;
            }
        }

        self::applyIndexesToDataTable_gFunc($systemName_pVar);

        return($ret_pVar);
    }

    static public function applyIndexesToDataTable_gFunc($systemName_pVar)
    {
        if($systemName_pVar === '') {
            return;
        }
        $sql_pVar = 'show keys from `%titems_'.$systemName_pVar.'__data`';
        $indexes_pVar =  self::getResultArray_gFunc($sql_pVar, __FILE__, __LINE__, false, 'Key_name');// indexujem podla Key_name, co znamena ze nemam sancu zistit ake ma index polia...ale viem zistit existenciu indexu pomocou isset.

        // @TODO: zatial neriesim mazanie nepotrebnych indexov, ani update uz vytvorenych. Iba vytvaranie novych, ak neexistuje ziadny s takym nazvom.

        $sql_pVar = 'select * from `%titems_'.$systemName_pVar.'__indexes`';
        $defs_pVar = self::getResultArray_gFunc($sql_pVar, __FILE__, __LINE__);

        foreach($defs_pVar as $v_pVar) {
            if(!isset($indexes_pVar[$v_pVar['index_name']])) {
                $sql_pVar = 'ALTER TABLE `%titems_'.$systemName_pVar.'__data` ADD ' . $v_pVar['index_type'] . ' `%s` (%r)';
                self::execute_gFunc($sql_pVar, __FILE__, __LINE__, array($v_pVar['index_name'], $v_pVar['index_fields']));
            }
        }

    }

    /**
     * Pomocna metoda pre metodu applyFieldsToDataTable_gFunc
     */
    static private function _formatTypeForField_gFunc($systemName_pVar, &$fieldData_pVar)
    {

        // typ a velkost
        if($fieldData_pVar['type'] == 'filelist' || $fieldData_pVar['type'] == 'imagelist'
            || $fieldData_pVar['type'] == 'xfilelist' || $fieldData_pVar['type'] == 'ximagelist'
            || $fieldData_pVar['type'] == 'itemlist') {
            $type_pVar = 'text';
        }
        elseif ($fieldData_pVar['type'] == 'xvarchar') {
            $type_pVar = 'varchar';
            $len_pVar = $fieldData_pVar['len'];
            if(!is_numeric($len_pVar) || $len_pVar <= 0) {
                $len_pVar = 255;
            }
            if(is_numeric($len_pVar) && $len_pVar>0) {
                $type_pVar .= '(' . $len_pVar . ')';
            }

        }
        elseif ($fieldData_pVar['type'] == 'xtext') {
            $type_pVar = 'text';
        }
        elseif ($fieldData_pVar['type'] == 'hidden_varchar') {
            $type_pVar = 'varchar(255)';
        }
        elseif ($fieldData_pVar['type'] == 'hidden_int') {
            $type_pVar = 'int unsigned';
        }
        elseif ($fieldData_pVar['type'] == 'ref') {
            $type_pVar = 'int unsigned';
        }
        elseif ($fieldData_pVar['type'] == 'password') {
            $type_pVar = 'varchar(255)';
        }
        elseif ($fieldData_pVar['type'] == 'email') {
            $type_pVar = 'varchar(255)';
        }
        elseif ($fieldData_pVar['type'] == 'xtext') {
            $type_pVar = 'text';
        }
        elseif ($fieldData_pVar['type'] == 'join') {
            $type_pVar = 'varchar(255)';
        }
        elseif ($fieldData_pVar['type'] == 'user_id') {
            $type_pVar = 'int unsigned';
        }
        elseif($fieldData_pVar['type'] === 'enum' || $fieldData_pVar['type'] === 'set') {
            $type_pVar = $fieldData_pVar['type'];
            if(!strlen($fieldData_pVar['len'])) {
                // musim si selectnut hodnoty
                $lenSql_pVar = 'SELECT `enum_field_value` FROM `%titems_'.$systemName_pVar.'__values` WHERE `enum_field_id` = %d';
                $lenValues_pVar = self::getResultArray_gFunc($lenSql_pVar, __FILE__, __LINE__, $fieldData_pVar['field_id']);
                $lenArray_pVar = array();
                foreach ($lenValues_pVar as $vv_pVar) {
                    $lenArray_pVar[] = '\'' . $vv_pVar['enum_field_value'] . '\'';
                }
                $len_pVar = implode(',', $lenArray_pVar);
            }
            else {
                $len_pVar = $fieldData_pVar['len'];
            }
            $type_pVar .= '(' . $len_pVar . ')';
        }
        else {
            $type_pVar = $fieldData_pVar['type'];
            $len_pVar = $fieldData_pVar['len'];
            if($type_pVar === 'varchar' && (!is_numeric($len_pVar) || $len_pVar <= 0)) {
                $len_pVar = 255;
            }
            if(is_numeric($len_pVar) && $len_pVar>0) {
                $type_pVar .= '(' . $len_pVar . ')';
            }
        }
        return($type_pVar);
    }

    /**
     * Pomocna metoda pre metodu applyFieldsToDataTable_gFunc
     */
    static private function _formatDefaultValueForField_gFunc($systemName_pVar, &$fieldData_pVar, $language_pVar = false)
    {
        $language_pVar = $language_pVar . '_';
        if($systemName_pVar === '') {
            $language_pVar = '';
        }

        // default hodnota
        if(!is_null($fieldData_pVar[$language_pVar . 'default_value'])) {
            if($fieldData_pVar['type'] === 'text' || $fieldData_pVar['type'] === 'blob') {
                return(null); // text a blob nemoze mat defaultnu hodnotu (mysql)
            }
            elseif($fieldData_pVar['type'] === 'int' || $fieldData_pVar['type'] === 'float') {
                $default_pVar = $fieldData_pVar[$language_pVar . 'default_value'] . ' ';
            }
            else {
                if(isset($fieldData_pVar[$language_pVar . 'default_value'][0]) && $fieldData_pVar[$language_pVar . 'default_value'][0] === '!') {
                    $default_pVar = substr($fieldData_pVar[$language_pVar . 'default_value'], 1) . ' ';
                }
                else {
                    $default_pVar = '\'' . $fieldData_pVar[$language_pVar . 'default_value'] . '\' ';
                }
            }
        }
        else {
            $default_pVar = null;
        }
        return($default_pVar);
    }

    /**
     * Selectne info o systeme itemov z hlavnej tabulky
     *
     * @param unknown_type $systemName_pVar
     * @return unknown
     */
    static public function getInfo_gFunc($systemName_pVar)
    {
        if($systemName_pVar === '') {
            $settings_pVar = array();
            $settings_pVar['languages'] = array('sk');
            $settings_pVar['tree'] = 'no';
            $settings_pVar['tree_defs'] = 'no';
            $settings_pVar['status'] = 'active';
            $settings_pVar['name'] = '';
            return($settings_pVar);
        }
        $settings_pVar = self::getItems_gFunc('', array('name'=>$systemName_pVar));
        $settings_pVar = reset($settings_pVar);
        if(!is_array($settings_pVar) || !isset($settings_pVar['languages'])) {
            return(false);
        }
        $settings_pVar['languages'] = explode(',', $settings_pVar['languages']);
        $lng_pVar = main_gClass::getLanguage_gFunc();
        $lng_key_pVar = array_search($lng_pVar, $settings_pVar['languages']);
        if($lng_key_pVar !== false && $lng_key_pVar != 0) {
            unset($settings_pVar['languages'][$lng_key_pVar]);
            array_unshift($settings_pVar['languages'], $lng_pVar);
        }

        return($settings_pVar);
    }

    static public function getRights_gFunc($systemName_pVar, $access_field_pVar = false, $cacheEnabled_pVar = true)
    {
        if($systemName_pVar === '') {
            return(array());
        }

        $cacheName_pVar= 'items_'.$systemName_pVar.'_rights_' . ($access_field_pVar===false ? '0':'1');

        if($cacheEnabled_pVar === true) {
            $rights_pVar = self::getCachedResult_gFunc($cacheName_pVar);
            if($rights_pVar !== false) {
                return($rights_pVar);
            }
        }

        $sql_pVar = 'SELECT * FROM `%titems_'.$systemName_pVar.'__access` WHERE ';
        if($access_field_pVar !== false) {
            $sql_pVar .= '`access_field` IS NOT NULL';
        }
        else {
            $sql_pVar .= '`access_field` IS NULL';
        }
        $rights_pVar = self::getResultArray_gFunc($sql_pVar, __FILE__, __LINE__, false);
        if($rights_pVar === true) {
            $rights_pVar = array();
        }

        self::cacheResult_gFunc($cacheName_pVar, $rights_pVar);
        return($rights_pVar);
    }

    static public function getRightsByType_gFunc($allRights_pVar, $type_pVar = 'all', $access_field_pVar = false)
    {
        if($type_pVar === 'all' && $access_field_pVar === false) {
            return($allRights_pVar);
        }

        $retRights_pVar = array();
        foreach ($allRights_pVar as $right_pVar) {
            if($right_pVar['access_type'] !== 'all' && $right_pVar['access_type'] !== $type_pVar) {
                continue;
            }
            if($access_field_pVar !== false && (empty($right_pVar['access_field']) || $right_pVar['access_field'] !== $access_field_pVar)) {
                continue;
            }
            $retRights_pVar[] = $right_pVar;
        }
        return($retRights_pVar);
    }

    /**
     * selectnem si stromovu strukturu kategorii priamo z datovej tabulky
     *
     * @param unknown_type $systemName_pVar
     */
    static public function selectAsTree_gFunc($systemName_pVar)
    {
        $info_pVar = self::getInfo_gFunc($systemName_pVar);

        $str1_pVar = array();
        foreach ($info_pVar['languages'] as $language_pVar) {
            $str1_pVar[] = 'V.' . $language_pVar . '_enum_field_name_group';
            $str1_pVar[] = 'V.' . $language_pVar . '_url_name';
        }

        $sql_pVar = 'SELECT F.field_id, F.tag, V.enum_field_value, F.url_order AS `level`, ' . implode(',', $str1_pVar) . ' from %titems_'.$systemName_pVar.'_fields AS F LEFT JOIN %titems_'.$systemName_pVar.'_values AS V ON F.field_id=V.enum_field_id WHERE F.url_order IS NOT NULL ORDER BY `level` DESC';
        $data_pVar = self::getResultArray_gFunc($sql_pVar, __FILE__, __LINE__);
        if(!$data_pVar) {
            return(false);
        }

        $tmp_pVar = array();
        $rootLevel_pVar = false;

        foreach ($data_pVar as $v_pVar) {
            $level_pVar = (int)$v_pVar['level'];
            if($rootLevel_pVar === false) {
                $rootLevel_pVar = $level_pVar;
            }
            $rootLevel_pVar = min($rootLevel_pVar, $level_pVar);

            if(!isset($tmp_pVar[$level_pVar])) {
                $tmp_pVar[$level_pVar] = array();
            }
            if(isset($tmp_pVar[$level_pVar + 1])) {
                $v_pVar['tree_childs'] = $tmp_pVar[$level_pVar + 1];
            }
            $itemData_pVar = array();
            foreach ($info_pVar['languages'] as $language_pVar) {
                $itemData_pVar[$language_pVar . '_name'] = $v_pVar[$language_pVar . '_enum_field_name_group'];
                $itemData_pVar[$language_pVar . '_tree_path'] = $v_pVar[$language_pVar . '_url_name'];
            }
            $itemData_pVar['tag_pVar'] = $v_pVar['tag'];
            $itemData_pVar['value_pVar'] = $v_pVar['enum_field_value'];
            $itemData_pVar['tree_status'] = 'enabled';
            if(isset($tmp_pVar[$level_pVar + 1])) {
                $itemData_pVar['tree_childs'] = $tmp_pVar[$level_pVar + 1];
            }

            $tmp_pVar[$level_pVar][] = $itemData_pVar;
        }

        // pozistujem, ktore skupiny obsahuju aspon jednu polozku
        self::_selectAsTree_setItemsStatus_gFunc($systemName_pVar, $tmp_pVar[$rootLevel_pVar]);

        return(array('tree_childs'=>$tmp_pVar[$rootLevel_pVar]));
    }

    /**
     * pomocna funkcia pre selectAsTree
     *
     * V dodanej stromovej strukture nastavi poloykam status enabled/disabled v zavislosti od toho ci obsahuju nejake
     * polozky alebo nie.
     *
     * @param unknown_type $systemName_pVar
     * @param unknown_type $data_pVar
     * @param unknown_type $sqlCondition_pVar
     */
    static private function _selectAsTree_setItemsStatus_gFunc($systemName_pVar, &$data_pVar, $sqlCondition_pVar = '')
    {
        if(count($data_pVar) == 1 && isset($data_pVar['tree_childs'])) {
            // root
            self::_selectAsTree_setItemsStatus_gFunc($systemName_pVar, $data_pVar['tree_childs'], $sqlCondition_pVar);
            return;
        }

        foreach ($data_pVar as $k_pVar=>$v_pVar) {
            $tmp_pVar = $sqlCondition_pVar;
            if(strlen($tmp_pVar)) {
                $tmp_pVar .= ' AND ';
            }
            $tmp_pVar .= '`'.$data_pVar[$k_pVar]['tag_pVar'] . '`=\'' . $data_pVar[$k_pVar]['value_pVar'].'\'';
            unset($data_pVar[$k_pVar]['tag_pVar']);
            unset($data_pVar[$k_pVar]['value_pVar']);

            $sql_pVar = 'SELECT item_id FROM %titems_'.$systemName_pVar.'_data WHERE `status`<>\'deleted\' AND `group`=\'product\' AND '.$tmp_pVar.' LIMIT 0,1';
            $status_pVar = self::getField_gFunc($sql_pVar, __FILE__, __LINE__);
            if($status_pVar && $status_pVar !== true) {
                $data_pVar[$k_pVar]['tree_status'] = 'enabled';
            }
            else {
                $data_pVar[$k_pVar]['tree_status'] = 'disabled';
            }

            if(isset($data_pVar[$k_pVar]['tree_childs'])) {
                self::_selectAsTree_setItemsStatus_gFunc($systemName_pVar, $data_pVar[$k_pVar]['tree_childs'], $tmp_pVar);
            }
        }
    }

    /**
     * Zacachuje data do stromovej struktury
     * @param unknown_type $systemName_pVar
     * @param unknown_type $dbTree_pVar
     * @return unknown
     */
    static public function cacheDataToTree_gFunc($systemName_pVar, $treeName_pVar)
    {

        // selectnem si stromovu strukturu poloziek
        $treeData_pVar = self::selectAsTree_gFunc($systemName_pVar);
        if(!is_array($treeData_pVar)) {
            return(false);
        }
        self::tree_saveAsChilds_gFunc('items_' . $systemName_pVar . '_tree_' . $treeName_pVar, 1, $treeData_pVar);
    }

    /**
     * Vrati obsah tabulky fields indexovane podla tag
     * @param unknown_type $systemName_pVar
     * @return unknown
     */
    static public function getItemsFields_gFunc($systemName_pVar, $cacheEnabled_pVar = true)
    {
        if($cacheEnabled_pVar === true) {
            $fields_pVar = self::getCachedResult_gFunc('items_'.$systemName_pVar.'_fields');
            if($fields_pVar !== false) {
                return($fields_pVar);
            }
        }

        $sql_pVar = 'SELECT * FROM `%titems_'.$systemName_pVar.'__fields`';
        $fields_pVar = self::getResultArray_gFunc($sql_pVar, __FILE__, __LINE__, false, 'tag');

        self::cacheResult_gFunc('items_'.$systemName_pVar.'_fields', $fields_pVar);
        return($fields_pVar);
    }

    /**
     * Vrati obsah tabulky fields kde url_order<>NULL indexovane podla url_order
     * @param unknown_type $systemName_pVar
     * @return unknown
     */
    static public function getItemsFieldsURL_gFunc($systemName_pVar)
    {
        $fields_pVar = self::getCachedResult_gFunc('items_'.$systemName_pVar.'_fields_url');
        if($fields_pVar !== false) {
            return($fields_pVar);
        }

        $tmp_pVar = self::getItemsFields_gFunc($systemName_pVar);
        $fields_pVar = array();
        foreach ($tmp_pVar as $k_pVar=>$v_pVar) {
            if($v_pVar['url_order'] !== null) {
                $fields_pVar[intval($v_pVar['url_order'])] = $v_pVar;
            }
        }

        // este zosortujem
        ksort($fields_pVar);

        self::cacheResult_gFunc('items_'.$systemName_pVar.'_fields_url', $fields_pVar);
        return($fields_pVar);
    }


    static public function getEnumFieldsValues_gFunc($systemName_pVar, $where_str_pVar = '', $where_data_pVar = array(), $columns_pVar = false, $index_by_pVar = false, $order_by_pVar = false)
    {
        if($columns_pVar === false) {
            $columns_pVar = '*';
        }
        $sql_pVar = 'SELECT ' . $columns_pVar . ' FROM `%titems_'.$systemName_pVar.'__values`';
        if(!empty($where_str_pVar)) {
            $sql_pVar .= ' WHERE ' . $where_str_pVar;
        }
        if($order_by_pVar === false) {
            $sql_pVar .= ' ORDER BY `enum_value_order`';
        }
        else {
            if($order_by_pVar[1] !== 'DESC') {
                $order_by_pVar[1] = 'ASC';
            }
            $sql_pVar .= ' ORDER BY `'.$order_by_pVar[0].'` '.$order_by_pVar[1];
        }

        $values_pVar = self::getResultArray_gFunc($sql_pVar, __FILE__, __LINE__, $where_data_pVar, $index_by_pVar);

        return($values_pVar);
    }

    static public function getItem_gFunc($systemName_pVar, $itemId_pVar)
    {
        $itemId_pVar = (int)$itemId_pVar;
        $items_pVar = self::getItems_gFunc($systemName_pVar, array('item_id'=>$itemId_pVar));
        if(is_array($items_pVar) && count($items_pVar)) {
            return(reset($items_pVar));
        }
        return(false);
    }

    /**
     * Checkuje prava.
     * Ak nenajde ziadne zodpovedajuce pravidlo, pristup je zakazany.
     * Aj najde jedno zodpovedajuce pravidlo, pristup je povoleny.
     *
     * @param unknown_type $systemName_pVar
     * @param unknown_type $accessType_pVar
     * @param unknown_type $filterData_pVar
     * @return unknown
     */
    static function checkRights_gFunc($systemName_pVar, $accessType_pVar, $filterData_pVar = array(), $access_field_pVar = false, $disableWarnings_pVar = false)
    {
        if($systemName_pVar === '' && $accessType_pVar === 'get') {
            return(true);
        }

        if(session_gClass::isFullAccess_gFunc()) {
            return(true);
        }

        $rights_pVar = self::getRights_gFunc($systemName_pVar, $access_field_pVar);
        $rights_pVar = self::getRightsByType_gFunc($rights_pVar, $accessType_pVar, $access_field_pVar);

        if($access_field_pVar !== false && !count($rights_pVar)) {
            // ak checkujem prava pre field, tak je pravo default povolene (na rozdiel od prava na celeho zaznamu)
            return(true);
        }

        $rulesOK_pVar = array();
        $rulesFAIL_pVar = array();
        $access_enabled_pVar = false;
        //echo '<pre>'; print_r($rights_pVar); echo '</pre>';
        if(count($rights_pVar)) { // prava overujem iba ak su na to pravidla.. Ak nie su, tak je pristup zakazany
            // ak jedno z pravidiel je platne, tak potom je pristup povoleny
            foreach ($rights_pVar as $right_pVar) {
                // skontrolujem filter
                if(!empty($right_pVar['access_filter'])) {
                    $access_filter_pVar = self::parseAccessFilter_gFunc($right_pVar['access_filter']);
                    foreach ($access_filter_pVar as $access_filter_value_pVar) {
                        if(!isset($filterData_pVar[$access_filter_value_pVar[0]])) {
                            // polozka nezodpoveda filtru, takze ju ignorujem
                            continue 2;
                        }

                        switch($access_filter_value_pVar['operator']) {
                            case '=':
                                if($filterData_pVar[$access_filter_value_pVar[0]] != $access_filter_value_pVar[1]) {
                                    // polozka nezodpoveda filtru, takze ju ignorujem
                                    continue 3;
                                }
                                break;
                            case '<>':
                                if($filterData_pVar[$access_filter_value_pVar[0]] == $access_filter_value_pVar[1]) {
                                    // polozka nezodpoveda filtru, takze ju ignorujem
                                    continue 3;
                                }
                                break;
                            case '<=':
                                if($filterData_pVar[$access_filter_value_pVar[0]] > $access_filter_value_pVar[1]) {
                                    // polozka nezodpoveda filtru, takze ju ignorujem
                                    continue 3;
                                }
                                break;
                            case '>=':
                                if($filterData_pVar[$access_filter_value_pVar[0]] < $access_filter_value_pVar[1]) {
                                    // polozka nezodpoveda filtru, takze ju ignorujem
                                    continue 3;
                                }
                                break;
                        }
                    }
                    // zodpoveda filtru. Vykonavanie pokracuje, prava sa vyhodnotia.
                }
                // vyhodnotenie prav
                if(session_gClass::userHasRightsInfo_gFunc(constant($right_pVar['access_rights']))) {
                    $rulesOK_pVar[] = $right_pVar;
                    $access_enabled_pVar = true;
                }
                else {
                    $rulesFAIL_pVar[] = $right_pVar;
                }
            }
        }

        if(!count($rulesOK_pVar) && !count($rulesFAIL_pVar)) {
            // nie su ziadne prava nastavene... urobim warning.. Ale ak checkujem field, tak je to ok, a pristup povolim
            if($access_field_pVar !== false) {
                return(true);
            }
            error_gClass::warning_gFunc(__FILE__, __LINE__, string_gClass::get('str_items_err_rights_undefined_sVar', $systemName_pVar, $accessType_pVar));
            message_gClass::warning_gFunc(string_gClass::get('str_items_err_rights_undefined_sVar', $systemName_pVar, $accessType_pVar), true);
        }

        if(!$access_enabled_pVar) {
            // prava nie su ok, iba ak bolo najdene aspon jedno pravidlo
            if((count($rulesOK_pVar) || count($rulesFAIL_pVar))) {
                /// tu zalogujem.. asi to pravidlom ktore je najspecifickejsie... to bude este fuska.
                //session_gClass::userHasRightsAccessAction_gFunc(constant($right_pVar['access_rights']), false, 0, $filter_pVar)
            }
            else {
                if(session_gClass::userHasRightsAccessAction_gFunc(s_system_admin)) {
                    return(true);
                }
            }
            if(!$disableWarnings_pVar && in_array($accessType_pVar, array('delete', 'insert', 'update', 'update_from'))) {
                if($access_field_pVar !== false) {
                    error_gClass::warning_gFunc(__FILE__, __LINE__, string_gClass::get('str_items_err_rights_field_failed_sVar', $systemName_pVar, $accessType_pVar, $access_field_pVar));
                    message_gClass::warning_gFunc(string_gClass::get('str_items_err_rights_field_failed_sVar', $systemName_pVar, $accessType_pVar, $access_field_pVar), true);
                }
                else {
                    error_gClass::warning_gFunc(__FILE__, __LINE__, string_gClass::get('str_items_err_rights_failed_sVar', $systemName_pVar, $accessType_pVar));
                    message_gClass::warning_gFunc(string_gClass::get('str_items_err_rights_failed_sVar', $systemName_pVar, $accessType_pVar), true);
                }
            }
            return(false);
        }
        return(true);
    }

    private static function parseAccessFilter_gFunc($filter_string_pVar)
    {
        $filter_pVar = explode('&', $filter_string_pVar);
        foreach($filter_pVar as $k_pVar=>$v_pVar) {
            if(strpos($v_pVar, '>=')) {
                $filter_pVar[$k_pVar] = explode('>=', $v_pVar);
                $filter_pVar[$k_pVar]['operator'] = '>=';
            }
            elseif(strpos($v_pVar, '<=')) {
                $filter_pVar[$k_pVar] = explode('<=', $v_pVar);
                $filter_pVar[$k_pVar]['operator'] = '<=';
            }
            elseif(strpos($v_pVar, '=')) {
                $filter_pVar[$k_pVar] = explode('=', $v_pVar);
                $filter_pVar[$k_pVar]['operator'] = '=';
            }
            elseif(strpos($v_pVar, '<>')) {
                $filter_pVar[$k_pVar] = explode('<>', $v_pVar);
                $filter_pVar[$k_pVar]['operator'] = '<>';
            }
            else {
                unset($filter_pVar[$k_pVar]);
            }

            // replace values
            if(isset($filter_pVar[$k_pVar])) {
                $filter_pVar[$k_pVar][1] = preg_replace('/\@\{session\:user_id\}/', session_gClass::getUserDetail_gFunc('user_id'), $filter_pVar[$k_pVar][1]);
            }

            $filter_pVar[$k_pVar][0] = trim($filter_pVar[$k_pVar][0]);
            $filter_pVar[$k_pVar][1] = trim($filter_pVar[$k_pVar][1]);
        }
        return($filter_pVar);
    }

    static function preparePager(String $filterPager, String $systemName_pVar)
    {
        $pager_pVar = explode(',', $filterPager);
        if(strlen($pager_pVar[1]) && intval($pager_pVar[1]) >= 0) {
            $pager_pVar[1] = intval($pager_pVar[1]) - 1;
        }

        if($systemName_pVar === 'test_questions') {
            if($pager_pVar[1] < 0) {
                $pager_pVar[1] = 0;
            }
        }

        if($pager_pVar[1] < 0) {
            $pager_pVar[0] = 10000;
            $pager_pVar[1] = 0;
        }

        return $pager_pVar;
    }

    /**
     * selectne itemy podla filtra (filter musi byt prepared)
     *
     * @param unknown_type $systemName_pVar
     * @param unknown_type $filter_pVar
     * @return unknown
     */
    static function getItems_gFunc($systemName_pVar, $filter_pVar = array(), $readExtInfo_pVar = true)
    {
        if($filter_pVar === false) {
            return(false);
        }

        // get data format type from filter
        $data_format_pVar = self::readDataFormatParameters_gFunc($filter_pVar);

        // prepare pager
        $pager_pVar = false;
        if(isset($filter_pVar['pager'])) {
            $pager_pVar = self::preparePager($filter_pVar['pager'], $systemName_pVar);
            unset($filter_pVar['pager']);
        }


        $info_pVar = self::getInfo_gFunc($systemName_pVar);

        /**
         * dd($info_pVar);
         *
         * array:5 [▼
         *      "languages" => array:1 [▼
         *          0 => "sk"
         *      ]
         *      "tree" => "no"
         *      "tree_defs" => "no"
         *      "status" => "active"
         *      "name" => ""
         * ]
         */

        $refs_pVar = array();

        $order_by_fields_pVar = array();
        $currentLanguage_pVar = main_gClass::getLanguage_gFunc();
        // prava overujem az na konci, a nie tu.. aby zabrali vsetky podmienky filtra

        // vytvorim si zoznam stlpcov (SELECT *) a LEFT JOINov
        $fields_pVar = self::getItemsFields_gFunc($systemName_pVar);


//        if($systemName_pVar=='test_questions') {
//            dd($fields_pVar);
//        }

        $columns_pVar = array('`D`.`item_id`');
        $joins_pVar = array();
        $joinTables_pVar = array();
        $njoin_pVar = 0; $njoinx_pVar = 0;
        foreach ($fields_pVar as $k_pVar=>$v_pVar) {
            if(isset($v_pVar['order_by']) && $v_pVar['order_by'] == 'yes') {
                if(isset($v_pVar[$currentLanguage_pVar . '_name'])) {
                    $order_by_fields_pVar[$v_pVar['tag']] = $v_pVar[$currentLanguage_pVar . '_name'];
                }
                elseif(isset($v_pVar['name'])) {
                    $order_by_fields_pVar[$v_pVar['tag']] = $v_pVar['name'];
                }
                else {
                    $order_by_fields_pVar[$v_pVar['tag']] = $v_pVar['tag'];
                }
            }

            $njoin_pVar = $njoinx_pVar;
            $nlanguages_pVar = 0;
            foreach ($info_pVar['languages'] as $language_pVar) {
                $nlanguages_pVar++;
                if($v_pVar['type'] === 'varchar' || $v_pVar['type'] === 'text'
                    || $v_pVar['type'] === 'filelist' || $v_pVar['type'] === 'imagelist') {
                    $lngPrefix_pVar = $language_pVar . '_';
                }
                else {
                    $lngPrefix_pVar = '';
                }
                $column_pVar = '`D`.`' . $lngPrefix_pVar . $v_pVar['tag'] . '`';
                if(array_search($column_pVar, $columns_pVar) === false) {
                    $columns_pVar[] = $column_pVar;
                }
                if($v_pVar['type'] === 'enum') {
                    $column_pVar = '`TX'.$njoin_pVar.'`.`'.$language_pVar.'_enum_field_name_item` AS `'. $language_pVar . '_' . $v_pVar['tag'].'_enum_name_item`';
                    if(array_search($column_pVar, $columns_pVar) === false) {
                        $columns_pVar[] = $column_pVar;
                    }
                    if($njoin_pVar === $njoinx_pVar) {
                        $joins_pVar[] = ' LEFT JOIN `%titems_' . $systemName_pVar . '__values` as `TX'.$njoin_pVar.'` ON `TX'.$njoin_pVar.'`.`enum_field_id`=' . $v_pVar['field_id'] . ' AND `TX'.$njoin_pVar.'`.`enum_field_value` = `D`.`' . $lngPrefix_pVar . $v_pVar['tag'] . '` ';
                    }
                    $njoinx_pVar++;
                }
                if($v_pVar['type'] === 'join' && $nlanguages_pVar === 1) {
                    $p_pVar = strpos($v_pVar['comment'], '|');
                    if($p_pVar !== false) {
                        $joins_pVar[] = ' ' . substr($v_pVar['comment'], $p_pVar + 1);
                        $columns_pVar[] = substr($v_pVar['comment'], 0, $p_pVar);
                        $tmpx_pVar = explode('|', $v_pVar['pattern']);
                        if(is_array($tmpx_pVar) && count($tmpx_pVar) >= 2) {
                            $joinTables_pVar[$tmpx_pVar[0]] = $tmpx_pVar[1];
                        }
                    }
                    else {
                        $joins_pVar[] = ' ' . $v_pVar['comment'];
                    }
                }
                if($v_pVar['type'] === 'ref' && $nlanguages_pVar === 1) {
                    $refDef_pVar = $v_pVar['pattern'];
                    $refDef_pVar = explode(' as ', $refDef_pVar);
                    if(!isset($refDef_pVar[1])) {
                        $refDef_pVar[1] = $refDef_pVar[0];
                    }
                    $refDef_pVar[0] = trim($refDef_pVar[0]);
                    $refDef_pVar[1] = trim($refDef_pVar[1]);
                    $refs_pVar[$refDef_pVar[1]] = $refDef_pVar[1];

                    $joins_pVar[] = ' LEFT JOIN `%titems_'.$refDef_pVar[0].'__data` as `'.$refDef_pVar[1].'` on `'.$refDef_pVar[1].'`.`item_id` = `D`.`'.$v_pVar['tag'].'`';
                    $columns_pVar[] = '`'.$refDef_pVar[1].'`.*';
                }
            }
        }


        // pre zakladny system sa toto neaplikuje
        if($systemName_pVar === '') {
            $columns_pVar = array('*');
            $joins_pVar = array('');
        } else {
            $columns_pVar[] = '`D`.`item_id` as `item_id`';
        }


            /**
             * dd($columns_pVar);
             *
             * array:68 [▼
             * 0 => "`D`.`item_id`"
             * 1 => "`D`.`status`"
             * 2 => "`TX0`.`sk_enum_field_name_item` AS `sk_status_enum_name_item`"
             * 3 => "`TX0`.`en_enum_field_name_item` AS `en_status_enum_name_item`"
             * 4 => "`TX0`.`cz_enum_field_name_item` AS `cz_status_enum_name_item`"
             * 5 => "`D`.`owner_id`"
             * 6 => "`D`.`garant_id`"
             * 7 => "`D`.`edited`"
             * 8 => "`TX3`.`sk_enum_field_name_item` AS `sk_edited_enum_name_item`"
             * 9 => "`TX3`.`en_enum_field_name_item` AS `en_edited_enum_name_item`"
             * 10 => "`TX3`.`cz_enum_field_name_item` AS `cz_edited_enum_name_item`"
             * 11 => "`D`.`insert_time`"
             * 12 => "`D`.`update_time`"
             * 13 => "`D`.`sk_otazka`"
             * 14 => "`D`.`en_otazka`"
             * 15 => "`D`.`cz_otazka`"
             * 16 => "`D`.`sk_otazka_media`"
             * 17 => "`D`.`en_otazka_media`"
             * 18 => "`D`.`cz_otazka_media`"
             * 19 => "`D`.`moznosti`"
             * 20 => "`answers`.*"
             * 21 => "`D`.`modul`"
             * 22 => "`TX6`.`sk_enum_field_name_item` AS `sk_modul_enum_name_item`"
             * 23 => "`TX6`.`en_enum_field_name_item` AS `en_modul_enum_name_item`"
             * 24 => "`TX6`.`cz_enum_field_name_item` AS `cz_modul_enum_name_item`"
             * 25 => "`D`.`program`"
             * 26 => "`TX9`.`sk_enum_field_name_item` AS `sk_program_enum_name_item`"
             * 27 => "`TX9`.`en_enum_field_name_item` AS `en_program_enum_name_item`"
             * 28 => "`TX9`.`cz_enum_field_name_item` AS `cz_program_enum_name_item`"
             * 29 => "`D`.`predmet`"
             * 30 => "`TX12`.`sk_enum_field_name_item` AS `sk_predmet_enum_name_item`"
             * 31 => "`TX12`.`en_enum_field_name_item` AS `en_predmet_enum_name_item`"
             * 32 => "`TX12`.`cz_enum_field_name_item` AS `cz_predmet_enum_name_item`"
             * 33 => "`D`.`kategoria`"
             * 34 => "`TX15`.`sk_enum_field_name_item` AS `sk_kategoria_enum_name_item`"
             * 35 => "`TX15`.`en_enum_field_name_item` AS `en_kategoria_enum_name_item`"
             * 36 => "`TX15`.`cz_enum_field_name_item` AS `cz_kategoria_enum_name_item`"
             * 37 => "`D`.`podkategoria`"
             * 38 => "`TX18`.`sk_enum_field_name_item` AS `sk_podkategoria_enum_name_item`"
             * 39 => "`TX18`.`en_enum_field_name_item` AS `en_podkategoria_enum_name_item`"
             * 40 => "`TX18`.`cz_enum_field_name_item` AS `cz_podkategoria_enum_name_item`"
             * 41 => "`D`.`sk_vysvetlenie`"
             * 42 => "`D`.`en_vysvetlenie`"
             * 43 => "`D`.`cz_vysvetlenie`"
             * 44 => "`D`.`literatura`"
             * 45 => "`D`.`navrhovatel`"
             * 46 => "`D`.`autorske_prava`"
             * 47 => "`D`.`sk_keywords`"
             * 48 => "`D`.`en_keywords`"
             * 49 => "`D`.`cz_keywords`"
             * 50 => "`D`.`obtiaznost`"
             * 51 => "`D`.`nekorektnost`"
             * 52 => "`D`.`aktualnost`"
             * 53 => "`D`.`spravne`"
             * 54 => "`D`.`nespravne`"
             * 55 => "`D`.`spravne_test`"
             * 56 => "`D`.`nespravne_test`"
             * 57 => "`D`.`typ`"
             * 58 => "`TX21`.`sk_enum_field_name_item` AS `sk_typ_enum_name_item`"
             * 59 => "`TX21`.`en_enum_field_name_item` AS `en_typ_enum_name_item`"
             * 60 => "`TX21`.`cz_enum_field_name_item` AS `cz_typ_enum_name_item`"
             * 61 => "`D`.`dolezitost`"
             * 62 => "`TX24`.`sk_enum_field_name_item` AS `sk_dolezitost_enum_name_item`"
             * 63 => "`TX24`.`en_enum_field_name_item` AS `en_dolezitost_enum_name_item`"
             * 64 => "`TX24`.`cz_enum_field_name_item` AS `cz_dolezitost_enum_name_item`"
             * 65 => "`D`.`literatura_md5`"
             * 66 => "`D`.`literatura_strana`"
             * 67 => "`D`.`item_id` as `item_id`"
             * ]
             *
             *
             * dd($joins_pVar);
             *
             * array:10 [▼
             * 0 => " LEFT JOIN `%titems_test_questions__values` as `TX0` ON `TX0`.`enum_field_id`=1 AND `TX0`.`enum_field_value` = `D`.`status` "
             * 1 => " LEFT JOIN `%titems_test_questions__values` as `TX3` ON `TX3`.`enum_field_id`=4 AND `TX3`.`enum_field_value` = `D`.`edited` "
             * 2 => " LEFT JOIN `%titems_test_answers__data` as `answers` ON `answers`.`test_question`=`D`.`item_id` AND `answers`.`status`<>'deleted'"
             * 3 => " LEFT JOIN `%titems_test_questions__values` as `TX6` ON `TX6`.`enum_field_id`=10 AND `TX6`.`enum_field_value` = `D`.`modul` "
             * 4 => " LEFT JOIN `%titems_test_questions__values` as `TX9` ON `TX9`.`enum_field_id`=11 AND `TX9`.`enum_field_value` = `D`.`program` "
             * 5 => " LEFT JOIN `%titems_test_questions__values` as `TX12` ON `TX12`.`enum_field_id`=12 AND `TX12`.`enum_field_value` = `D`.`predmet` "
             * 6 => " LEFT JOIN `%titems_test_questions__values` as `TX15` ON `TX15`.`enum_field_id`=13 AND `TX15`.`enum_field_value` = `D`.`kategoria` "
             * 7 => " LEFT JOIN `%titems_test_questions__values` as `TX18` ON `TX18`.`enum_field_id`=14 AND `TX18`.`enum_field_value` = `D`.`podkategoria` "
             * 8 => " LEFT JOIN `%titems_test_questions__values` as `TX21` ON `TX21`.`enum_field_id`=27 AND `TX21`.`enum_field_value` = `D`.`typ` "
             * 9 => " LEFT JOIN `%titems_test_questions__values` as `TX24` ON `TX24`.`enum_field_id`=28 AND `TX24`.`enum_field_value` = `D`.`dolezitost` "
             * ]
             *
             * dd($joinTables_pVar);
             * array:1 [▼
             * "test_answers" => "answers"
             * ]
             */



        /**************
         * v $joinTables_pVar mam aliasy tabuliek, ktore joinujem (ale iba ktore maju vo fielde nastavene pattern)
         * Ak chcem aby filter fungoval aj na tieto tabulky, je situacia trosku zlozitejsia.
         *
         * Najskor prehladam polozky filtra, ci niektora polozka zacina NAZOVJOINTABULKY_field.
         * Ak nezacina, neriesim.
         * Ak zacina, pouzijem specialny postup selectovania, aby fungoval aj pager
         * 	1. zostavim si where str, a limit (limit uz mam).
         * 	2. selectnem z datovej tabulky item_id, ktore zodpovedaju tomuto filtru a limitu (zohladnim joiny)
         *  3. where podmienku zrusim a vytvorim novu na zaklade ziskanych item_id (bude to o tom istom)
         *  4. vykonam select standardnym sposobom.
         *
         * Nemhol som to riesit jednym selectom, lebo sa pouziva syntax select * from (select * from ... where ... limit) order by
         * Toto mi navyse selectne kompletne polozky, a nie iba joiny pre ktore podmienka plati.. Co je logicky spravne. ;)
         *
         */

        list($where_str_pVar, $where_data_pVar, $order_by_pVar) = self::getFilterQuery_gFunc($filter_pVar, $systemName_pVar, $joinTables_pVar);

        $pager_where_str_pVar = $where_str_pVar;
        $pager_where_data_pVar = $where_data_pVar;

        $filterUseJoin_pVar = false;
        if(count($joinTables_pVar)) {
            foreach($joinTables_pVar as $v_pVar) {
                foreach($filter_pVar as $kk_pVar=>$vv_pVar) {
                    if(substr($kk_pVar, 0, strlen($v_pVar) + 1) == $v_pVar . '_') {
                        $filterUseJoin_pVar = true;
                        break;
                    }
                }
            }
        }

        if($filterUseJoin_pVar) {
            foreach($joinTables_pVar as $v_pVar) {
                $where_str_pVar = str_replace('`D`.`' . $v_pVar . '_', '`' . $v_pVar . '`.`', $where_str_pVar);
                $pager_where_str_pVar = str_replace('`D`.`' . $v_pVar . '_', '`' . $v_pVar . '`.`', $pager_where_str_pVar);
            }
            if(is_array($pager_pVar) && count($pager_pVar) == 2 && intval($pager_pVar[0]) > 0) {
                $page_pVar = intval($pager_pVar[1]);
                $pageLen_pVar = intval($pager_pVar[0]);
                $offset_pVar = $pageLen_pVar * $page_pVar;
                $limit_str_pVar = ' LIMIT ' . $offset_pVar . ', ' . $pageLen_pVar;

                $sql_pVar = 'SELECT DISTINCT `D`.`item_id` FROM `%titems_' . $systemName_pVar . '__data`';
                $sql_pVar .=  ' as `D` ' . implode(' ', $joins_pVar);
                $sql_pVar .= $where_str_pVar;
                $sql_pVar .= $limit_str_pVar;

                $data_pVar = self::getResultArray_gFunc($sql_pVar, __FILE__, __LINE__, $where_data_pVar);
                $item_ids_pVar = array();
                foreach($data_pVar as $v_pVar) {
                    $item_ids_pVar[] = $v_pVar['item_id'];
                }
                if(!count($item_ids_pVar)) {
                    $item_ids_pVar[] = 0;
                }
                $where_data_pVar = array($item_ids_pVar);
                $where_str_pVar = ' WHERE `D`.`item_id` IN (%ai) ';
            }
        }


        if(is_array($pager_pVar) && count($pager_pVar) == 2 && intval($pager_pVar[0]) > 0) {
            $page_pVar = intval($pager_pVar[1]);
            $pageLen_pVar = intval($pager_pVar[0]);
            $offset_pVar = $pageLen_pVar * $page_pVar;
            if($filterUseJoin_pVar) {
                $offset_pVar = 0;
            }

            $limit_str_pVar = ' LIMIT ' . $offset_pVar . ', ' . $pageLen_pVar;

            $sql_pVar = 'SELECT ' . implode(',', $columns_pVar) . ' FROM ';
            $sql_pVar .= '(SELECT * FROM `%titems_' . $systemName_pVar . '__data` AS `D`';
            $sql_pVar .= $where_str_pVar;
            $sql_pVar .= $order_by_pVar;
            $sql_pVar .= $limit_str_pVar;
            $sql_pVar .= ')';
            $sql_pVar .=  ' as `D` ' . implode(' ', $joins_pVar);
        }
        else {
            $sql_pVar = 'SELECT ' . implode(',', $columns_pVar) . ' FROM `%titems_' . $systemName_pVar . '__data`';
            $sql_pVar .=  ' as `D` ' . implode(' ', $joins_pVar);
            $sql_pVar .= $where_str_pVar;
            $pager_pVar = false;
        }


        if(!empty($order_by_pVar)) {
            $sql_pVar .= $order_by_pVar;
        }

        $cacheName = __FILE__ . ':' . __LINE__ . ':' . md5($sql_pVar) . ':' . md5(serialize($where_data_pVar));
        $data_pVar = self::getCachedResult_gFunc($cacheName);
        if($data_pVar === false) {
            // echo $sql_pVar;
            $data_pVar = self::getResultArray_gFunc($sql_pVar, __FILE__, __LINE__, $where_data_pVar, 'item_id', 1);
            //echo '<pre>'; print_r($data_pVar); echo '</pre>';
            $copyArray = array_merge([], $data_pVar);
            self::cacheResult_gFunc($cacheName, $copyArray);
        }

        // ref fieldy su ako polia obsahujuce jednu polozku, zmenim ich na obycajne premenne
        foreach ($data_pVar as $k_pVar=>$v_pVar) {
            foreach ($data_pVar[$k_pVar] as $kk_pVar=>$vv_pVar) {
                if(is_array($vv_pVar) && count($vv_pVar) == 1 && array_key_exists(0, $vv_pVar)) {
                    $i_pVar = strpos($kk_pVar, '.');
                    if($i_pVar !== false && isset($refs_pVar[substr($kk_pVar, 0, $i_pVar)])) {
                        $data_pVar[$k_pVar][$kk_pVar] = $vv_pVar[0];
                    }
                }
            }
        }

        if($readExtInfo_pVar) {
            self::readExtInfo_gFunc($data_pVar, $order_by_fields_pVar, $systemName_pVar, array('files', 'rights', 'languages'));
        }

        $data_pVar['filter'] = $filter_pVar;

        if($pager_pVar !== false) {
            $sql_pVar = 'SELECT count(DISTINCT `D`.`item_id`) as `n` FROM `%titems_' . $systemName_pVar . '__data` as `D`';
            $sql_pVar .= implode(' ', $joins_pVar);
            $sql_pVar .= $pager_where_str_pVar;
            $pager_pVar[2] = self::getField_gFunc($sql_pVar, __FILE__, __LINE__, $pager_where_data_pVar);

            $data_pVar['_pager'] = array();
            $data_pVar['_pager']['pageLen'] = $pager_pVar[0];
            $data_pVar['_pager']['currentPage'] = (int)$pager_pVar[1] + 1;
            $data_pVar['_pager']['totalItems'] = $pager_pVar[2];
            $data_pVar['_pager']['totalPages'] = ceil($data_pVar['_pager']['totalItems'] / $data_pVar['_pager']['pageLen']);
        }

        $data_pVar['_order_by_fields'] = $order_by_fields_pVar;

        if($data_format_pVar['data_format'] !== 'data') {
            $data_pVar = self::formatData_gFunc($data_pVar, $data_format_pVar);
        }

        $data_pVar['_info'] = $info_pVar;

        return $data_pVar;
    }

    static public function readExtInfo_gFunc(&$data_pVar, &$order_by_fields_pVar, $systemName_pVar, $settings_pVar = array())
    {
        if($systemName_pVar !== '') {

            $info_pVar = db_items_gClass::getInfo_gFunc($systemName_pVar);

            $readFiles_pVar = false;
            $readRights_pVar = false;
            $readUrl_pVar = false;
            $readLanguages_pVar = false;

            if(in_array('files', $settings_pVar)) {
                $readFiles_pVar = true;
            }
            if(in_array('rights', $settings_pVar)) {
                $readRights_pVar = true;
            }
            if(in_array('url', $settings_pVar)) {
                $readUrl_pVar = true;
            }
            if(in_array('languages', $settings_pVar)) {
                $readLanguages_pVar = true;
            }
            ////////////////////////////////

            if($readFiles_pVar) {
                // najskor si nacitam files
                $itemIds_pVar = array_keys($data_pVar);
                $filesTmp_pVar = main_gClass::getFiles_gFunc('items_' . $systemName_pVar . '_%', $itemIds_pVar, true);
                //echo '<pre>'; print_r($filesTmp_pVar); echo '</pre>';
//dd($data_pVar);
                ////////// a este subory pre odpovede...
                if($systemName_pVar == 'test_questions') {
                    $answer_ids_pVar = array();
                    foreach($data_pVar as $k_pVar=>$v_pVar) {
                        if(count($data_pVar[$k_pVar]['answers.item_id'])) {
                            foreach($data_pVar[$k_pVar]['answers.item_id'] as $vv_pVar) {
                                if($vv_pVar) {
                                    $answer_ids_pVar[] = $vv_pVar;
                                }
                            }
                        }
                    }
                    $filesAnswers_pVar = main_gClass::getFiles_gFunc('items_test_answers_%', $answer_ids_pVar , true);
                }
            }


            foreach ($data_pVar as $k_pVar=>$v_pVar) {

                if($readRights_pVar) {
                    if(!self::checkRights_gFunc($systemName_pVar, 'get', $v_pVar)) {
                        unset($data_pVar[$k_pVar]);
                        continue;
                    }

                    foreach($v_pVar as $kk_pVar=>$vv_pVar) {
                        if($kk_pVar == 'email'
                            && isset($v_pVar['email_public'])
                            && $v_pVar['email_public'] == 'yes'
                            && session_gClass::userHasRightsInfo_gFunc(s_system_logged_on)) {
                            continue;
                        }
                        if(!self::checkRights_gFunc($systemName_pVar, 'get', $v_pVar, $kk_pVar)) {
                            unset($data_pVar[$k_pVar][$kk_pVar]);
                            unset($order_by_fields_pVar[$kk_pVar]);
                        }
                    }
                }

                if($readUrl_pVar) {
                    $data_pVar[$k_pVar]['url_category'] = self::getUrlForItem_gFunc($systemName_pVar, $v_pVar, true, true);
                    $data_pVar[$k_pVar]['title_category'] = $data_pVar[$k_pVar]['url_category'];
                    $data_pVar[$k_pVar]['title_category'] = str_replace('/', ' - ', trim($data_pVar[$k_pVar]['title_category'], '/'));

                    $data_pVar[$k_pVar]['url_item'] = self::getUrlForItem_gFunc($systemName_pVar, $v_pVar, false, true);
                }

                if($readFiles_pVar) {
                    $fields_pVar = self::getItemsFields_gFunc($systemName_pVar);
                    $files_pVar = array();
                    foreach ($fields_pVar as $fk_pVar=>$fv_pVar) {
                        if($fv_pVar['type'] === 'filelist' || $fv_pVar['type'] === 'imagelist'
                            || $fv_pVar['type'] === 'xfilelist' || $fv_pVar['type'] === 'ximagelist') {
                            $files_pVar[$fv_pVar['tag']] = $fv_pVar['type'][0] === 'x' ? false:true;
                        }
                    }

                    if(count($files_pVar)) {
                        $lng_pVar = main_gClass::getLanguage_gFunc();
                        foreach ($files_pVar as $kk_pVar=>$vv_pVar) {
                            if($vv_pVar) {
                                foreach ($info_pVar['languages'] as $language_pVar) {
                                    //$data_pVar[$k_pVar][$language_pVar . '_' . $kk_pVar] = main_gClass::getFiles_gFunc('items_' . $systemName_pVar . '_'  . $language_pVar . '_' . $kk_pVar, $v_pVar['item_id']);
                                    if(!isset($data_pVar[$k_pVar][$language_pVar . '_' . $kk_pVar])
                                        || !is_array($data_pVar[$k_pVar][$language_pVar . '_' . $kk_pVar])) {
                                        $data_pVar[$k_pVar][$language_pVar . '_' . $kk_pVar] = array();
                                    }
                                    if(isset($filesTmp_pVar['items_' . $systemName_pVar . '_'  . $language_pVar . '_' . $kk_pVar . '_' . $v_pVar['item_id']])) {
                                        $data_pVar[$k_pVar][$language_pVar . '_' . $kk_pVar][] = $filesTmp_pVar['items_' . $systemName_pVar . '_'  . $language_pVar . '_' . $kk_pVar . '_' . $v_pVar['item_id']];
                                    }
                                    if($language_pVar === $lng_pVar) {
                                        $v_pVar[$language_pVar . '_' . $kk_pVar] = $data_pVar[$k_pVar][$language_pVar . '_' . $kk_pVar];
                                    }
                                }
                            }
                            else {
                                //$data_pVar[$k_pVar][$kk_pVar] = main_gClass::getFiles_gFunc('items_' . $systemName_pVar . '_' . $kk_pVar, $v_pVar['item_id']);
                                if(!isset($data_pVar[$k_pVar][$kk_pVar])
                                    || !is_array($data_pVar[$k_pVar][$kk_pVar])) {
                                    $data_pVar[$k_pVar][$kk_pVar] = array();
                                }
                                if(isset($filesTmp_pVar['items_' . $systemName_pVar . '_' . $kk_pVar . '_' . $v_pVar['item_id']])) {
                                    $data_pVar[$k_pVar][$kk_pVar][] = $filesTmp_pVar['items_' . $systemName_pVar . '_' . $kk_pVar . '_' . $v_pVar['item_id']];
                                }
                            }
                        }
                    }

                    if(isset($filesAnswers_pVar) && count($filesAnswers_pVar)) {
                        $lng_pVar = main_gClass::getLanguage_gFunc();
                        $languages_pVar = array_reverse($info_pVar['languages']);
                        if(!isset($data_pVar[$k_pVar]['answers.odpoved_media']) || !is_array($data_pVar[$k_pVar]['answers.odpoved_media'])) {
                            $data_pVar[$k_pVar]['answers.odpoved_media'] = array();
                        }
                        foreach($data_pVar[$k_pVar]['answers.item_id'] as $kk_pVar=>$vv_pVar) {
                            foreach($languages_pVar as $lang_pVar) {
                                if(!isset($data_pVar[$k_pVar]['answers.' . $lang_pVar . '_odpoved_media']) || !is_array($data_pVar[$k_pVar]['answers.' . $lang_pVar . '_odpoved_media'])) {
                                    $data_pVar[$k_pVar]['answers.' . $lang_pVar . '_odpoved_media'] = array();
                                }
                                if(!isset($data_pVar[$k_pVar]['answers.' . $lang_pVar . '_odpoved_media'][$kk_pVar]) || !is_array($data_pVar[$k_pVar]['answers.' . $lang_pVar . '_odpoved_media'][$kk_pVar])) {
                                    $data_pVar[$k_pVar]['answers.' . $lang_pVar . '_odpoved_media'][$kk_pVar] = array();
                                }
                                if($readLanguages_pVar && (!isset($data_pVar[$k_pVar]['answers.odpoved_media'][$kk_pVar]) || !is_array($data_pVar[$k_pVar]['answers.odpoved_media'][$kk_pVar]))) {
                                    $data_pVar[$k_pVar]['answers.odpoved_media'][$kk_pVar] = array();
                                }

                                if(isset($filesAnswers_pVar['items_test_answers_' . $lang_pVar . '_odpoved_media_' . $vv_pVar])) {
                                    $data_pVar[$k_pVar]['answers.' . $lang_pVar . '_odpoved_media'][$kk_pVar] =  array($filesAnswers_pVar['items_test_answers_' . $lang_pVar . '_odpoved_media_' . $vv_pVar]);
                                    if($readLanguages_pVar && $lang_pVar == $lng_pVar) {
                                        $data_pVar[$k_pVar]['answers.odpoved_media'][$kk_pVar][] =  $filesAnswers_pVar['items_test_answers_' . $lang_pVar . '_odpoved_media_' . $vv_pVar];
                                    }
                                }
                            }
                            if($readLanguages_pVar && !count($data_pVar[$k_pVar]['answers.odpoved_media'][$kk_pVar])) {
                                foreach($languages_pVar as $lang_pVar) {
                                    if(count($data_pVar[$k_pVar]['answers.' . $lang_pVar . '_odpoved_media'][$kk_pVar])) {
                                        $data_pVar[$k_pVar]['answers.odpoved_media'][$kk_pVar] = $data_pVar[$k_pVar]['answers.' . $lang_pVar . '_odpoved_media'][$kk_pVar];
                                    }
                                }
                            }
                        }
                    }
                }


                if($readLanguages_pVar) {
                    $lng_pVar = main_gClass::getLanguage_gFunc() . '_';
                    foreach ($v_pVar as $kk_pVar=>$vv_pVar) {
                        if(substr($kk_pVar, 0, 3) === $lng_pVar) {
                            $data_pVar[$k_pVar][substr($kk_pVar, 3)] = $vv_pVar;
                        }
                    }

                    $languages_pVar = array_reverse($info_pVar['languages']);
                    foreach ($files_pVar as $kk_pVar=>$vv_pVar) {
                        if(!isset($data_pVar[$k_pVar][$kk_pVar])
                            || !is_array($data_pVar[$k_pVar][$kk_pVar])
                            || !count($data_pVar[$k_pVar][$kk_pVar])) {
                            foreach ($languages_pVar as $language_pVar) {
                                if(isset($data_pVar[$k_pVar][$language_pVar . '_' . $kk_pVar])
                                    && is_array($data_pVar[$k_pVar][$language_pVar . '_' . $kk_pVar])
                                    && count($data_pVar[$k_pVar][$language_pVar . '_' . $kk_pVar])) {
                                    $data_pVar[$k_pVar][$kk_pVar] = $data_pVar[$k_pVar][$language_pVar . '_' . $kk_pVar];
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    static public function readDataFormatParameters_gFunc(&$filter_pVar)
    {
        if(isset($filter_pVar['data_format'])) {
            $data_format_pVar = array('data_format'=>$filter_pVar['data_format']);
            unset($filter_pVar['data_format']);
        }
        else {
            $data_format_pVar = array('data_format'=>'data');
        }

        switch($data_format_pVar['data_format'])
        {
            case 'data':
                break;
            case 'table':
                if(isset($filter_pVar['columns'])) {
                    $data_format_pVar['columns'] = $filter_pVar['columns'];
                    unset($filter_pVar['columns']);
                }
                break;
        }
        return($data_format_pVar);
    }

    static public function formatData_gFunc($data_pVar, $data_format_pVar)
    {
        switch($data_format_pVar['data_format'])
        {
            case 'data':
                break;
            case 'table':
                if(modules_gClass::isModuleRegistred_gFunc('tables')) {
                    $table_pVar = new table_gClass();
                    $table_pVar->setData_gFunc($data_pVar);
                    if(isset($data_format_pVar['columns'])) {
                        $table_pVar->setColumnsFromString_gFunc($data_format_pVar['columns']);
                    }
                    $data_pVar = $table_pVar->getTableData_gFunc();
                }
                break;
        }
        return($data_pVar);
    }

    static public function getFilterQuery_gFunc(&$filter_pVar, $systemName_pVar = false, $joinSystems_pVar = array())
    {
        // ziskam data podla filtra

        $where_str_pVar = '';
        $where_data_pVar = array();

        if(!is_array($filter_pVar)) {
            $filter_pVar = array();
        }

        if(isset($filter_pVar['_order_by'])) {
            $order_by_str_pVar = array();
            $order_by_pVar = explode(',', $filter_pVar['_order_by']);
            if($systemName_pVar !== false) {
                $adapter_pVar = items_gClass::getAdapter_gFunc($systemName_pVar);
                $fields_pVar = $adapter_pVar->getItemsFields_gFunc();
            }
            foreach ($order_by_pVar as $k_pVar=>$v_pVar) {
                if(strpos($v_pVar, '.') === false) {
                    if(empty($v_pVar)) {
                        unset($order_by_pVar[$k_pVar]);
                        continue;
                    }
                    $order_rule_pVar = explode('/', trim($v_pVar));
                    if(empty($order_rule_pVar[0])) {
                        unset($order_by_pVar[$k_pVar]);
                        continue;
                    }
                    if(!isset($order_rule_pVar[1])) {
                        $order_rule_pVar[1] = 'ASC';
                    }
                    if($order_rule_pVar[1] != 'ASC' && $order_rule_pVar[1] != 'DESC') {
                        $order_rule_pVar[1] = 'ASC';
                    }

                    if($systemName_pVar !== false) {
                        if(isset($fields_pVar[$order_rule_pVar[0]])) {
                            switch($fields_pVar[$order_rule_pVar[0]]['type']) {
                                case 'text':
                                case 'varchar':
                                    $order_rule_pVar[0] = main_gClass::getLanguage_gFunc() . '_' . $order_rule_pVar[0];
                                    break;
                                default;
                            }
                        }
                    }

                    $order_by_str_pVar[$k_pVar] = '`D`.`' . $order_rule_pVar[0] . '` ' . $order_rule_pVar[1];
                }
            }
            $order_by_data_pVar = implode(',', $order_by_str_pVar);
            if(!empty($order_by_data_pVar)) {
                $order_by_pVar = ' ORDER BY ' . $order_by_data_pVar;
            }
            else {
                $order_by_pVar = '';
            }
            unset($filter_pVar['_order_by']);
        }
        else {
            $order_by_pVar = '';
        }

        if(isset($filter_pVar['selected_items'])) {
            if($filter_pVar['selected_items'] === 'yes') {
                $items_pVar = array(0); // nulu vlozim, aby ak nie su oznacene ziadne polozky, tak aby filtru nevyhovovali ziadne polozky.
                $selected_items_pVar = main_gClass::getSessionData_gFunc('selected_items', array());
                if(isset($selected_items_pVar[$systemName_pVar]) && count($selected_items_pVar[$systemName_pVar])) {
                    $items_pVar = array_keys($selected_items_pVar[$systemName_pVar]);
                }
                // @TODO: zatial to dam natvrdo. Ak nahodou existuje vo filtri polozka item_id, tak bude prepisana.
                $filter_pVar['item_id'] = implode('|', $items_pVar);
            }
            unset($filter_pVar['selected_items']);
        }

        // rozlozsirenie filtra o jazykove mutacie
        $lngSettings_pVar = array();
        if($systemName_pVar !== false && !empty($systemName_pVar)) {
            $lngSettings_pVar[''] = $systemName_pVar;
        }
        if(count($joinSystems_pVar)) {
            foreach($joinSystems_pVar as $k_pVar=>$v_pVar) {
                $lngSettings_pVar[$v_pVar] = $k_pVar;
            }
        }

        foreach($lngSettings_pVar as $k_pVar=>$v_pVar) {
            $strlen_k_pVar = strlen($k_pVar);
            $adapter_pVar = items_gClass::getAdapter_gFunc($v_pVar);
            $fields_pVar = $adapter_pVar->getItemsFields_gFunc();
            if(is_array($fields_pVar)) {
                $info_pVar = $adapter_pVar->getInfo_gFunc();
                foreach($filter_pVar as $kk_pVar=>$vv_pVar) {
                    if(!empty($k_pVar)) {
                        if(substr($kk_pVar, 0, $strlen_k_pVar + 1) !== $k_pVar . '_') {
                            continue;
                        }
                        $key_pVar = substr($kk_pVar, $strlen_k_pVar + 1);
                    }
                    else {
                        $key_pVar = $kk_pVar;
                    }

                    if(isset($fields_pVar[$key_pVar])) {
                        if(in_array($fields_pVar[$key_pVar]['type'], array('varchar', 'text'))) {
                            $filter_pVar[$kk_pVar] = array('_operator'=>'OR');
                            foreach($info_pVar['languages'] as $language_pVar) {
                                if(!empty($k_pVar)) {
                                    $filter_pVar[$kk_pVar][$k_pVar . '_' . $language_pVar . '_' . $key_pVar] = $vv_pVar;
                                }
                                else {
                                    $filter_pVar[$kk_pVar][$language_pVar . '_' . $key_pVar] = $vv_pVar;
                                }
                            }
                        }
                    }
                }
            }
        }

        if(isset($filter_pVar['_operator']) && count($filter_pVar) == 1) {
            unset($filter_pVar['_operator']);
        }
        if(count($filter_pVar)) {
            $where_str_pVar .= self::expandConditionsToSql_gFunc($filter_pVar, $where_data_pVar);
        }

        if(!empty($where_str_pVar)) {
            $where_str_pVar = ' WHERE `D`.`status`<>\'deleted\'' . ' AND ' . $where_str_pVar;
        }
        else {
            $where_str_pVar = ' WHERE `D`.`status`<>\'deleted\'';
        }

        return(array($where_str_pVar, $where_data_pVar, $order_by_pVar));
    }

    static private function expandConditionsToSql_gFunc($conds_pVar, &$values_pVar, $defaultOperator_pVar = 'AND', $defaultOperator2_pVar = '=')
    {
        if(!is_array($conds_pVar)) {
            return($conds_pVar);
        }

        $ret_pVar = array();
        // _operator
        if(isset($conds_pVar['_operator'])) {
            $operator_pVar = $conds_pVar['_operator'];
        }
        else {
            $operator_pVar = $defaultOperator_pVar;
        }

        // _operator2
        if(isset($conds_pVar['__operator'])) {
            $operator2_pVar = $conds_pVar['__operator'];
        }
        else {
            $operator2_pVar = $defaultOperator2_pVar;
        }
        unset($conds_pVar['_operator']);
        unset($conds_pVar['__operator']);

        foreach ($conds_pVar as $k_pVar=>$v_pVar) {
            if(strlen($k_pVar) && substr($k_pVar, 0, 1) === '_') {
                continue;
            }
            if(is_numeric($k_pVar) && is_array($v_pVar)) {
                $ret_pVar[] = self::expandConditionsToSql_gFunc($v_pVar, $values_pVar);
            }
            elseif(is_array($v_pVar)) {
                $ret_pVar[] = self::expandConditionsToSql_gFunc($v_pVar, $values_pVar);
            }
            else {
                if(isset($conds_pVar['_key'])) {
                    $useKey_pVar = $conds_pVar['_key'];
                }
                else {
                    $useKey_pVar = $k_pVar;
                }

                if(strpos($useKey_pVar, '.') === false) {
                    $useKey_pVar = '`D`.`' . $k_pVar . '`';
                }
                else {
                    $useKey_pVar = explode('.', $useKey_pVar);
                    $useKey_pVar = '`' . implode('`.`', $useKey_pVar) . '`';
                }

                if(strpos($v_pVar, '|') !== false) {
                    if(strpos($v_pVar, 'LIKE(') !== false) {
                        $v_pVar = explode('|', $v_pVar);
                        $o_pVar = array();
                        foreach($v_pVar as $vv_pVar) {
                            if(substr($vv_pVar, 0, 5) == 'LIKE(') {
                                $vv_pVar = substr(substr($vv_pVar, 5), 0, -1);
                                if($operator2_pVar == '<>') {
                                    $o_pVar[] = $useKey_pVar . ' NOT LIKE %s';
                                }
                                else {
                                    $o_pVar[] = $useKey_pVar . ' LIKE %s';
                                }
                                $values_pVar[] = $vv_pVar;
                            }
                            else {
                                $o_pVar[] = $useKey_pVar . ' ' . $operator2_pVar . ' %s';
                                $values_pVar[] = $vv_pVar;
                            }
                        }
                        $ret_pVar[] = '(' . implode(' OR ', $o_pVar) . ')';
                    }
                    else {
                        if($operator2_pVar == '=') {
                            $ret_pVar[] = $useKey_pVar . ' IN (%r)';
                        }
                        elseif ($operator2_pVar == '<>') {
                            $ret_pVar[] = $useKey_pVar . ' NOT IN (%r)';
                        }
                        else {
                            $ret_pVar[] = $useKey_pVar . ' ' . $operator2_pVar . ' %s';
                        }
                        $v_pVar = explode('|', $v_pVar);
                        $v_pVar = '\'' . implode('\',\'', $v_pVar) . '\'';
                        $values_pVar[] = $v_pVar;
                    }
                }
                else {
                    if(substr($v_pVar, 0, 5) == 'LIKE(') {
                        $v_pVar = substr(substr($v_pVar, 5), 0, -1);
                        if($operator2_pVar == '<>') {
                            $ret_pVar[] = $useKey_pVar . ' NOT LIKE %s';
                        }
                        else {
                            $ret_pVar[] = $useKey_pVar . ' LIKE %s';
                        }
                        $values_pVar[] = $v_pVar;
                    }
                    else {
                        $ret_pVar[] = $useKey_pVar . ' ' . $operator2_pVar . ' %s';
                        $values_pVar[] = $v_pVar;
                    }
                }
            }
        }

        if(count($ret_pVar)) {
            $ret_pVar = ' (' . implode(' ' . $operator_pVar . ' ', $ret_pVar) . ') ';
            return($ret_pVar);
        }
        else {
            return($ret_pVar);
        }
    }

    static private function _addHiddenField_gFunc(&$struct_pVar, $fieldName_pVar, $fieldValue_pVar)
    {
        $struct_pVar[$fieldName_pVar]['type'] = 'hidden';
        $struct_pVar[$fieldName_pVar]['tag'] = $fieldName_pVar;
        $struct_pVar[$fieldName_pVar]['name'] = $fieldName_pVar;
        $struct_pVar[$fieldName_pVar]['value'] = $fieldValue_pVar;
    }

    static private function _isValue_gFunc($values_pVar, $value_pVar)
    {
        foreach ($values_pVar as $k_pVar=>$v_pVar) {
            if($v_pVar['enum_field_value'] === $value_pVar) {
                return($v_pVar['enum_id']);
            }
        }
        return(false);
    }


    static public function saveOrUpdateItemValue_gFunc($systemName_pVar, $data_pVar)
    {
        if(!isset($data_pVar['enum_field_id']) && isset($data_pVar['enum_field_tag'])) {
            $fields_pVar = db_items_gClass::getItemsFields_gFunc($systemName_pVar);
            if(isset($fields_pVar[$data_pVar['enum_field_tag']])) {
                $data_pVar['enum_field_id'] = $fields_pVar[$data_pVar['enum_field_tag']]['field_id'];
            }
            unset($data_pVar['enum_field_tag']);
        }

        $info_pVar = self::getInfo_gFunc($systemName_pVar);

        $tree_info_pVar = false;

        if(isset($data_pVar['_standard_tree_data'])) {
            $tree_info_pVar = $data_pVar['_standard_tree_data'];
            unset($data_pVar['_standard_tree_data']);
        }

        if(isset($data_pVar['enum_id'])) {
            $enum_id = $data_pVar['enum_id'];
            unset($data_pVar['enum_id']);
            unset($data_pVar['enum_field_value']);

            $changeId_pVar = self::traceNewChange_gFunc($systemName_pVar, 'updatevalue', 0, $enum_id);
            $oldValues_pVar = self::getResult_gFunc('SELECT * FROM `%titems_' . $systemName_pVar . '__values` WHERE `enum_id` = %d', __FILE__, __LINE__, array($enum_id));
            $i_pVar = 0;
            $fields_pVar = array();
            $fields_format_pVar = array();
            $fields_data_pVar = array();
            foreach ($data_pVar as $k_pVar=>$v_pVar) {
                $kk_pVar = $k_pVar;
                $prefix_pVar = '';
                if(strlen($k_pVar)>=2 && substr($k_pVar, 2, 1) === '_') {
                    $prefix_pVar = substr($k_pVar, 0, 2);
                    $kk_pVar = substr($k_pVar, 3);
                    $found_pVar = false;
                    foreach ($info_pVar['languages'] as $language_pVar) {
                        if($prefix_pVar !== $language_pVar) {
                            continue;
                        }
                        $found_pVar = true;
                        break;
                    }
                    if(!$found_pVar) {
                        $prefix_pVar = '';
                        $kk_pVar = $k_pVar;
                    }
                }

                self::traceChange_gFunc($systemName_pVar, $changeId_pVar, $kk_pVar, $oldValues_pVar[$k_pVar], $v_pVar, $prefix_pVar);
                $fields_pVar[$i_pVar] = '`' . $k_pVar . '`';
                $fields_data_pVar[$i_pVar] = $v_pVar;
                if($k_pVar == 'enum_id' || $k_pVar == 'enum_field_id' || $k_pVar == 'enum_value_order') {
                    $fields_format_pVar[$i_pVar] = '%d';
                }
                else {
                    $fields_format_pVar[$i_pVar] = '%s';
                }
                $i_pVar++;
            }

            $sql_pVar = 'UPDATE `%titems_'.$systemName_pVar.'__values` SET ';
            $n_pVar = 0;
            foreach ($fields_pVar as $k_pVar=>$v_pVar) {
                if($n_pVar) {
                    $sql_pVar .= ',';
                }
                $sql_pVar .= $v_pVar . '=' . $fields_format_pVar[$k_pVar];
                $n_pVar++;
            }
            $sql_pVar .= ' WHERE `enum_id`=%d';
            $fields_data_pVar[] = $enum_id;

            self::execute_gFunc($sql_pVar, __FILE__, __LINE__, $fields_data_pVar);
        }
        else {
            $changeId_pVar = self::traceNewChange_gFunc($systemName_pVar, 'insertvalue');

            $fields_pVar = array();
            $fields_format_pVar = array();
            $fields_data_pVar = array();
            foreach ($data_pVar as $k_pVar=>$v_pVar) {
                $kk_pVar = $k_pVar;
                $prefix_pVar = '';
                if(strlen($k_pVar)>=2 && substr($k_pVar, 2, 1) === '_') {
                    $prefix_pVar = substr($k_pVar, 0, 2);
                    $kk_pVar = substr($k_pVar, 3);
                    $found_pVar = false;
                    foreach ($info_pVar['languages'] as $language_pVar) {
                        if($prefix_pVar !== $language_pVar) {
                            continue;
                        }
                        $found_pVar = true;
                        break;
                    }
                    if(!$found_pVar) {
                        $prefix_pVar = '';
                        $kk_pVar = $k_pVar;
                    }
                }

                self::traceChange_gFunc($systemName_pVar, $changeId_pVar, $kk_pVar, '', $v_pVar, $prefix_pVar);

                $fields_pVar[] = '`' . $k_pVar . '`';
                $fields_data_pVar[] = $v_pVar;
                if($k_pVar == 'enum_id' || $k_pVar == 'enum_field_id' || $k_pVar == 'enum_value_order') {
                    $fields_format_pVar[] = '%d';
                }
                else {
                    $fields_format_pVar[] = '%s';
                }
            }

            $sql_pVar = 'INSERT INTO `%titems_'.$systemName_pVar.'__values` (';
            $sql_pVar .= implode(',', $fields_pVar);
            $sql_pVar .= ') VALUES(';
            $sql_pVar .= implode(',', $fields_format_pVar);
            $sql_pVar .= ')';
            $enum_id = self::insert_gFunc($sql_pVar, __FILE__, __LINE__, $fields_data_pVar, true);
            self::traceSetItemId_gFunc($systemName_pVar, $changeId_pVar, 0, $enum_id);
        }

        if(is_array($tree_info_pVar) && (count($tree_info_pVar['parent']) || count($tree_info_pVar['grandparent']))) {
            /////// TODO: stromovu struktutu zatial nelogujem do logu zmien, treba to spravit!!

            // array('tree_name'=>$this->tree_name_pVar, 'parent' => array(), 'grandparent' => array());
            $sql_pVar = 'SELECT `tree_id` FROM `%titems_'.$systemName_pVar.'__tree__defs` WHERE `tree_name` = \''.$tree_info_pVar['tree_name'].'\'';
            $result_pVar = self::getResult_gFunc($sql_pVar, __FILE__, __LINE__);
            if(is_array($result_pVar)) {
                $tree_id_pVar = $result_pVar['tree_id'];
                if($tree_id_pVar) {
                    $sql_pVar = 'DELETE FROM `%titems_'.$systemName_pVar.'__tree__values` WHERE `tree_id` = %d AND `enum_id` = %d';
                    self::execute_gFunc($sql_pVar, __FILE__, __LINE__, array($tree_id_pVar, $enum_id));
                    $sql_pVar = 'INSERT INTO `%titems_'.$systemName_pVar.'__tree__values` (`tree_id`, `enum_id`, `parent_value_id`, `grandparent_value_id`)
								 VALUES ';
                    $values_str_pVar = array();
                    $values_data_pVar = array();
                    foreach ($tree_info_pVar['parent'] as $v_pVar) {
                        foreach ($v_pVar as $vv_pVar) {
                            $values_str_pVar[] = ' (%d, %d, %d, NULL)';
                            $values_data_pVar[] = $tree_id_pVar;
                            $values_data_pVar[] = $enum_id;
                            $values_data_pVar[] = $vv_pVar;
                        }
                    }
                    foreach ($tree_info_pVar['grandparent'] as $v_pVar) {
                        foreach ($v_pVar as $vv_pVar) {
                            $values_str_pVar[] = ' (%d, %d, NULL, %d)';
                            $values_data_pVar[] = $tree_id_pVar;
                            $values_data_pVar[] = $enum_id;
                            $values_data_pVar[] = $vv_pVar;
                        }
                    }
                    $sql_pVar .= implode(',', $values_str_pVar);

                    self::execute_gFunc($sql_pVar, __FILE__, __LINE__, $values_data_pVar);
                }
            }
        }
    }

    /**
     * Aktualizuje alebo vytvori novy item.
     * Ak je nastavene $itemData_pVar['item_id'], spravi aktualizaciu.
     *
     * @param unknown_type $itemData_pVar
     */
    static public function saveOrUpdateItem_gFunc($systemName_pVar, &$itemData_pVar, $setUpdateTime_pVar = true)
    {
        $retValue_pVar = false;
        $info_pVar = self::getInfo_gFunc($systemName_pVar);
        $rights_pVar = self::getRights_gFunc($systemName_pVar);

        $deletedFiles_pVar = array();
        if(isset($itemData_pVar['te_deleted_files'])) {
            $deletedFiles_pVar = $itemData_pVar['te_deleted_files'];
            unset($itemData_pVar['te_deleted_files']);
        }

        $files_pVar = array();
        if(isset($itemData_pVar['te_uploaded_files'])) {
            $files_pVar = $itemData_pVar['te_uploaded_files'];
            unset($itemData_pVar['te_uploaded_files']);
        }

        if(isset($itemData_pVar['item_id']) && $itemData_pVar['item_id']) {
            // update item
            if($setUpdateTime_pVar) {
                $itemData_pVar['update_time'] = date('Y-m-d H:i:s');
            }

            // selctnem stare data
            $oldValues_pVar = db_items_gClass::getItem_gFunc($systemName_pVar, $itemData_pVar['item_id']);

            if(isset($itemData_pVar['email']) &&
                (!isset($oldValues_pVar['email']) || $itemData_pVar['email'] != $oldValues_pVar['email'])
            ) {
                $itemData_pVar['email_check_status'] = 'changed';
            }

            // overim prava, ci moze updatnut na taketo hodnoty
            if(isset($itemData_pVar['status']) && $itemData_pVar['status'] === 'deleted') {
                if(!self::checkRights_gFunc($systemName_pVar, 'delete',  $oldValues_pVar)) {
                    return($itemData_pVar['item_id']);
                }
                $itemData_pVar = array('status'=>'deleted', 'item_id'=>$itemData_pVar['item_id']);
            }
            else {
                if(!self::checkRights_gFunc($systemName_pVar, 'update',  array_merge($oldValues_pVar, $itemData_pVar))) {
                    return($itemData_pVar['item_id']);
                }
                // overim prava, ci moze updatovat z takychto hodnot
                if(!self::checkRights_gFunc($systemName_pVar, 'update_from', $oldValues_pVar)) {
                    return($itemData_pVar['item_id']);
                }
            }

            // overim prava, ci moze updatovat vsetky fieldy
            // a aby ich mohol updatovat, musi mat pravo na update, ale aj na get (aby nahodou nedoslo k nechcenemu zmazaniu dat)
            foreach($itemData_pVar as $k_pVar=>$v_pVar) {
                if(!self::checkRights_gFunc($systemName_pVar, 'get', $itemData_pVar, $k_pVar)) {
                    return($itemData_pVar['item_id']);
                }
            }
            foreach($itemData_pVar as $k_pVar=>$v_pVar) {
                if(!self::checkRights_gFunc($systemName_pVar, 'update', $itemData_pVar, $k_pVar)) {
                    return($itemData_pVar['item_id']);
                }
            }

            $changeId_pVar = self::traceNewChange_gFunc($systemName_pVar, 'update', $itemData_pVar['item_id']);

            $fields_pVar = self::getItemsFields_gFunc($systemName_pVar);
            $sql_pVar = "UPDATE `%titems_".$systemName_pVar."__data` SET ";
            $sql_values_pVar = array();
            $sql_parts_pVar = array();
            foreach ($itemData_pVar as $k_pVar=>$v_pVar) {
                $kk_pVar = $k_pVar;
                $prefix_pVar = '';
                if(!isset($fields_pVar[$k_pVar])) {
                    if(!isset($k_pVar[2]) || $k_pVar[2] !== '_') {
                        continue; // nie je prefixovany, a neexistuje...
                    }
                    $prefix_pVar = substr($k_pVar, 0, 2);
                    $kk_pVar = substr($k_pVar, 3);
                    $found_pVar = false;
                    foreach ($info_pVar['languages'] as $language_pVar) {
                        if($prefix_pVar !== $language_pVar) {
                            continue;
                        }
                        if(isset($fields_pVar[$kk_pVar]) && ($fields_pVar[$kk_pVar]['type'] === 'varchar' || $fields_pVar[$kk_pVar]['type'] === 'text')) {
                            $found_pVar = true;
                            break;
                        }
                    }
                    if(!$found_pVar) {
                        continue; // field nebol najdeny
                    }
                }

                if($fields_pVar[$kk_pVar]['type'] === 'password' && empty($v_pVar)) {
                    continue;
                }

                if(in_array($fields_pVar[$kk_pVar]['type'], array('filelist', 'imagelist', 'xfilelist', 'ximagelist'))) {
                    continue;
                }

                $oldValue_pVar = '';
                $newValue_pVar = $v_pVar;
                if(isset($oldValues_pVar[$k_pVar])) {
                    $oldValue_pVar = $oldValues_pVar[$k_pVar];
                    if($fields_pVar[$kk_pVar]['type'] === 'enum' && $oldValue_pVar === null) {
                        $oldValue_pVar = '';
                    }
                    elseif($fields_pVar[$kk_pVar]['type'] === 'date' && (
                            $oldValue_pVar === null
                            || $oldValue_pVar === '0000-00-00')
                    ) {
                        $oldValue_pVar = '';
                    }
                }
                if($fields_pVar[$kk_pVar]['type'] === 'enum' && $newValue_pVar === '-1') {
                    $newValue_pVar = '';
                    $v_pVar = null;
                }
                self::traceChange_gFunc($systemName_pVar, $changeId_pVar, $kk_pVar, $oldValue_pVar, $newValue_pVar, $prefix_pVar);

                $sql_values_pVar[] = $v_pVar;
                switch ($fields_pVar[$kk_pVar]['type']) {
                    case 'int':
                        $sql_parts_pVar[] = '`'.$k_pVar.'`=%d';
                        break;
                    case 'float':
                        $sql_parts_pVar[] = '`'.$k_pVar.'`=%f';
                        break;
                    case 'enum':
                        $sql_parts_pVar[] = '`'.$k_pVar.'`=%xs';
                        break;
                    default:
                        $sql_parts_pVar[] = '`'.$k_pVar.'`=%s';
                        break;
                }
            }

            if(!count($sql_parts_pVar)) {
                return(true);
            }

            $sql_pVar .= implode(', ', $sql_parts_pVar);
            $sql_pVar .= " WHERE `item_id`=%d";
            $sql_values_pVar[] = $itemData_pVar['item_id'];
            $ret_pVar = self::execute_gFunc($sql_pVar, __FILE__, __LINE__, $sql_values_pVar);

            if($info_pVar['tree'] === 'yes') {
                self::cacheDataToTree_gFunc($systemName_pVar, 'categories');
            }
            if($ret_pVar === false) {
                return(false);
            }
            $retValue_pVar = $itemData_pVar['item_id'];

            // ak menim rolu pouzivatela, este zalogujem do specialneho logu
            if($systemName_pVar == 'users'
                && isset($itemData_pVar['user_role'])
                && $oldValues_pVar['user_role'] !== $itemData_pVar['user_role']) {
                $data_pVar = array(
                    'user_id'=>session_gClass::getUserDetail_gFunc('user_id'),
                    'record_time'=>'now()',
                    'record_type1'=>'user',
                    'record_type2'=>'role_add',
                    'record_data'=>$itemData_pVar['item_id'] . '|' . $itemData_pVar['user_role'] . '|' . $oldValues_pVar['user_role']
                );
                db_public_gClass::insertData_gFunc('%taccess__log', '%d,%r,%s,%s,%s', $data_pVar, __FILE__, __LINE__);
            }

        }
        else {
            // add item
            // overim prava
            if(!self::checkRights_gFunc($systemName_pVar, 'insert', $itemData_pVar)) {
                return(false);
            }

            // overim prava, ci moze insertovat vsetky fieldy
            foreach($itemData_pVar as $k_pVar=>$v_pVar) {
                if(!self::checkRights_gFunc($systemName_pVar, 'insert', $itemData_pVar, $k_pVar)) {
                    return($itemData_pVar['item_id']);
                }
            }

            $itemData_pVar['insert_time'] = date('Y-m-d H:i:s');
            $itemData_pVar['update_time'] = date('Y-m-d H:i:s');
            $itemData_pVar['owner_id'] = session_gClass::getUserDetail_gFunc('user_id');
            $changeId_pVar = self::traceNewChange_gFunc($systemName_pVar, 'insert', false);
            $fields_pVar = self::getItemsFields_gFunc($systemName_pVar);
            $sql_pVar = "INSERT INTO `%titems_".$systemName_pVar."__data` ";
            $sql_values_pVar = array();
            $sql_parts_pVar = array();
            $sql_fields_pVar = array();
            foreach ($itemData_pVar as $k_pVar=>$v_pVar) {
                $kk_pVar = $k_pVar;
                $prefix_pVar = '';
                if(!isset($fields_pVar[$k_pVar])) {
                    if(!isset($k_pVar[2]) || $k_pVar[2] !== '_') {
                        continue; // nie je prefixovany
                    }
                    $prefix_pVar = substr($k_pVar, 0, 2);
                    $kk_pVar = substr($k_pVar, 3);
                    $found_pVar = false;
                    foreach ($info_pVar['languages'] as $language_pVar) {
                        if($prefix_pVar !== $language_pVar) {
                            continue;
                        }
                        if(isset($fields_pVar[$kk_pVar]) && ($fields_pVar[$kk_pVar]['type'] === 'varchar' || $fields_pVar[$kk_pVar]['type'] === 'text')) {
                            $found_pVar = true;
                            break;
                        }
                    }
                    if(!$found_pVar) {
                        continue; // field nebol najdeny
                    }
                }

                $newValue_pVar = $v_pVar;
                if($fields_pVar[$kk_pVar]['type'] === 'enum' && $newValue_pVar === '-1') {
                    $newValue_pVar = '';
                    $v_pVar = null;
                }
                self::traceChange_gFunc($systemName_pVar, $changeId_pVar, $kk_pVar, '', $newValue_pVar, $prefix_pVar);
                $sql_values_pVar[] = $v_pVar;
                $sql_fields_pVar[] = '`' . $k_pVar . '`';
                switch ($fields_pVar[$kk_pVar]['type']) {
                    case 'int':
                        $sql_parts_pVar[] = '%d';
                        break;
                    case 'float':
                        $sql_parts_pVar[] = '%f';
                        break;
                    case 'enum':
                        $sql_parts_pVar[] = '%xs';
                        break;
                    default:
                        $sql_parts_pVar[] = '%s';
                        break;
                }
            }

            if(!count($sql_parts_pVar)) {
                return(true);
            }

            $sql_pVar .= ' (' . implode(', ', $sql_fields_pVar) . ') VALUES (';
            $sql_pVar .= implode(', ', $sql_parts_pVar);
            $sql_pVar .= ")";
            $inserted_id = self::insert_gFunc($sql_pVar, __FILE__, __LINE__, $sql_values_pVar, true);

            self::traceSetItemId_gFunc($systemName_pVar, $changeId_pVar, $inserted_id, $inserted_id);
            if(is_array($files_pVar) && count($files_pVar)) {
                $tmpFiles_pVar = implode(',', $files_pVar);
                $sql_pVar = 'UPDATE `%tfiles` SET `ref_value` = %d WHERE `file_id` IN (' . $tmpFiles_pVar . ')';
                self::execute_gFunc($sql_pVar, __FILE__, __LINE__, array($inserted_id));
            }

            if($info_pVar['tree'] === 'yes') {
                self::cacheDataToTree_gFunc($systemName_pVar, 'categories');
            }
            $retValue_pVar = $inserted_id;

            // ak nastavujem rolu pouzivatela, este zalogujem do specialneho logu
            if($systemName_pVar == 'users'
                && isset($itemData_pVar['user_role'])) {
                $data_pVar = array(
                    'user_id'=>session_gClass::getUserDetail_gFunc('user_id'),
                    'record_time'=>'now()',
                    'record_type1'=>'user',
                    'record_type2'=>'role_add',
                    'record_data'=>$inserted_id . '|' . $itemData_pVar['user_role']
                );
                db_public_gClass::insertData_gFunc('%taccess__log', '%d,%r,%s,%s,%s', $data_pVar, __FILE__, __LINE__);
            }
        }

        ///////// zaznamenanie zmien suborov (upload/delete)
        $itemFilesTmp_pVar = db_public_gClass::files_get_records_gFunc('items_' . $systemName_pVar . '_%', $retValue_pVar, true);
        // rozgrupujem subory podla ref_tagu
        $itemFiles_pVar = array();
        foreach ($itemFilesTmp_pVar as $itemFile_pVar) {
            if(!isset($itemFiles_pVar[$itemFile_pVar['ref_tag']])) {
                $itemFiles_pVar[$itemFile_pVar['ref_tag']] = array();
            }
            $itemFiles_pVar[$itemFile_pVar['ref_tag']][] = $itemFile_pVar;
        }

        // zaznamenanie zmien suborov (upload/delete)
        foreach ($itemFiles_pVar as $itemFilesRefTag_pVar=>$itemFilesRefTagData_pVar) {
            $fieldName_pVar = substr($itemFilesRefTag_pVar, strlen('items_' . $systemName_pVar . '_'));
            if(isset($fieldName_pVar[2]) && $fieldName_pVar[2] == '_') {
                $prefix_pVar = substr($fieldName_pVar, 0, 2);
                $fieldName_pVar = substr($fieldName_pVar, 3);
            }

            $uploaded_pVar = false;
            $deleted_pVar = false;
            $fieldsDone_pVar = array();
            foreach ($itemFilesRefTagData_pVar as $itemFile_pVar) {
                if(isset($fieldsDone_pVar[$itemFile_pVar['ref_tag']])) {
                    break;
                }
                if(array_search($itemFile_pVar['file_id'], $files_pVar) !== false) {
                    $uploaded_pVar = true;
                }
                if(array_search($itemFile_pVar['file_id'], $deletedFiles_pVar) !== false) {
                    $deleted_pVar = true;
                }

                if($uploaded_pVar || $deleted_pVar) {
                    // field bol modifikovany
                    $fieldsDone_pVar[$itemFile_pVar['ref_tag']] = true;

                    $oldFiles_pVar = $itemFilesRefTagData_pVar;
                    foreach ($oldFiles_pVar as $kFile_pVar=>$vFile_pVar) {
                        if(array_search($vFile_pVar['file_id'], $files_pVar) !== false) {
                            unset($oldFiles_pVar[$kFile_pVar]);
                        }
                    }
                    $newFiles_pVar = $itemFilesRefTagData_pVar;
                    foreach ($newFiles_pVar as $kFile_pVar=>$vFile_pVar) {
                        if(array_search($vFile_pVar['file_id'], $deletedFiles_pVar) !== false) {
                            unset($newFiles_pVar[$kFile_pVar]);
                        }
                    }

                    $oldValue_pVar = array();
                    $size_pVar = 0;
                    foreach ($oldFiles_pVar as $file_pVar) {
                        $name_pVar = $file_pVar['full_name'];
                        if(!empty($file_pVar['custom_name'])) {
                            $name_pVar = $file_pVar['custom_name'];
                            if($file_pVar['custom_name'] !== $file_pVar['full_name']) {
                                $name_pVar .= ' [' . $file_pVar['full_name'] . ']';
                            }
                        }
                        $size_pVar += $file_pVar['size'];
                        $oldValue_pVar[] = $name_pVar;
                    }
                    $oldValue_pVar = implode('; ', $oldValue_pVar);
                    $oldValue_pVar .= ' (TOTAL: ' . $size_pVar . ' bytes)';

                    $newValue_pVar = array();
                    $size_pVar = 0;
                    foreach ($newFiles_pVar as $file_pVar) {
                        $name_pVar = $file_pVar['full_name'];
                        if(!empty($file_pVar['custom_name'])) {
                            $name_pVar = $file_pVar['custom_name'];
                            if($file_pVar['custom_name'] !== $file_pVar['full_name']) {
                                $name_pVar .= ' [' . $file_pVar['full_name'] . ']';
                            }
                        }
                        $size_pVar += $file_pVar['size'];
                        $newValue_pVar[] = $name_pVar;
                    }
                    $newValue_pVar = implode('; ', $newValue_pVar);
                    $newValue_pVar .= ' (TOTAL: ' . $size_pVar . ' bytes)';

                    self::traceChange_gFunc($systemName_pVar, $changeId_pVar, $fieldName_pVar, $oldValue_pVar, $newValue_pVar, $prefix_pVar);
                }
            }

        }


        if(is_array($deletedFiles_pVar) && count($deletedFiles_pVar)) {
            foreach ($deletedFiles_pVar as $fileId_pVar) {
                $fileName_pVar = db_public_gClass::files_deleteFileRecord_gFunc($fileId_pVar, true, 'items_'.$systemName_pVar.'%', $retValue_pVar);
                if(!empty($fileName_pVar)) {
                    if(file_exists($fileName_pVar)) {
                        unlink($fileName_pVar);
                    }
                }
            }
        }

        // a este updatnem suborove polozky.. dam tam nazvy suborov, aby som mohol potom vyhladavat cez LIKE
        if($retValue_pVar) {
            $info_pVar = db_items_gClass::getInfo_gFunc($systemName_pVar);
            foreach($fields_pVar as $k_pVar=>$field_pVar) {
                if(in_array($field_pVar['type'], array('imagelist','filelist','ximagelist','xfilelist')) === false) {
                    continue;
                }
                if($field_pVar['type'][0] == 'x') {
                    $records_pVar = db_public_gClass::files_get_records_gFunc('items_' . $systemName_pVar . '_' . $field_pVar['tag'], $retValue_pVar, false);
                    $file_names_pVar = array();
                    foreach($records_pVar as $record_pVar) {
                        if(!empty($record_pVar['custom_name'])) {
                            $file_names_pVar[] = $record_pVar['custom_name'];
                        }
                        else {
                            $file_names_pVar[] = $record_pVar['full_name'];
                        }
                    }
                    $sql_pVar = 'UPDATE `%titems_' . $systemName_pVar . '__data` SET `' . $field_pVar['tag'] . '` = %s WHERE `item_id` = %d';
                    db_public_gClass::execute_gFunc($sql_pVar, __FILE__, __LINE__, array(NL . implode(NL, $file_names_pVar) . NL, $retValue_pVar));
                }
                else {
                    foreach($info_pVar['languages'] as $lng_pVar) {
                        $records_pVar = db_public_gClass::files_get_records_gFunc('items_' . $systemName_pVar . '_' . $lng_pVar . '_' . $field_pVar['tag'], $retValue_pVar, false);
                        $file_names_pVar = array();
                        foreach($records_pVar as $record_pVar) {
                            if(!empty($record_pVar['custom_name'])) {
                                $file_names_pVar[] = $record_pVar['custom_name'];
                            }
                            else {
                                $file_names_pVar[] = $record_pVar['full_name'];
                            }
                        }
                        $sql_pVar = 'UPDATE `%titems_' . $systemName_pVar . '__data` SET `' . $lng_pVar . '_' . $field_pVar['tag'] . '` = %s WHERE `item_id` = %d';
                        db_public_gClass::execute_gFunc($sql_pVar, __FILE__, __LINE__, array(NL . implode(NL, $file_names_pVar) . NL, $retValue_pVar));
                    }
                }
            }
        }

        return($retValue_pVar);
    }

    static public function traceNewChange_gFunc($systemName_pVar, $changeType_pVar, $itemId_pVar = 0, $db_id_pVar = 0)
    {
        if($db_id_pVar === 0) {
            if($changeType_pVar === 'insert' || $changeType_pVar === 'update') {
                $db_id_pVar = $itemId_pVar;
            }
        }
        $userId_pVar = session_gClass::getUserDetail_gFunc('user_id');

        $sql_pVar = 'INSERT INTO `%titems_' . $systemName_pVar . '__changes`
						(`user_id`, `change_datetime`, `item_id`, `db_id`,`change_type`)
						VALUES (%d, now(), %d, %d, %s)';
        $changeId_pVar = self::insert_gFunc($sql_pVar, __FILE__, __LINE__, array($userId_pVar, $itemId_pVar, $db_id_pVar, $changeType_pVar), true);
        return($changeId_pVar);
    }

    static private function traceChange_gFunc($systemName_pVar, $changeId_pVar, $fieldName_pVar, $oldValue_pVar, $newValue_pVar, $language_pVar = '')
    {
        if($oldValue_pVar == $newValue_pVar) {
            return(0);
        }
        $sql_pVar = "INSERT INTO `%titems_" . $systemName_pVar . "__change_details`
						(`change_id`, `field_name`, `old_value`, `new_value`, `value_language`)
						VALUES(%d, %s, %s, %s, %s)";
        $changeDetailId_pVar = self::insert_gFunc($sql_pVar, __FILE__, __LINE__, array($changeId_pVar, $fieldName_pVar, $oldValue_pVar, $newValue_pVar, $language_pVar), true);
        return($changeDetailId_pVar);
    }

    static private function traceSetItemId_gFunc($systemName_pVar, $changeId_pVar, $itemId_pVar, $db_id_pVar = false)
    {
        if($db_id_pVar === false) {
            $sql_pVar = 'UPDATE `%titems_' . $systemName_pVar . '__changes`
							SET `item_id` = %d WHERE `change_id` = %d';
            self::execute_gFunc($sql_pVar, __FILE__, __LINE__, array($itemId_pVar, $changeId_pVar));
        }
        else {
            $sql_pVar = 'UPDATE `%titems_' . $systemName_pVar . '__changes`
							SET `item_id` = %d, db_id = %d WHERE `change_id` = %d';
            self::execute_gFunc($sql_pVar, __FILE__, __LINE__, array($itemId_pVar, $db_id_pVar, $changeId_pVar));
        }
    }

    /**
     * ziska url pre kategoriu danu v $item_pVar
     * Cachuje vysledok.
     *
     * @param unknown_type $systemName_pVar
     * @param unknown_type $item_pVar
     * @return unknown
     */
    static private function _getUrlForCategory_gFunc($systemName_pVar, $item_pVar)
    {
        $fields_pVar = self::getItemsFieldsURL_gFunc($systemName_pVar);
        $cacheName_pVar = 'items_'.$systemName_pVar.'_url_cat_';
        foreach ($fields_pVar as $k_pVar=>$v_pVar) {
            $cacheName_pVar .= $v_pVar['tag'] . '_' . $item_pVar[$v_pVar['tag']];
        }
        $url_pVar = self::getCachedResult_gFunc($cacheName_pVar);
        if($url_pVar !== false) {
            return($url_pVar);
        }

        // musim selectnut url hodnoty re polozky
        $where_str_pVar = '';
        $where_data_pVar = array();
        foreach ($fields_pVar as $k_pVar=>$v_pVar) {
            if(!empty($where_str_pVar)) {
                $where_str_pVar .= ' OR ';
            }
            $where_str_pVar .= '(`enum_field_id`=%d AND `enum_field_value`=%s)';
            $where_data_pVar[] = $v_pVar['field_id'];
            $where_data_pVar[] = $item_pVar[$v_pVar['tag']];
        }

        $fieldValues_pVar = db_items_gClass::getEnumFieldsValues_gFunc($systemName_pVar, $where_str_pVar, $where_data_pVar ,'`enum_id`, `enum_field_id`, `enum_field_value`, `' . main_gClass::getLanguage_gFunc() . '_url_name`', 'enum_field_id');

        $url_pVar = array();
        foreach ($fields_pVar as $k_pVar=>$v_pVar) {
            $url_pVar[] = $fieldValues_pVar[intval($v_pVar['field_id'])][main_gClass::getLanguage_gFunc() . '_url_name'];
        }

        $url_pVar = '/' . implode('/', $url_pVar) . '/';

        self::cacheResult_gFunc($cacheName_pVar, $url_pVar);
        return($url_pVar);
    }


    /**
     * vypocita url pre kategoriu alebo item
     * $item_pVar je pole, v ktorom musia byt nastavene vsetky potrebne polozky pre ziskanie url.
     * 		teda vsetky polia co maju nastavene url_order, a ak chcem url na polozku, tak aj item_id (alebo rovno url) musi byt nastavene
     *
     * ak $category_url_pVar = true, vrati url pre kategoriu
     * ak $category_url_pVar = false, vrati url pre polozku
     *
     * @param unknown_type $systemName_pVar
     * @param unknown_type $item_pVar
     * @param unknown_type $category_url_pVar
     */
    static public function getUrlForItem_gFunc($systemName_pVar, $item_pVar, $category_url_pVar = false, $getItemDisabled_pVar = false)
    {
        $url_cat_pVar = self::_getUrlForCategory_gFunc($systemName_pVar, $item_pVar);
        if($category_url_pVar) {
            return($url_cat_pVar);
        }
        else {
            if(isset($item_pVar[main_gClass::getLanguage_gFunc() . '_url'])) {
                return($url_cat_pVar . $item_pVar[main_gClass::getLanguage_gFunc() . '_url'] . '/');
            }
            else {
                if(!$getItemDisabled_pVar && isset($item_pVar['item_id']) && $item_pVar['item_id']) {
                    $item_pVar = self::getItem_gFunc($systemName_pVar, $item_pVar['item_id']);
                }
                if(isset($item_pVar[main_gClass::getLanguage_gFunc() . '_url'])) {
                    return($url_cat_pVar . $item_pVar[main_gClass::getLanguage_gFunc() . '_url'] . '/');
                }
                else {
                    // nic sa neda robit, vratim iba url na kategoriu.
                    return($url_cat_pVar . '/');
                }
            }
        }
    }


    /**
     * selectnem strukturu formulara
     *
     * @param unknown_type $systemName_pVar
     * @param unknown_type $default_values_pVar
     * @return unknown
     */
    static public function getItemFormFields_gFunc($systemName_pVar, $indexByTag_pVar = false)
    {

        $sql_pVar = "SELECT * FROM `%titems_".$systemName_pVar."__fields` as `f`
						LEFT JOIN `%titems_".$systemName_pVar."__values` as `v`
						ON (`f`.`type`='enum' OR `f`.`type`='set')
							AND `f`.`field_id`=`v`.`enum_field_id`
						LEFT JOIN `%titems_".$systemName_pVar."__fieldsets` as `fs`
						ON `f`.`fieldset` = `fs`.`fieldset_id`
						ORDER BY `fs`.`fieldset_order`, `f`.`field_order`, `f`.`field_id`, `v`.`enum_value_order`";
        $fields_pVar = self::getResultArray_gFunc($sql_pVar, __FILE__, __LINE__, false, $indexByTag_pVar?'tag':false); /// toto nemoze byt indexovane podla TAG !!!
        //echo '<pre>'; print_r($fields_pVar); echo '</pre>';
        return($fields_pVar);
    }

    static public function getFormRules_gFunc($systemName_pVar, $formName_pVar, $cacheEnabled_pVar = true)
    {
        $rules_pVar = false;
        if($cacheEnabled_pVar) {
            $cacheName_pVar = 'form_rules_' . $systemName_pVar . '_' . $formName_pVar;
            $rules_pVar = self::getCachedResult_gFunc($cacheName_pVar);
            if($rules_pVar !== false) {
                return($rules_pVar);
            }
        }

        $sql_pVar = 'SELECT * FROM `%titems_' . $systemName_pVar . '__form_rules` as `fr`
						LEFT JOIN `%titems_' . $systemName_pVar . '__forms` as `f` ON `f`.`form_id` = `fr`.`form_id`
						WHERE `f`.`form_name`=%s';
        $rules_pVar = self::getResultArray_gFunc($sql_pVar, __FILE__, __LINE__, array($formName_pVar), 'field_name');
        self::cacheResult_gFunc($cacheName_pVar, $rules_pVar);
        return($rules_pVar);
    }

    static function isFormRule_gFunc($systemName_pVar, $formName_pVar, $fieldName_pVar, $fieldAccess_pVar)
    {
        $rules_pVar = self::getFormRules_gFunc($systemName_pVar, $formName_pVar);
        if(!isset($rules_pVar[$fieldName_pVar])) {
            return(false);
        }
        if($rules_pVar[$fieldName_pVar]['field_access'] == $fieldAccess_pVar) {
            return(true);
        }
        return(false);
    }

    /**
     * Prepocita indexy enum_value_order pre konkretny field
     * Ak nezadam field_tag, tak prepocita vsetky fieldy v systemName
     *
     * z 1,4,7,8,10,23,49 urobi 10,20,30,40,50,60,70
     * polozky s nastavenou hodnotou 0 (alebo nenastavenou) dava na koniec zoznamu
     *
     * @param unknown_type $systemName_pVar
     * @param unknown_type $field_tag_pVar
     */
    static public function reorderItemValues_gFunc($systemName_pVar, $field_tag_pVar = false)
    {
        $fields_pVar = self::getItemsFields_gFunc($systemName_pVar);

        if($field_tag_pVar !== false) {
            $fields_pVar = array($fields_pVar[$field_tag_pVar]);
        }

        foreach ($fields_pVar as $field_pVar) {
            if($field_pVar['type'] !== 'enum' && $field_pVar['type'] !== 'set') {
                continue;
            }
            $field_id_pVar =  $field_pVar['field_id'];

            $sql_pVar = 'SELECT `enum_id`, `enum_value_order` FROM `%titems_' . $systemName_pVar . '__values` WHERE `enum_field_id` = %d ORDER BY `enum_value_order`';
            $values_pVar = self::getResultArray_gFunc($sql_pVar, __FILE__, __LINE__, array($field_id_pVar));

            $i_pVar = 10;
            $ordered_values_pVar = array();
            foreach ($values_pVar as $value_pVar) {
                if(!$value_pVar['enum_value_order']) {
                    continue;
                }
                $ordered_values_pVar[$value_pVar['enum_id']] = $i_pVar;
                $i_pVar += 10;
            }
            foreach ($values_pVar as $value_pVar) {
                if($value_pVar['enum_value_order']) {
                    continue;
                }
                $ordered_values_pVar[$value_pVar['enum_id']] = $i_pVar;
                $i_pVar += 10;
            }

            // updatnem databazu
            foreach ($ordered_values_pVar as $k_pVar=>$v_pVar) {
                $sql_pVar = 'UPDATE `%titems_' . $systemName_pVar . '__values` SET `enum_value_order` = %d WHERE `enum_id` = %d';
                self::execute_gFunc($sql_pVar, __FILE__, __LINE__, array($v_pVar, $k_pVar));
            }
        }
    }

    static public function deleteItemValue_gFunc($systemName_pVar, $valueId_pVar)
    {
        $sql_pVar = 'DELETE FROM `%titems_'.$systemName_pVar.'__values` WHERE `enum_id` = %d';
        self::execute_gFunc($sql_pVar, __FILE__, __LINE__, array($valueId_pVar));
        self::traceNewChange_gFunc($systemName_pVar, 'deletevalue', 0, $valueId_pVar);
    }

    /**
     * Prepocita hodnoty v stlpci fieldName.
     * Prva hodnota bude od step, kazda nasledujuca o step vyssia
     * Hodnoty ktore su null, zaradi na koniec.
     *
     * @param unknown_type $systemName_pVar
     * @param unknown_type $fieldName_pVar
     * @param unknown_type $step_pVar
     */
    static public function reorderItems_gFunc($systemName_pVar, $fieldName_pVar, $step_pVar = 10, $whereStr_pVar = '')
    {

        $sql_pVar = 'SELECT `item_id`, `'.$fieldName_pVar.'` FROM `%titems_' . $systemName_pVar . '__data`';
        if(!empty($whereStr_pVar)) {
            $sql_pVar .= ' WHERE ' . $whereStr_pVar;
            $values_pVar = self::getResultArray_gFunc($sql_pVar, __FILE__, __LINE__);

            $i_pVar = $step_pVar;
            $ordered_values_pVar = array();
            foreach ($values_pVar as $value_pVar) {
                if(is_null($value_pVar[$fieldName_pVar])) {
                    continue;
                }
                $ordered_values_pVar[$value_pVar['item_id']] = $i_pVar;
                $i_pVar += $step_pVar;
            }
            foreach ($values_pVar as $value_pVar) {
                if(!is_null($value_pVar[$fieldName_pVar])) {
                    continue;
                }
                $ordered_values_pVar[$value_pVar['item_id']] = $i_pVar;
                $i_pVar += $step_pVar;
            }

            // updatnem databazu
            foreach ($ordered_values_pVar as $k_pVar=>$v_pVar) {
                $sql_pVar = 'UPDATE `%titems_' . $systemName_pVar . '__data` SET `'.$fieldName_pVar.'` = %d WHERE `item_id` = %d';
                self::execute_gFunc($sql_pVar, __FILE__, __LINE__, array($v_pVar, $k_pVar));
            }
        }
    }

    static public function getItemComments_gFunc($systemName_pVar, $itemId_pVar, $creator_id_pVar, $owner_id_pVar, $order_by_pVar = false)
    {
        $sql_pVar = 'SELECT `c`.*,`u`.`login`, `u`.`nick`, concat_ws(\'\', `c`.`comment_datetime`, \'<span class="hidden">\', UNIX_TIMESTAMP(`c`.`comment_datetime`), \'</span>\') as `comment_datetime_html` FROM `%titems_' . $systemName_pVar . '__comments` as `c`
						LEFT JOIN `%titems_users__data` as `u` ON `u`.`item_id` = `c`.`user_id`
						LEFT JOIN `%titems_test_questions__data` as `q` ON `c`.`item_id` = `q`.`item_id`
						WHERE
							' . ( $itemId_pVar ? ' `c`.`item_id` = %d AND ' : '') . '
							' . ( $creator_id_pVar ? ' `c`.`user_id` = %d AND ' : '') . '
							' . ( $owner_id_pVar ? ' (`q`.`owner_id` = %d OR `q`.`garant_id` = %d) AND ' : '') . '
						`c`.`comment_state` = \'enabled\'
						AND `q`.`status` <> \'deleted\'
						ORDER BY ';
        if(empty($order_by_pVar)) {
            $sql_pVar .= '`c`.`comment_datetime` DESC';
        }
        else {
            $order_by_pVar = explode('/', $order_by_pVar);
            if($order_by_pVar[1] != 'DESC') {
                $order_by_pVar[1] = 'ASC';
            }
            $sql_pVar .= '`'.$order_by_pVar[0].'` '. $order_by_pVar[1];
        }
        $sql_params_pVar = array();
        if($itemId_pVar) {
            $sql_params_pVar[] = $itemId_pVar;
        }
        if($creator_id_pVar) {
            $sql_params_pVar[] = $creator_id_pVar;
        }
        if($owner_id_pVar) {
            $sql_params_pVar[] = $owner_id_pVar;
            $sql_params_pVar[] = $owner_id_pVar;
        }
        return(self::getResultArray_gFunc($sql_pVar, __FILE__, __LINE__, $sql_params_pVar));
    }

    static public function getItemComment_gFunc($systemName_pVar, $commentId_pVar)
    {
        $sql_pVar = 'SELECT `c`.*,`u`.`login`, `u`.`nick` FROM `%titems_' . $systemName_pVar . '__comments` as `c`
						LEFT JOIN `%titems_users__data` as `u` ON `u`.`item_id` = `c`.`user_id`
						WHERE `c`.`comment_id` = %d';
        $ret_pVar = self::getResult_gFunc($sql_pVar, __FILE__, __LINE__, $commentId_pVar);
        return($ret_pVar);
    }

    static public function addItemComment_gFunc($systemName_pVar, $itemId_pVar, $commentText_pVar)
    {
        $sql_pVar = 'INSERT INTO `%titems_' . $systemName_pVar . '__comments`
							(`user_id`, `item_id`, `comment_state`, `comment_access`, `comment_datetime`, `comment_language`, `comment_text`)
							VALUES (%d, %d, \'enabled\', \'public\', now(), \'sk\', %s)';
        $user_id_pVar = session_gClass::getUserDetail_gFunc('user_id');
        $commentId_pVar = self::insert_gFunc($sql_pVar, __FILE__, __LINE__, array($user_id_pVar, $itemId_pVar, $commentText_pVar), true);

        $changeId_pVar = self::traceNewChange_gFunc($systemName_pVar, 'insertcomment', $itemId_pVar, $commentId_pVar);
        self::traceChange_gFunc($systemName_pVar, $changeId_pVar, 'comments.comment_access', '', 'public', '');
        self::traceChange_gFunc($systemName_pVar, $changeId_pVar, 'comments.comment_text', '', $commentText_pVar, '');
    }

    static public function deleteItemComment_gFunc($systemName_pVar, $commentId_pVar)
    {
        $comment_pVar = self::getItemComment_gFunc($systemName_pVar, $commentId_pVar);

        if(is_array($comment_pVar)) {
            $sql_pVar = 'UPDATE `%titems_' . $systemName_pVar . '__comments` SET `comment_state` = \'deleted\'
								WHERE `comment_id` = %d';
            self::execute_gFunc($sql_pVar, __FILE__, __LINE__, $commentId_pVar);

            $changeId_pVar = self::traceNewChange_gFunc($systemName_pVar, 'deletecomment', $comment_pVar['item_id'], $commentId_pVar);
            self::traceChange_gFunc($systemName_pVar, $changeId_pVar, 'comments.user_id', $comment_pVar['user_id'], '', '');
            self::traceChange_gFunc($systemName_pVar, $changeId_pVar, 'comments.comment_text', $comment_pVar['comment_text'], '', '');
        }
    }

    static public function getLog_pVar($systemName_pVar, $ids_pVar = false, $sort_index_pVar = '0')
    {
        if(!count($ids_pVar)) {
            return(array());
        }
        $sql_pVar = 'SELECT CONCAT(`change`.`change_datetime`, \'_' . $sort_index_pVar . '_\', `change`.`change_id`) as `sort`, `change`.*, `details`.*, CONCAT_WS(`u`.`last_name`, \' \', `u`.`first_name`) as username FROM `%titems_' . $systemName_pVar . '__changes` as `change`
						LEFT JOIN `%titems_' . $systemName_pVar . '__change_details` as `details` ON `change`.`change_id` = `details`.`change_id`
						LEFT JOIN `%titems_users__data` as `u` ON u.item_id = change.user_id';
        if(is_array($ids_pVar) && count($ids_pVar)) {
            $sql_pVar .= ' WHERE `change`.`item_id` IN ('.implode(',', $ids_pVar).') ';
        }
        $sql_pVar .= ' ORDER BY `sort`';
        $ret_pVar = self::getResultArray_gFunc($sql_pVar, __FILE__, __LINE__, array(), 'sort', 1);
        foreach($ret_pVar as $k_pVar=>$v_pVar) {
            foreach($v_pVar['change.change_id'] as $kk_pVar=>$vv_pVar) {
                $rights_filter_pVar = array('item_id'=>$ret_pVar[$k_pVar]['change.item_id'][$kk_pVar]);
                if(!self::checkRights_gFunc($systemName_pVar, 'get', $rights_filter_pVar, $ret_pVar[$k_pVar]['details.field_name'][$kk_pVar])) {
                    $ret_pVar[$k_pVar]['details.old_value'][$kk_pVar] = '??';
                    $ret_pVar[$k_pVar]['details.new_value'][$kk_pVar] = '??';
                }
            }
        }
        return($ret_pVar);
    }

    static public function getRawLogData_gFunc($systemName_pVar)
    {
        $sql_pVar = 'SELECT * FROM `%titems_' . $systemName_pVar . '__changes` ORDER BY `change_id`';
        $data_pVar = self::getResultArray_gFunc($sql_pVar, __FILE__, __LINE__);
        return($data_pVar);
    }

    static public function getRawLogDetails_gFunc($systemName_pVar)
    {
        $sql_pVar = 'SELECT * FROM `%titems_' . $systemName_pVar . '__change_details` ORDER BY `change_detail_id`';
        $data_pVar = self::getResultArray_gFunc($sql_pVar, __FILE__, __LINE__);
        return($data_pVar);
    }

    static public function getRawComments_gFunc($systemName_pVar)
    {
        $sql_pVar = 'SELECT * FROM `%titems_' . $systemName_pVar . '__comments`';
        $data_pVar = self::getResultArray_gFunc($sql_pVar, __FILE__, __LINE__, false, 'comment_id');
        return($data_pVar);
    }

    static public function getTreeDef_gFunc($systemName_pVar, $treeName_pVar, $cacheEnabled_pVar = true)
    {
        $cacheName_pVar = 'items_'.$systemName_pVar.'_treedef';
        if($cacheEnabled_pVar === true) {
            $tree_pVar = self::getCachedResult_gFunc($cacheName_pVar);
            if($tree_pVar !== false && isset($tree_pVar[$treeName_pVar])) {
                return($tree_pVar[$treeName_pVar]);
            }
        }


        $sql_pVar = 'SELECT * FROM `%titems_' . $systemName_pVar . '__tree__defs`';
        $tree_pVar = self::getResultArray_gFunc($sql_pVar, __FILE__, __LINE__, false, 'tree_name');

        self::cacheResult_gFunc($cacheName_pVar, $tree_pVar);
        if(isset($tree_pVar[$treeName_pVar])) {
            return($tree_pVar[$treeName_pVar]);
        }
        return(false);
    }

    static public function getRawTree_gFunc($systemName_pVar, $tree_id_pVar, $cacheEnabled_pVar = true)
    {
        $cacheName_pVar = 'items_'.$systemName_pVar.'_tree_rawdata_'.$tree_id_pVar;
        if($cacheEnabled_pVar === true) {
            $tree_pVar = self::getCachedResult_gFunc($cacheName_pVar);
            if($tree_pVar !== false) {
                return($tree_pVar);
            }
        }
        $sql_pVar = 'SELECT * FROM `%titems_' . $systemName_pVar . '__tree__values` WHERE `tree_id` = %d';
        $tree_pVar = self::getResultArray_gFunc($sql_pVar, __FILE__, __LINE__, $tree_id_pVar);

        self::cacheResult_gFunc($cacheName_pVar, $tree_pVar);
        return($tree_pVar);
    }

    static public function getTree_gFunc($systemName_pVar, $treeName_pVar, $cacheEnabled_pVar = true)
    {
        $cacheName_pVar = 'items_'.$systemName_pVar.'_tree_'.$treeName_pVar;
        if($cacheEnabled_pVar === true) {
            $tree_pVar = self::getCachedResult_gFunc($cacheName_pVar);
            if($tree_pVar !== false) {
                return($tree_pVar);
            }
        }

        $tree_pVar = array();

        $sql_pVar = 'SELECT * FROM `%titems_' . $systemName_pVar . '__tree__defs` WHERE `tree_name` = %s';
        $tree_def_pVar = self::getResultArray_gFunc($sql_pVar, __FILE__, __LINE__, $treeName_pVar);
        if(!count($tree_def_pVar)) {
            return(false);
        }

        $fields_pVar = explode(',', $tree_def_pVar[0]['tree_def']);
        if(count($fields_pVar)) {
            $sql_pVar = 'SELECT `field_id`,`tag` FROM `%titems_' . $systemName_pVar . '__fields` WHERE `tag` IN (%as)';
            $tmp_fields_pVar = self::getResultArray_gFunc($sql_pVar, __FILE__, __LINE__, array($fields_pVar), 'tag');
            $tmp2_fields_pVar = $fields_pVar;
            $fields_pVar = array();
            foreach ($tmp2_fields_pVar as $v_pVar) {
                $fields_pVar[$v_pVar] = $tmp_fields_pVar[$v_pVar]['field_id'];
            }
        }

        $tree_pVar['_defs'] = $fields_pVar;

        // strom
        $sql_pVar = 'SELECT `f`.`tag` as `field_tag`, `f`.`field_id`, `tv`.*, `v`.`enum_field_value`, `vparent`.`enum_field_value`, `vgrandparent`.`enum_field_value`, `vgrandparent`.`enum_field_id`
						FROM `%titems_' . $systemName_pVar . '__tree__values` as `tv`
						LEFT JOIN `%titems_' . $systemName_pVar . '__values` as `v` ON `v`.`enum_id` = `tv`.`enum_id`
						LEFT JOIN `%titems_' . $systemName_pVar . '__fields` as `f` ON `f`.`field_id` = `v`.`enum_field_id`
						LEFT JOIN `%titems_' . $systemName_pVar . '__values` as `vparent` ON `vparent`.`enum_id` = `tv`.`parent_value_id`
						LEFT JOIN `%titems_' . $systemName_pVar . '__values` as `vgrandparent` ON `vgrandparent`.`enum_id` = `tv`.`grandparent_value_id`
						WHERE `tv`.`tree_id` = %d AND `f`.`field_id` IS NOT NULL';  // field_id is not null - ak zmazaem enum, a v strome zostane jeho hodnota, tak ju ignoruje (inak dava errory)
        $rules_pVar = self::getResultArray_gFunc($sql_pVar, __FILE__, __LINE__, $tree_def_pVar[0]['tree_id'], 'field_tag', 1);

        $tree_pVar['_values'] = array();

        $tmp_values = self::getEnumFieldsValues_gFunc($systemName_pVar, '`enum_field_id` IN (%ad)', array($fields_pVar), '*');
        foreach ($fields_pVar as $v_pVar) {
            $tree_pVar['_values'][$v_pVar] = array();
            foreach ($tmp_values as $vv_pVar) {
                if($vv_pVar['enum_field_id'] == $v_pVar) {
                    $tree_pVar['_values'][$v_pVar][] = $vv_pVar;
                }
            }
        }

        foreach ($tree_pVar['_values'] as $k_pVar=>$v_pVar) {
            $key_pVar = array_search($k_pVar, $fields_pVar);
            if($key_pVar === false) {
                continue;
            }
            foreach ($v_pVar as $kk_pVar=>$vv_pVar) {
                $tree_pVar['_values'][$k_pVar][$kk_pVar]['rules'] = array();
                if(!isset($rules_pVar[$key_pVar])) {
                    continue;
                }
                if(array_search($vv_pVar['enum_id'], $rules_pVar[$key_pVar]['tv.enum_id']) === false) {
                    continue;
                }
                $parents_pVar = array();
                $grandParents_pVar = array();
                foreach ($rules_pVar[$key_pVar]['tv.enum_id'] as $kkk_pVar=>$vvv_pVar) {
                    if($vvv_pVar !== $vv_pVar['enum_id']) {
                        continue;
                    }

                    if($rules_pVar[$key_pVar]['tv.parent_value_id'][$kkk_pVar]) {
                        $parents_pVar[] = $rules_pVar[$key_pVar]['tv.parent_value_id'][$kkk_pVar];
                    }
                    if($rules_pVar[$key_pVar]['tv.grandparent_value_id'][$kkk_pVar]) {
                        if(!isset($grandParents_pVar[$rules_pVar[$key_pVar]['vgrandparent.enum_field_id'][$kkk_pVar]])) {
                            $grandParents_pVar[$rules_pVar[$key_pVar]['vgrandparent.enum_field_id'][$kkk_pVar]] = array();
                        }
                        $grandParents_pVar[$rules_pVar[$key_pVar]['vgrandparent.enum_field_id'][$kkk_pVar]][] = $rules_pVar[$key_pVar]['tv.grandparent_value_id'][$kkk_pVar];
                    }
                }

                if(count($parents_pVar) || count($grandParents_pVar)) {
                    $tree_pVar['_values'][$k_pVar][$kk_pVar]['rules'] = array('parents'=>$parents_pVar, 'grandparents'=>$grandParents_pVar);
                }

            }
        }

        $tree_pVar['_rules'] = $rules_pVar;

        //echo '<pre>'; print_r($tree_pVar); echo '</pre>';exit;


        $tree_pVar['_js'] = '';
        $tree_pVar['_js'] .= $systemName_pVar . '_' . $treeName_pVar . '_selector = new Object();' . NL;
        $tree_pVar['_js_selName'] = $systemName_pVar . '_' . $treeName_pVar . '_selector';
        $parentName_pVar = false;
        foreach ($tree_pVar['_defs'] as $k_pVar=>$v_pVar) {
            $tree_pVar['_js'] .= $tree_pVar['_js_selName'] . '[\''.$k_pVar.'\'] = new Object();' . NL;

            $i_pVar = 0;
            foreach ($tree_pVar['_values'][$v_pVar] as $vv_pVar) {
                $tree_pVar['_js'] .= $tree_pVar['_js_selName'] . '[\''.$k_pVar.'\']['.$i_pVar.'] = new Object();' .NL;
                $tree_pVar['_js'] .= $tree_pVar['_js_selName'] . '[\''.$k_pVar.'\']['.$i_pVar.'].label = \''.$vv_pVar[main_gClass::getLanguage_gFunc().'_enum_field_name_item'].'\';' . NL;
                $tree_pVar['_js'] .= $tree_pVar['_js_selName'] . '[\''.$k_pVar.'\']['.$i_pVar.'].value = \''.$vv_pVar['enum_field_value'].'\';' . NL;
                $tree_pVar['_js'] .= $tree_pVar['_js_selName'] . '[\''.$k_pVar.'\']['.$i_pVar.'].rules = new Object();' .NL;
                if(isset($tree_pVar['_rules'][$k_pVar])) {
                    $tmp_pVar = array();
                    if($parentName_pVar !== false) {
                        foreach ($tree_pVar['_rules'][$k_pVar]['v.enum_field_value'] as $kkk_pVar=>$vvv_pVar) {
                            if(empty($tree_pVar['_rules'][$k_pVar]['vparent.enum_field_value'][$kkk_pVar])) {
                                continue;
                            }

                            if($vv_pVar['enum_id'] != $tree_pVar['_rules'][$k_pVar]['tv.enum_id'][$kkk_pVar]) {
                                continue;
                            }

                            if(!isset($tmp_pVar[$parentName_pVar])) {
                                $tree_pVar['_js'] .= $tree_pVar['_js_selName'] . '[\''.$k_pVar.'\']['.$i_pVar.'].rules.'.$parentName_pVar.' = new Array();' . NL;
                                $tmp_pVar[$parentName_pVar] = true;
                            }
                            $tree_pVar['_js'] .= $tree_pVar['_js_selName'] . '[\''.$k_pVar.'\']['.$i_pVar.'].rules.'.$parentName_pVar.'.push(\''. $tree_pVar['_rules'][$k_pVar]['vparent.enum_field_value'][$kkk_pVar] .'\');' . NL;
                        }
                    }

                    foreach ($tree_pVar['_rules'][$k_pVar]['v.enum_field_value'] as $kkk_pVar=>$vvv_pVar) {
                        if(empty($tree_pVar['_rules'][$k_pVar]['vgrandparent.enum_field_value'][$kkk_pVar])) {
                            continue;
                        }

                        if($vv_pVar['enum_id'] != $tree_pVar['_rules'][$k_pVar]['tv.enum_id'][$kkk_pVar]) {
                            continue;
                        }

                        // tu zistim, do ktoreho zelectboxu patri hodnota, a nastavim to do $grandParentName_pVar
                        $grandParentName_pVar = '';
                        $grandparentId_pVar = $tree_pVar['_rules'][$k_pVar]['tv.grandparent_value_id'][$kkk_pVar];
                        foreach ($tree_pVar['_values'] as $tk_pVar => $tv_pVar) {
                            foreach ($tv_pVar as $tkk_pVar => $tvv_pVar) {
                                if($tvv_pVar['enum_id'] == $grandparentId_pVar) {
                                    $grandParentName_pVar = array_search($tk_pVar, $tree_pVar['_defs']);
                                    break 2;
                                }
                            }
                        }

                        if(!isset($tmp_pVar[$grandParentName_pVar])) {
                            $tree_pVar['_js'] .= $tree_pVar['_js_selName'] . '[\''.$k_pVar.'\']['.$i_pVar.'].rules.'.$grandParentName_pVar.' = new Array();' . NL;
                        }
                        $tmp_pVar[$grandParentName_pVar] = true;
                        $tree_pVar['_js'] .= $tree_pVar['_js_selName'] . '[\''.$k_pVar.'\']['.$i_pVar.'].rules.'.$grandParentName_pVar.'.push(\''. $tree_pVar['_rules'][$k_pVar]['vgrandparent.enum_field_value'][$kkk_pVar] .'\');' . NL;
                    }
                }
                $i_pVar++;
            }

            $parentName_pVar = $k_pVar;
        }

        $tree_pVar['_struct'] = array();
        foreach ($tree_pVar['_defs'] as $k_pVar=>$v_pVar) {
            self::_treeAddChilds_gFunc($tree_pVar, $tree_pVar['_struct'], $v_pVar);
        }
        $tree_pVar['_tree'] = $treeName_pVar;
        unset($tree_pVar['_js_selName']);

        self::cacheResult_gFunc($cacheName_pVar, $tree_pVar);
        return($tree_pVar);
    }

    static private function _treeAddChilds_gFunc(&$data_pVar, &$target_pVar, $defKey_pVar, $parent_pVar = 0, $grandParents_pVar = array())
    {
        if(!isset($target_pVar['childs'])) {
            $target_pVar['childs'] = array();
            $target_pVar['childs_level'] = $defKey_pVar;
            $target_pVar['childs_level_name'] = array_search($defKey_pVar, $data_pVar['_defs']);
        }
        if($target_pVar['childs_level'] === $defKey_pVar) {
            $key_pVar = array_search($defKey_pVar, $data_pVar['_defs']); // ziskam slovnu reprezentaciu (key_pVar = modul, etc.)

            foreach ($data_pVar['_values'][$defKey_pVar] as $k_pVar=>$v_pVar) { // prechadzam vsetky existujuce polozky v danom leveli (v_pVar je polozka z tabulky values = pole (enum_id, enum_field_id,...))
                $status_pVar = true; // polozka je defaultne ON

                if(!isset($v_pVar['rules']['parents']) || !count($v_pVar['rules']['parents'])) {
                    if(!isset($v_pVar['rules']['grandparents']) || !count($v_pVar['rules']['grandparents'])) {
                        // nie je nastaveny rodic, ani prarodicia
                        $status_pVar = true;
                    }
                    else {
                        // nie je nastaveny rodic, ale su nastaveni prarodicia
                        $status_pVar = true;
                        foreach ($v_pVar['rules']['grandparents'] as $gp) {
                            foreach ($grandParents_pVar as $vvv_pVar) {
                                if(array_search($vvv_pVar, $gp) !== false) {
                                    continue 2;
                                }
                            }
                            $status_pVar = false;
                        }
                    }
                }
                else {
                    if(!isset($v_pVar['rules']['grandparents']) || !count($v_pVar['rules']['grandparents'])) {
                        // je nastaveny rodic, ale prarodicia nie su najdeni
                        $status_pVar = false;
                        if(array_search($parent_pVar, $v_pVar['rules']['parents']) !== false) {
                            $status_pVar = true;
                        }
                    }
                    else {
                        // je nastaveny rodic, aj prarodicia
                        $status_pVar = false;
                        if(array_search($parent_pVar, $v_pVar['rules']['parents']) !== false) {
                            $status_pVar = true;
                        }
                        if($status_pVar) {
                            foreach ($v_pVar['rules']['grandparents'] as $gp) {
                                foreach ($grandParents_pVar as $vvv_pVar) {
                                    if(array_search($vvv_pVar, $gp) !== false) {
                                        continue 2;
                                    }
                                }
                                $status_pVar = false;
                            }
                        }
                    }
                }

                if($status_pVar) {
                    $target_pVar['childs'][] = $v_pVar;
                }
            }
        }
        else {
            foreach ($target_pVar['childs'] as $kChild_pVar=>$child_pVar) {
                $newGrandParents_pVar = $grandParents_pVar;
                $newGrandParents_pVar[] = $parent_pVar;
                $newParent_pVar = $child_pVar['enum_id'];
                self::_treeAddChilds_gFunc($data_pVar, $target_pVar['childs'][$kChild_pVar], $defKey_pVar, $newParent_pVar, $newGrandParents_pVar);
            }
        }
    }

    static public function getTreeForField_gFunc($systemName_pVar, $fieldName_pVar)
    {
        $sql_pVar = 'SELECT * FROM `%titems_' . $systemName_pVar . '__tree__defs` WHERE `tree_def` LIKE %s';
        $tree_def_pVar = self::getResultArray_gFunc($sql_pVar, __FILE__, __LINE__, '%' . $fieldName_pVar . '%');
        if(!count($tree_def_pVar)) {
            return(false);
        }

        return(self::getTree_gFunc($systemName_pVar, $tree_def_pVar[0]['tree_name']));
    }

    /*
    static public function getTree_gFunc($systemName_pVar, $treeName_pVar, $cacheEnabled_pVar = true)
    {

        $selName_pVar= $systemName_pVar . '_' . $treeName_pVar . '_selector';
        $js_pVar = $selName_pVar . ' = new Object();' . NL;
        foreach ($tree_pVar['_defs'] as $k_pVar=>$v_pVar) {
            $js_pVar .= $selName_pVar . '[\''.$k_pVar.'\'] = new Object();' . NL;
            $js_pVar .= $selName_pVar . '[\''.$k_pVar.'\'].data = new Object();' . NL;
            $js_pVar .= $selName_pVar . '[\''.$k_pVar.'\'].rules = new Object();' . NL;
            // nastavim data
            $i_pVar = 0;
            foreach ($tree_pVar['_values'][$v_pVar] as $vv_pVar) {
                $js_pVar .= $selName_pVar . '[\''.$k_pVar.'\'].data['.$i_pVar.'] = new Object();';
                $js_pVar .= $selName_pVar . '[\''.$k_pVar.'\'].data['.$i_pVar.'].label = \''.$vv_pVar['sk_enum_field_name_item'].'\';' . NL;
                $js_pVar .= $selName_pVar . '[\''.$k_pVar.'\'].data['.$i_pVar.'].value = \''.$vv_pVar['enum_field_value'].'\';' . NL;
                $i_pVar++;
            }
            // nastavm rules
            if(isset($tree_pVar[$k_pVar])) {
                $tmp_pVar = array();
                foreach ($tree_pVar[$k_pVar]['v.enum_field_value'] as $kkk_pVar=>$vvv_pVar) {
                    if(empty($tree_pVar[$k_pVar]['vparent.enum_field_value'][$kkk_pVar])) {
                        continue;
                    }
                    if(!isset($tmp_pVar[$vvv_pVar])) {
                        $js_pVar .= $selName_pVar . '[\''.$k_pVar.'\'].rules.'.$vvv_pVar.' = new Array();' . NL;
                    }
                    $tmp_pVar[$vvv_pVar] = true;
                    $js_pVar .= $selName_pVar . '[\''.$k_pVar.'\'].rules.'.$vvv_pVar.'.push(\''. $tree_pVar[$k_pVar]['vparent.enum_field_value'][$kkk_pVar] .'\');';
                }
                foreach ($tree_pVar[$k_pVar]['v.enum_field_value'] as $kkk_pVar=>$vvv_pVar) {
                    if(empty($tree_pVar[$k_pVar]['vgrandparent.enum_field_value'][$kkk_pVar])) {
                        continue;
                    }
                    if(!isset($tmp_pVar[$vvv_pVar])) {
                        $js_pVar .= $selName_pVar . '[\''.$k_pVar.'\'].rules.'.$vvv_pVar.' = new Array();' . NL;
                    }
                    $tmp_pVar[$vvv_pVar] = true;
                    $js_pVar .= $selName_pVar . '[\''.$k_pVar.'\'].rules.'.$vvv_pVar.'.push(\''. $tree_pVar[$k_pVar]['vgrandparent.enum_field_value'][$kkk_pVar] .'\');';
                }
            }
        }

        $tree_pVar['_js'] = $js_pVar;
        $tree_pVar['_tree'] = $treeName_pVar;

        $tree_pVar['_struct'] = array();
        foreach ($tree_pVar['_defs'] as $k_pVar=>$v_pVar) {
            self::_treeAddChilds_gFunc($tree_pVar, $tree_pVar['_struct'], $v_pVar);
        }

        self::cacheResult_gFunc($cacheName_pVar, $tree_pVar);

        echo '<pre>'; print_r($tree_pVar); echo '</pre>';
        return($tree_pVar);
    }

    static private function _treeAddChilds_gFunc(&$data_pVar, &$target_pVar, $defKey_pVar, $parent_pVar = 0, $grandParents_pVar = array())
    {
        if(!isset($target_pVar['childs'])) {
            $target_pVar['childs'] = array();
            $target_pVar['childs_level'] = $defKey_pVar;
        }
        if($target_pVar['childs_level'] === $defKey_pVar) {
            $key_pVar = array_search($defKey_pVar, $data_pVar['_defs']); // ziskam slovnu reprezentaciu (key_pVar = modul, etc.)
            foreach ($data_pVar['_values'][$defKey_pVar] as $k_pVar=>$v_pVar) { // prechadzam vsetky existujuce polozky v danom leveli (v_pVar je polozka z tabulky values = pole (enum_id, enum_field_id,...))
                $status_pVar = true; // polozka je defaultne ON
                if(!empty($key_pVar) && isset($data_pVar[$key_pVar])) { // ak existuju pravidla
                    $x_pVar = array_search($v_pVar['enum_id'], $data_pVar[$key_pVar]['tv.enum_id']);
                    if($x_pVar !== false) { // existuje previdlo pre polozku
                        $tmp_parents_pVar = array();
                        $tmp_grandparents_pVar = array();
                        foreach ($data_pVar[$key_pVar]['tv.enum_id'] as $kk_pVar=>$vv_pVar) {
                            //
                            // prechadzam vsetky pravidla, a ignorujem ktore nepatria pre tuto polozku
                            // naplnim si polia $tmp_parents_pVar a $tmp_grandparents_pVar
                            //
                            if($vv_pVar != $v_pVar['enum_id']) {
                                continue;
                            }
                            if(!empty($data_pVar[$key_pVar]['tv.parent_value_id'][$kk_pVar])) {
                                $tmp_parents_pVar[] = $data_pVar[$key_pVar]['tv.parent_value_id'][$kk_pVar];
                            }
                            if(!empty($data_pVar[$key_pVar]['tv.grandparent_value_id'][$kk_pVar])) {
                                $tmp_grandparents_pVar[] = $data_pVar[$key_pVar]['tv.grandparent_value_id'][$kk_pVar];
                            }
                        }

                        if(!count($tmp_parents_pVar)) {
                            if(!count($tmp_grandparents_pVar)) {
                                $status_pVar = true; // je true.. ale toto asi nikdy nenastane, lebo zabere predosla podmienka, a potom sa sem nedostanem...ale aj tak bude nastavene true
                            }
                            else {
                                // rodic nie je nastaveny, ale su nastaveni prarodicia
                                $status_pVar = false;
                                foreach ($grandParents_pVar as $vvv_pVar) {
                                    if(array_search($vvv_pVar, $tmp_grandparents_pVar) !== false) {
                                        $status_pVar = true;
                                        break;
                                    }
                                }
                            }
                        }
                        else {
                            if(!count($tmp_grandparents_pVar)) {
                                // je nastaveny rodic, a prarodicia nie su nastaveni
                                $status_pVar = false;
                                if(array_search($parent_pVar, $tmp_parents_pVar) !== false) {
                                    $status_pVar = true;
                                }
                            }
                            else {
                                // je nastaveny rodic aj prarodicia...
                                $status_pVar = false;
                                if(array_search($parent_pVar, $tmp_parents_pVar) !== false) {
                                    $status_pVar = true;
                                }
                                if($status_pVar) {
                                    $status_pVar = false;
                                    foreach ($grandParents_pVar as $vvv_pVar) {
                                        if(array_search($vvv_pVar, $tmp_grandparents_pVar) !== false) {
                                            $status_pVar = true;
                                            break;
                                        }
                                    }
                                }
                            }
                        }
                    }
                }

                if($status_pVar) {
                    $target_pVar['childs'][] = $v_pVar;
                }
            }
        }
        else {
            foreach ($target_pVar['childs'] as $kChild_pVar=>$child_pVar) {
                $newGrandParents_pVar = $grandParents_pVar;
                $newGrandParents_pVar[] = $parent_pVar;
                $newParent_pVar = $child_pVar['enum_id'];
                self::_treeAddChilds_gFunc($data_pVar, $target_pVar['childs'][$kChild_pVar], $defKey_pVar, $newParent_pVar, $newGrandParents_pVar);
            }
        }
    }

    */

    static public function getRawData_gFunc($systemName_pVar, $applyJoins_pVar = true)
    {
        $sql_pVar = 'SELECT * FROM `%titems_'.$systemName_pVar.'__data` as `D` ';
        if($applyJoins_pVar) {
            $fields_pVar = db_items_gClass::getItemFormFields_gFunc($systemName_pVar);
            foreach ($fields_pVar as $field_pVar) {
                if($field_pVar['type'] == 'join') {
                    $tmp_pVar = explode('|', $field_pVar['comment']);
                    $sql_pVar .= $tmp_pVar[1];
                }
            }
        }
        $data_pVar = self::getResultArray_gFunc($sql_pVar, __FILE__, __LINE__, false, 'item_id', 1);

        return($data_pVar);
    }


}
