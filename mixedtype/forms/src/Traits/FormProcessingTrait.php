<?php

namespace Mixedtype\Forms\Traits;

use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;

trait FormProcessingTrait
{
    private string $submitId;

    private string $submitMessage;








    function getValidatedData($key = null)
    {
        if($key !== null) {
            return $this->validatedData[$key] ?? null;
        }
        return $this->validatedData;
    }

    private function validatedProcessGroups($validated) : array
    {
        if(isset($validated['_form_groups'])) {
            foreach($validated['_form_groups'] as $groupName) {
                $validated[$groupName] = [];
                foreach ($validated as $key => $value) {
                    if(strpos($key, $groupName . '_') === 0) {
                        $itemKey = explode('___', substr($key, strlen($groupName . '_')));
                        if(count($itemKey) !== 2) {
                            continue;
                        }
                        if($itemKey[1] === 'XXX__') {
                            unset($validated[$key]);
                            continue;
                        }
                        $itemIndex = (int)trim($itemKey[1], '_') - 1;
                        $itemKey = $itemKey[0];
                        if($itemIndex < 0) {
                            continue;
                        }

                        if(!isset($validated[$groupName][$itemIndex])) {
                            $validated[$groupName][$itemIndex] = [];
                        }

                        $validated[$groupName][$itemIndex][$itemKey] = $value;
                        unset($validated[$key]);
                    }
                }
                if(count($validated[$groupName]) !== (int)$validated[$groupName . '_count']) {
                    throw new \Exception('Invalid count of items in group: ' . $groupName);
                }
                unset($validated[$groupName . '_count']);
            }

            unset($validated['_form_groups']);
        }
        return $validated;
    }




    function afterProcessSubmit() : RedirectResponse
    {
        return back()->with('submited-message', !empty($this->submitMessage)?$this->submitMessage:'Formulár bol spracovaný.');
    }





    function processSubmit(array $validated):void
    {
        if(isset($validated['item_id']) && $validated['item_id']) {

        }

        dump($validated);
        throw new \Exception('No processSubmit method defined.');
    }






    function getFieldValue($fieldName, $defaultValue = null)
    {
        if(isset($this->uploadedFiles[$fieldName])) {
            return $this->uploadedFiles[$fieldName];
        }

        $data = $this->getData();
        if(isset($data[$fieldName])) {
            if($fieldName === 'test_questions_sk_otazka') {
            }
            return $data[$fieldName];
        }
        return $defaultValue;
    }

    protected function getValueFromInput($key, $default = null, $index = null)
    {
        $request = request();

        if($request !== null) {
            $key .= '___' . $index . '__';
        }

        if($request->has($key)) {
            return $request->input($key);
        }
        return $default;
    }

    protected function setSubmitMessage($message)
    {
        $this->submitMessage = $message;
    }

}
