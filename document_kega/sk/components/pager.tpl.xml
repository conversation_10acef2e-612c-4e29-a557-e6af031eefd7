<?xml version="1.0" encoding="utf-8" ?>
<truEngine-document xmlns:te="http://www.truengine.sk">

<div te:print="no" class="pager" style="display:inline">
	<?php
	if(!isset(@{params})) {
		@{params} = '';
	}

	if(!isset(@{extra_params})) {
		@{extra_params} = '';
	}
	
	if(!isset(@{_order_by})) {
		@{_order_by} = '';
	}
	
	if(@{pager:totalPages} > 1) {
		if(@{pager:currentPage} <= 1) {
			echo '&lt;&lt;';
		}
		else {
			echo '<a href="' . main_gClass::makeUrl_gFunc('/@{doc}?page='.(@{pager:currentPage}-1)).'@{params}@{extra_params}">&lt;&lt;</a>';
		}
		echo ' ';
		for($i=0; $i < @{pager:totalPages}; $i++) {
			if(($i + 1) == @{pager:currentPage}) {
				echo $i+1;
			}
			else {
				echo '<a href="' . main_gClass::makeUrl_gFunc('/@{doc}?page='.($i+1)).'@{params}@{extra_params}">'.($i+1).'</a>';
			}
			echo ' ';
		}
		if(@{pager:currentPage} >= @{pager:totalPages}) {
			echo '&gt;&gt;';
		}
		else {
			echo '<a href="' . main_gClass::makeUrl_gFunc('/@{doc}?page='.(@{pager:currentPage}+1)).'@{params}@{extra_params}">&gt;&gt;</a>';
		}
	}
	
	?>
	
	<?php 
		@{current_order} = explode('/', @{_order_by});
		if(!isset(@{current_order:1})) { @{current_order:1} = 'ASC'; }
	@{display_type} = @{pager:totalItems} > 1 ? 'inline':'none';
	if(@{pager:totalItems} <= 0) {
		?>
			<script type="text/javascript">
				var fce = document.getElementById('filter_container_empty');
				if(fce) {
					fce.style.display='none';
				}
			</script>
			<?php
		}
	?>
	<te:if test="isset(@{order_fields})">
	 <?php 
		if(!isset($GLOBALS['pager_id'])) {
			$GLOBALS['pager_id'] = 0;
		}
		$GLOBALS['pager_id'] ++;
		@{ctag} = 'pager_id_' . $GLOBALS['pager_id']; ?>
		
			 <script type="text/javascript">
	 function sob_@{ctag}()
	 {

	 	var sel1 = document.getElementById('sort_sel1_@{ctag}');
	 	var sel2 = document.getElementById('sort_sel2_@{ctag}');
	 	
	 	var order_params = '';

	 	order_params = order_params +  $j('#sort_sel2_@{ctag}').val();
	 	order_params = order_params +  '/' + $j('#sort_sel1_@{ctag}').val();

	 	$j('#filter_sort').val(order_params);

	 	var url = '<?php echo main_gClass::makeUrl_gFunc('/' . @{doc}); ?>';

		if(url.indexOf('?') >= 0) {
			url += '&amp;';
		}
		else {
			url += '?';
		}

		url += 'zoradit=' + order_params;

		if(filters['default'].filter_params) {
			url += '&amp;filter=' + escape(filters['default'].filter_params);
		}
		
		url += '@{extra_params}';

		document.location = url;
	 }
	 </script>
	 <span>
	 @L{Radiť} <select onchange="sob_@{ctag}();" id="sort_sel1_@{ctag}">
	 			<option te:if="@{current_order:1} == 'ASC'" value="ASC" selected="selected">@L{vzostupne}</option>
	 			<option te:if="@{current_order:1} == 'DESC'" value="DESC" selected="selected">@L{zostupne}</option>
	 			<option te:if="@{current_order:1} != 'ASC'" value="ASC">@L{vzostupne}</option>
	 			<option te:if="@{current_order:1} != 'DESC'" value="DESC">@L{zostupne}</option>
	 	   </select>
	 @L{podľa} <select onchange="sob_@{ctag}();" id="sort_sel2_@{ctag}">
	 	<option></option>
	 	<te:for each="@{order_fields} as @{kfield}=>@{vfield}">
	 		<option te:if="@{current_order:0} == @{kfield}" value="@{kfield}" selected="selected">@{vfield}</option>
	 		<option te:if="@{current_order:0} != @{kfield}" value="@{kfield}">@{vfield}</option>
	 	</te:for>
	 </select>
	 </span>
	 

	 </te:if>
	 <te:if test="isset(@{select}) &amp;&amp; is_array(@{select})">
	 	@{select:html}
	</te:if>
	
	(@{pager:totalItems})
</div>
  
</truEngine-document>
