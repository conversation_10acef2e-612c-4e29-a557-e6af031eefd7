<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Mixedtype\Multisite\Multisite;

class SingleQuestion extends Model
{
    use HasFactory;
    protected $table = 'single_questions';

    public function answers()
    {
        return $this->hasMany(SingleQuestionAnswer::class, 'single_question_id', 'id');
    }

    // referencia na tabulku kega_items_test_questions__data
    public function question()
    {
        return $this->belongsTo(KegaItemsTestQuestionsData::class, 'question_id', 'item_id');
    }

    public function generateUid()
    {
        $rand = rand(10, 99);
        return $rand  . ($this->id * $rand * 3);
    }

    public function getQrCodeUrl()
    {
        return route(Multisite::getFullRouteName('single-questions.run'), [$this->uid]);
    }
}
