{% set crumbs = breadcrumbs.get() %}
{% set breadcrumbs_config = config.plugins.breadcrumbs %}
{% set divider = breadcrumbs_config.icon_divider_classes %}

{% if crumbs|length > 1 or breadcrumbs_config.show_all %}
<div id="breadcrumbs" itemscope itemtype="http://data-vocabulary.org/Breadcrumb">
    {% if breadcrumbs_config.icon_home %}
    <i class="{{ breadcrumbs_config.icon_home }}"></i>
    {% endif %}
    {% for crumb in crumbs %}
        {% if not loop.last %}
            {% if crumb.routable %}
                <a href="{{ crumb.url }}" itemprop="url"><span itemprop="title">{{ crumb.menu }}</span></a>
            {% else  %}
                <span itemprop="title">{{ crumb.menu }}</span>
            {% endif %}
            <i class="{{ divider }}"></i>
        {% else %}
            {% if breadcrumbs_config.link_trailing %}
                <a href="{{ crumb.url }}" itemprop="url"><span itemprop="title">{{ crumb.menu }}</span></a>
            {% else %}
                <span itemprop="title">{{ crumb.menu }}</span>
            {% endif %}
        {% endif %}
    {% endfor %}
</div>
{% endif %}
